version: '3.8'

# 🏗️ FULMARK ERP-CRM - Główny Docker Compose
# System transkrypcji NVIDIA NeMo + Gemma3-4b + Email Intelligence

services:
  # 🗄️ BAZY DANYCH
  
  # Redis - Cache i kolejki
  redis:
    image: redis:7-alpine
    container_name: fulmark-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - fulmark-network

  # MongoDB - Dokumenty i metadane
  mongodb:
    image: mongo:7
    container_name: fulmark-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
    networks:
      - fulmark-network

  # Weaviate - Baza wektorowa
  weaviate:
    image: semitechnologies/weaviate:1.25.0
    container_name: fulmark-weaviate
    restart: unless-stopped
    ports:
      - "8082:8080"
      - "50051:50051"
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'text2vec-transformers'
      ENABLE_MODULES: 'text2vec-transformers'
      TRANSFORMERS_INFERENCE_API: 'http://embeddings:8080'
    volumes:
      - weaviate_data:/var/lib/weaviate
    depends_on:
      - embeddings
    networks:
      - fulmark-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/v1/.well-known/ready"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Embeddings transformer
  embeddings:
    image: cr.weaviate.io/semitechnologies/transformers-inference:sentence-transformers-all-mpnet-base-v2
    container_name: fulmark-embeddings
    restart: unless-stopped
    environment:
      ENABLE_CUDA: 0
    networks:
      - fulmark-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/.well-known/ready"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 🤖 SYSTEM TRANSKRYPCJI

  # Gemma 4B Local Model Server
  gemma-4b:
    build:
      context: ./transcription-system/gemma-4b-container
      dockerfile: Dockerfile
    container_name: fulmark-gemma-4b
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - gemma_models:/root/.ollama
    networks:
      - fulmark-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 120s

  # NVIDIA NeMo STT Service
  nvidia-stt:
    build:
      context: ./transcription-system/simple-stt
      dockerfile: Dockerfile
    container_name: fulmark-nvidia-stt
    restart: unless-stopped
    ports:
      - "8889:8889"
    volumes:
      - ./transcription-system/transcriptions:/app/transcriptions
      - ./transcription-system/logs:/app/logs
    networks:
      - fulmark-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8889/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Audio Converter Service
  audio-converter:
    build:
      context: ./transcription-system/audio-converter
      dockerfile: Dockerfile
    container_name: fulmark-audio-converter
    restart: unless-stopped
    volumes:
      - ./transcription-system/audio_input:/app/audio_input
      - ./transcription-system/audio_converted:/app/audio_converted
      - ./transcription-system/logs:/app/logs
    networks:
      - fulmark-network

  # Email Processor Service
  email-processor:
    build:
      context: ./transcription-system/email-processor
      dockerfile: Dockerfile
    container_name: fulmark-email-processor
    restart: unless-stopped
    environment:
      - REDIS_URL=redis://fulmark-redis:6379
      - MONGODB_URL=***************************************************************************
    volumes:
      - ./transcription-system/email_attachments:/app/email_attachments
      - ./transcription-system/logs:/app/logs
    depends_on:
      - redis
      - mongodb
    networks:
      - fulmark-network

  # Gemma Integration Service
  gemma-integration:
    build:
      context: ./transcription-system/gemma-integration
      dockerfile: Dockerfile
    container_name: fulmark-gemma-integration
    restart: unless-stopped
    environment:
      - LM_STUDIO_URL=http://fulmark-gemma-4b:11434
      - MODEL_NAME=gemma3:4b
      - LANGUAGE=pl
      - MAX_TOKENS=2048
      - TEMPERATURE=0.3
      - REDIS_URL=redis://fulmark-redis:6379
    volumes:
      - ./transcription-system/transcriptions:/app/transcriptions
      - ./transcription-system/analysis_results:/app/analysis
      - ./transcription-system/logs:/app/logs
    depends_on:
      gemma-4b:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - fulmark-network

  # Transcription Orchestrator
  transcription-orchestrator:
    build:
      context: ./transcription-system/orchestrator
      dockerfile: Dockerfile
    container_name: fulmark-transcription-orchestrator
    restart: unless-stopped
    ports:
      - "9000:8080"
    environment:
      - REDIS_URL=redis://fulmark-redis:6379
      - NVIDIA_STT_URL=http://fulmark-nvidia-stt:8889
      - AUDIO_CONVERTER_URL=http://fulmark-audio-converter:8080
      - EMAIL_PROCESSOR_URL=http://fulmark-email-processor:8080
      - GEMMA_INTEGRATION_URL=http://fulmark-gemma-integration:8080
      - GOBACKEND_URL=http://host.docker.internal:8080
    volumes:
      - ./transcription-system/logs:/app/logs
    depends_on:
      - redis
      - nvidia-stt
      - audio-converter
      - email-processor
      - gemma-integration
    networks:
      - fulmark-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 📧 EMAIL INTELLIGENCE
  
  email-intelligence:
    build:
      context: ./email_intelligence
      dockerfile: Dockerfile
    container_name: fulmark-email-intelligence
    restart: unless-stopped
    ports:
      - "8001:8001"
    environment:
      - WEAVIATE_URL=http://fulmark-weaviate:8080
      - REDIS_URL=redis://fulmark-redis:6379
      - MONGODB_URL=***************************************************************************
      - TRANSCRIPTION_ORCHESTRATOR_URL=http://fulmark-transcription-orchestrator:8080
    volumes:
      - ./email_intelligence/logs:/app/logs
      - ./email_intelligence/temp:/app/temp
      - ./email_intelligence/attachments:/app/attachments
    depends_on:
      weaviate:
        condition: service_healthy
      transcription-orchestrator:
        condition: service_healthy
    networks:
      - fulmark-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
  mongodb_data:
  weaviate_data:
  gemma_models:

networks:
  fulmark-network:
    driver: bridge