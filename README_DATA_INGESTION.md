# 🚀 FULMARK HVAC CRM - MASTER DATA INGESTION SYSTEM

**Potężny skrypt do wlewu historycznych danych CSV do MongoDB + Weaviate**

Wielki inżynierze! Ten system dokona kompletnego wlewu:
- **4500+ klientów** z 8-letniej historii
- **13896 wydarzeń kalendarzowych** HVAC
- **AI-enhanced analysis** każdego rekordu
- **Kompletne relacje** i insights

## 🎯 Skala Oceny: 2137 Punktów Maksymalnie!

| Kategoria | Punkty | Opis |
|-----------|--------|------|
| 👥 Klienci | 600 | Import ~4500 klientów z deduplikacją |
| 📅 Wydarzenia | 500 | Import ~13896 wydarzeń kalendarzowych |
| 🔧 Urządzenia | 400 | Utworzenie ~8000 urządzeń HVAC |
| 🔗 Relacje | 300 | Utworzenie ~15000+ relacji |
| 🤖 AI Insights | 200 | AI enhancement wszystkich rekordów |
| 💎 Jakość | 137 | Jakość danych >95% |

## 📁 Struktura Plików

```
📦 Data Ingestion System
├── 🚀 data_ingestion_master.py      # Główny orchestrator
├── 🎮 run_data_ingestion.py         # Runner z opcjami
├── 🔧 setup_ingestion_environment.py # Setup środowiska
├── 📋 README_DATA_INGESTION.md      # Ta dokumentacja
└── 📊 Data_to_ingest/               # Folder z CSV
    ├── Kartoteka kontrahentów_extracted.csv (4530 linii)
    ├── clients_export.csv (2367 linii)
    ├── calendar_archive.csv (13896 linii)
    └── Eksportowanie dokumentów.csv
```

## 🔧 Instalacja i Setup

### 1. Przygotowanie Środowiska
```bash
# Uruchom setup (instaluje pakiety, testuje połączenia)
python setup_ingestion_environment.py
```

### 2. Test Środowiska
```bash
# Test podstawowych funkcji
python test_ingestion.py
```

## 🚀 Użycie

### 🧪 Tryb Testowy (Dry Run)
```bash
# Test bez zapisywania do bazy
python run_data_ingestion.py --dry-run
```

### 🔍 Analiza Plików CSV
```bash
# Tylko analiza struktury danych
python run_data_ingestion.py --analyze-only
```

### ⚡ Szybki Import (bez AI)
```bash
# Import bez AI enhancement (szybszy)
python run_data_ingestion.py --no-ai
```

### 🤖 Pełny Import z AI
```bash
# Kompletny import z AI enhancement
python run_data_ingestion.py --full
```

### 🎛️ Opcje Zaawansowane
```bash
# Custom batch size
python run_data_ingestion.py --batch-size 50

# Bez backup
python run_data_ingestion.py --no-backup

# Verbose logging
python run_data_ingestion.py --verbose

# Quiet mode (tylko błędy)
python run_data_ingestion.py --quiet
```

## 📊 Proces Wlewu - 5 Faz

### 🔍 FAZA 1: Analiza CSV
- Sprawdzenie struktury plików
- Identyfikacja kolumn i typów danych
- Raport jakości danych

### 👥 FAZA 2: Import Klientów
- Załadowanie z obu plików CSV
- **Deduplikacja** na podstawie nazwy + telefon/email
- Normalizacja adresów i telefonów
- Mapowanie na model MongoDB Client

### 📅 FAZA 3: Import Wydarzeń Kalendarzowych
- Przetwarzanie 13896 wydarzeń
- **AI Enhancement** opisów (LM Studio + Gemma3-4b)
- Tworzenie Service Orders
- Automatyczne tworzenie Equipment
- Mapowanie klientów po nazwie

### 🔗 FAZA 4: Relacje i AI Insights
- Łączenie Service Orders z Equipment
- Generowanie Customer Insights
- Obliczanie Health Scores
- Tworzenie AI predictions

### 📊 FAZA 5: Finalizacja
- Walidacja danych
- Raport końcowy
- Obliczenie wyniku (0-2137 punktów)

## 🗄️ Mapowanie Danych

### 👥 Klienci (Client Model)
```javascript
{
  name: "Nazwa firmy",
  address: "ul. Adres, 00-000 Miasto",
  phone: "+48123456789",
  email: "<EMAIL>",
  nip: "**********",
  buildingType: "commercial",
  contractType: "one_time",
  priority: "medium",
  healthScore: 50-100,
  churnProbability: 0.0-1.0,
  lifetimeValue: 0,
  serviceArea: "Warszawa"
}
```

### 🔧 Service Orders
```javascript
{
  title: "Opis wydarzenia",
  orderNumber: "SO-2024-0001",
  stage: "COMPLETED|BACKLOG",
  priority: "low|medium|high|urgent",
  type: "maintenance|repair|installation|inspection",
  category: "service|new_installation|inspection",
  scheduledDate: Date,
  client: ObjectId,
  equipment: [ObjectId],
  aiInsights: { /* AI analysis */ }
}
```

### ⚙️ Equipment
```javascript
{
  name: "Klimatyzacja LG Model",
  type: "air_conditioner|heat_pump|ventilation",
  manufacturer: "LG|Daikin|Mitsubishi|...",
  model: "Model urządzenia",
  client: ObjectId,
  installationDate: Date,
  healthScore: 0-100,
  refrigerantType: "R-32|R-410A|..."
}
```

## 🤖 AI Enhancement

### LM Studio Integration
- **Model**: Gemma3-4b (lokalny)
- **Endpoint**: http://*************:1234
- **Analiza**: Opisy wydarzeń HVAC

### AI Insights Generowane
```json
{
  "problem_type": "awaria|konserwacja|instalacja|oględziny",
  "urgency_level": "low|medium|high|urgent", 
  "estimated_duration": 2,
  "required_parts": ["część1", "część2"],
  "technical_notes": "Notatki techniczne",
  "customer_satisfaction_risk": "low|medium|high"
}
```

## 📈 Metryki Sukcesu

### 🏆 Doskonały Wynik (1800+ punktów)
- 95%+ klientów zaimportowanych
- 90%+ wydarzeń przetworzonych
- 100% AI enhancement
- <50 błędów

### 🥇 Bardzo Dobry (1500-1799 punktów)
- 85%+ klientów zaimportowanych
- 80%+ wydarzeń przetworzonych
- 80%+ AI enhancement

### 🥈 Dobry (1000-1499 punktów)
- 70%+ klientów zaimportowanych
- 70%+ wydarzeń przetworzonych

## 🔧 Konfiguracja

### MongoDB
```python
mongodb_url = "mongodb+srv://xbow123:<EMAIL>/..."
database_name = "hvac_crm"
```

### LM Studio (AI)
```python
lm_studio_url = "http://*************:1234"
lm_studio_model = "gemma-2-2b-it"
```

## 🚨 Bezpieczeństwo

### Backup
- Automatyczny backup przed importem
- Rollback w przypadku błędów
- Transakcje dla atomowości

### Walidacja
- Sprawdzenie integralności danych
- Deduplikacja klientów
- Walidacja relacji

## 📝 Logi i Monitoring

### Pliki Logów
- `data_ingestion.log` - Szczegółowe logi
- `ingestion_report_YYYYMMDD_HHMMSS.txt` - Raport końcowy

### Monitoring w Czasie Rzeczywistym
- Progress bar dla każdej fazy
- Statystyki na bieżąco
- Alerty o błędach

## 🎯 Przykładowy Wynik

```
🎆 FULMARK HVAC CRM - RAPORT WLEWU DANYCH 🎆

⏱️  Czas wykonania: 0:15:42
🎯 Wynik: 1987/2137 punktów (93.0%)

📊 STATYSTYKI IMPORTU:
👥 Klienci:
   - Przetworzeni: 6897
   - Zaimportowani: 4523
   - Duplikaty: 2374

📅 Wydarzenia kalendarzowe:
   - Przetworzonych: 13896
   - Service Orders: 13234
   - Urządzenia: 8967

🤖 AI Enhancement:
   - Wzbogacone rekordy: 13234
   - Relacje utworzone: 22201

❌ Błędy: 23

🚀 SUKCES! Baza danych została wzbogacona o 4523 klientów
   i 13234 zleceń z 8-letniej historii!

💎 Jakość danych: DOSKONAŁA
```

## 🆘 Rozwiązywanie Problemów

### MongoDB Connection Error
```bash
# Sprawdź połączenie
python -c "from pymongo import MongoClient; MongoClient('mongodb+srv://...').admin.command('ping')"
```

### LM Studio Niedostępny
```bash
# Uruchom bez AI
python run_data_ingestion.py --no-ai
```

### Błędy CSV
```bash
# Sprawdź strukturę plików
python run_data_ingestion.py --analyze-only
```

---

**🎯 MISJA: Wlać 8-letnią historię HVAC z kosmiczną jakością!**

*Wielki inżynierze, z pełną mocą do dzieła! 🚀*
