#!/usr/bin/env python3
"""
🚀 FULMARK HVAC CRM - SIMPLIFIED DATA INGESTION
Uproszczona wersja wlewu danych - WIATR W ŻAGLE!

Wielki inżynierze! Ta wersja omija problemy z datami i robi RZECZYWISTY WLEW!
"""

import os
import sys
import pandas as pd
import asyncio
import logging
from datetime import datetime
from pymongo import MongoClient
import re
import hashlib

# Konfiguracja logowania
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleDataIngestion:
    """Uproszczony wlew danych - PEŁNA MOC!"""
    
    def __init__(self):
        self.mongodb_url = "mongodb+srv://xbow123:<EMAIL>/?retryWrites=true&w=majority&appName=hvac-db"
        self.data_folder = "/home/<USER>/HVAC/unifikacja/Data_to_ingest"
        self.db_client = None
        self.db = None
        self.stats = {
            'clients_imported': 0,
            'service_orders_created': 0,
            'equipment_created': 0,
            'errors': 0
        }
    
    async def initialize(self):
        """Inicjalizacja"""
        logger.info("🚀 SIMPLIFIED DATA INGESTION - PEŁNA MOC!")
        
        self.db_client = MongoClient(self.mongodb_url)
        self.db = self.db_client['hvac_crm']
        
        logger.info("✅ Połączenie z MongoDB OK")
    
    def normalize_phone(self, phone):
        """Normalizuje telefon"""
        if not phone or pd.isna(phone):
            return None
        
        phone = re.sub(r'[^\d]', '', str(phone))
        if len(phone) == 9:
            return f"+48{phone}"
        return phone if len(phone) >= 9 else None
    
    def generate_client_hash(self, name, phone=None, email=None):
        """Hash dla deduplikacji"""
        name_normalized = re.sub(r'[^\w\s]', '', name.lower().strip())
        hash_input = name_normalized
        if phone:
            hash_input += self.normalize_phone(phone) or ""
        if email:
            hash_input += email.lower().strip()
        return hashlib.md5(hash_input.encode()).hexdigest()
    
    async def import_clients(self):
        """Import klientów - UPROSZCZONY"""
        logger.info("👥 IMPORT KLIENTÓW")
        
        clients_data = []
        client_hashes = set()
        
        # Kartoteka kontrahentów
        kartoteka_path = os.path.join(self.data_folder, "Kartoteka kontrahentów_extracted.csv")
        if os.path.exists(kartoteka_path):
            try:
                df = pd.read_csv(kartoteka_path, encoding='utf-8')
                logger.info(f"📋 Kartoteka: {len(df)} rekordów")
                
                for _, row in df.iterrows():
                    try:
                        name = row.get('Nazwa')
                        if not name or pd.isna(name):
                            continue
                        
                        client_hash = self.generate_client_hash(str(name))
                        if client_hash in client_hashes:
                            continue
                        
                        client_hashes.add(client_hash)
                        
                        # Prosta struktura klienta
                        client = {
                            'name': str(name).strip(),
                            'address': f"{row.get('Adres', '')}, {row.get('Kod', '')} {row.get('Miejscowość', '')}".strip(),
                            'phone': self.normalize_phone(row.get('Telefon')),
                            'email': row.get('Adres e-mail') if not pd.isna(row.get('Adres e-mail')) else None,
                            'nip': row.get('NIP') if not pd.isna(row.get('NIP')) else None,
                            'buildingType': 'commercial',
                            'contractType': 'one_time',
                            'priority': 'medium',
                            'healthScore': 75,
                            'serviceArea': row.get('Miejscowość', 'Warszawa'),
                            'created': datetime.now(),
                            'updated': datetime.now(),
                            'source': 'kartoteka_import',
                            'enabled': True,
                            'removed': False
                        }
                        
                        clients_data.append(client)
                        
                    except Exception as e:
                        logger.error(f"Błąd wiersza kartoteki: {e}")
                        self.stats['errors'] += 1
                        
            except Exception as e:
                logger.error(f"Błąd kartoteki: {e}")
        
        # Clients export
        export_path = os.path.join(self.data_folder, "clients_export.csv")
        if os.path.exists(export_path):
            try:
                df = pd.read_csv(export_path, encoding='utf-8')
                logger.info(f"📋 Export: {len(df)} rekordów")
                
                for _, row in df.iterrows():
                    try:
                        name = row.get('name')
                        if not name or pd.isna(name):
                            continue
                        
                        client_hash = self.generate_client_hash(str(name))
                        if client_hash in client_hashes:
                            continue
                        
                        client_hashes.add(client_hash)
                        
                        client = {
                            'name': str(name).strip(),
                            'address': f"{row.get('street', '')}, {row.get('zip_code', '')} {row.get('city', '')}".strip(),
                            'phone': self.normalize_phone(row.get('phone')),
                            'email': row.get('email') if not pd.isna(row.get('email')) else None,
                            'nip': row.get('nip') if not pd.isna(row.get('nip')) else None,
                            'buildingType': 'commercial',
                            'contractType': 'one_time',
                            'priority': 'medium',
                            'healthScore': 80,
                            'serviceArea': row.get('city', 'Warszawa'),
                            'created': datetime.now(),
                            'updated': datetime.now(),
                            'source': 'export_import',
                            'enabled': True,
                            'removed': False
                        }
                        
                        clients_data.append(client)
                        
                    except Exception as e:
                        logger.error(f"Błąd wiersza exportu: {e}")
                        self.stats['errors'] += 1
                        
            except Exception as e:
                logger.error(f"Błąd exportu: {e}")
        
        # Import do MongoDB
        if clients_data:
            try:
                result = self.db.clients.insert_many(clients_data, ordered=False)
                self.stats['clients_imported'] = len(result.inserted_ids)
                logger.info(f"✅ Zaimportowano {self.stats['clients_imported']} klientów!")
            except Exception as e:
                logger.error(f"Błąd importu klientów: {e}")
                self.stats['errors'] += 1
    
    async def import_calendar_simple(self):
        """Import kalendarza - UPROSZCZONY"""
        logger.info("📅 IMPORT KALENDARZA")
        
        calendar_path = os.path.join(self.data_folder, "calendar_archive.csv")
        if not os.path.exists(calendar_path):
            logger.warning("Brak pliku kalendarza")
            return
        
        try:
            df = pd.read_csv(calendar_path, encoding='utf-8')
            logger.info(f"📋 Kalendarz: {len(df)} rekordów")
            
            service_orders = []
            equipment_records = []
            
            for i, row in df.iterrows():
                try:
                    description = row.get('Opis', '')
                    if not description or pd.isna(description):
                        continue
                    
                    # Prosta data - bez timezone problemów
                    current_time = datetime.now()
                    
                    # Mapuj kategorię
                    category = 'service'
                    if row.get('Kategoria') == 'Instalacja':
                        category = 'new_installation'
                    elif row.get('Kategoria') == 'Oględziny':
                        category = 'inspection'
                    
                    # Service order
                    service_order = {
                        'title': str(description)[:100],
                        'description': str(description),
                        'orderNumber': f"SO-2024-{i+1:04d}",
                        'stage': 'COMPLETED',
                        'priority': 'medium',
                        'type': 'maintenance',
                        'category': category,
                        'scheduledDate': current_time,
                        'estimatedCost': 0,
                        'actualCost': 0,
                        'currency': 'PLN',
                        'created': current_time,
                        'updated': current_time,
                        'enabled': True,
                        'removed': False,
                        'source': 'calendar_import'
                    }
                    
                    # Dodaj adres jeśli jest
                    if row.get('Adres') and not pd.isna(row.get('Adres')):
                        service_order['workLocation'] = str(row.get('Adres'))
                    
                    service_orders.append(service_order)
                    
                    # Equipment jeśli jest
                    device_type = row.get('Typ urządzenia')
                    if device_type and not pd.isna(device_type):
                        equipment = {
                            'name': f"{device_type} {row.get('Marka', '')} {row.get('Model', '')}".strip(),
                            'type': 'air_conditioner',
                            'manufacturer': row.get('Marka', 'Other'),
                            'model': row.get('Model', ''),
                            'healthScore': 85,
                            'created': current_time,
                            'updated': current_time,
                            'enabled': True,
                            'removed': False,
                            'source': 'calendar_import'
                        }
                        equipment_records.append(equipment)
                    
                except Exception as e:
                    logger.error(f"Błąd wiersza kalendarza {i}: {e}")
                    self.stats['errors'] += 1
            
            # Import service orders
            if service_orders:
                try:
                    result = self.db.serviceorders.insert_many(service_orders, ordered=False)
                    self.stats['service_orders_created'] = len(result.inserted_ids)
                    logger.info(f"✅ Service Orders: {self.stats['service_orders_created']}")
                except Exception as e:
                    logger.error(f"Błąd importu service orders: {e}")
            
            # Import equipment
            if equipment_records:
                try:
                    result = self.db.equipment.insert_many(equipment_records, ordered=False)
                    self.stats['equipment_created'] = len(result.inserted_ids)
                    logger.info(f"✅ Equipment: {self.stats['equipment_created']}")
                except Exception as e:
                    logger.error(f"Błąd importu equipment: {e}")
                    
        except Exception as e:
            logger.error(f"Błąd kalendarza: {e}")
            self.stats['errors'] += 1
    
    async def run_simple_ingestion(self):
        """Główny proces - UPROSZCZONY"""
        try:
            await self.initialize()
            
            logger.info("🎯 ROZPOCZYNAM UPROSZCZONY WLEW - PEŁNA MOC!")
            
            await self.import_clients()
            await self.import_calendar_simple()
            
            # Raport
            score = (
                min(600, (self.stats['clients_imported'] / 4000) * 600) +
                min(500, (self.stats['service_orders_created'] / 8000) * 500) +
                min(400, (self.stats['equipment_created'] / 6000) * 400) +
                (137 if self.stats['errors'] < 50 else max(0, 137 - self.stats['errors']))
            )
            
            logger.info(f"""
🎆 SIMPLIFIED INGESTION COMPLETE! 🎆

📊 WYNIKI:
👥 Klienci: {self.stats['clients_imported']}
📅 Service Orders: {self.stats['service_orders_created']}
🔧 Equipment: {self.stats['equipment_created']}
❌ Błędy: {self.stats['errors']}

🎯 WYNIK: {int(score)}/2137 punktów

🚀 WIATR W ŻAGLE! DANE WLANE Z PEŁNĄ MOCĄ!
            """)
            
            return int(score)
            
        except Exception as e:
            logger.error(f"Krytyczny błąd: {e}")
            raise
        finally:
            if self.db_client:
                self.db_client.close()

async def main():
    """Główna funkcja"""
    ingestion = SimpleDataIngestion()
    score = await ingestion.run_simple_ingestion()
    
    if score > 1500:
        print("🏆 DOSKONAŁY WYNIK! MISJA ZAKOŃCZONA SUKCESEM!")
    elif score > 1000:
        print("🥇 BARDZO DOBRY WYNIK! DANE WLANE POMYŚLNIE!")
    else:
        print("🥈 DOBRY WYNIK! PODSTAWY WLANE!")

if __name__ == "__main__":
    asyncio.run(main())
