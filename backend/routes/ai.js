const express = require('express');
const router = express.Router();
const aiManager = require('../services/aiManager');

/**
 * 🧠 AI MANAGEMENT API ROUTES
 * Endpoints for Bielik V3 4.5B and hybrid AI system management
 */

/**
 * @route   GET /api/ai/health
 * @desc    Get health status of all AI models
 * @access  Public
 */
router.get('/health', async (req, res) => {
    try {
        const health = await aiManager.healthCheck();
        
        const statusCode = health.overall_status === 'healthy' ? 200 :
                          health.overall_status === 'degraded' ? 206 : 503;
        
        res.status(statusCode).json({
            success: true,
            data: health
        });
    } catch (error) {
        console.error('AI health check failed:', error);
        res.status(500).json({
            success: false,
            error: 'Health check failed',
            details: error.message
        });
    }
});

/**
 * @route   GET /api/ai/stats
 * @desc    Get AI usage statistics
 * @access  Public
 */
router.get('/stats', (req, res) => {
    try {
        const stats = aiManager.getStats();
        res.json({
            success: true,
            data: stats
        });
    } catch (error) {
        console.error('Failed to get AI stats:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get statistics',
            details: error.message
        });
    }
});

/**
 * @route   POST /api/ai/analyze
 * @desc    Analyze text using hybrid AI system
 * @access  Public
 */
router.post('/analyze', async (req, res) => {
    try {
        const { text, type = 'general', options = {} } = req.body;
        
        if (!text) {
            return res.status(400).json({
                success: false,
                error: 'Text is required for analysis'
            });
        }
        
        const result = await aiManager.analyzeText(text, type, options);
        
        res.json({
            success: true,
            data: result
        });
    } catch (error) {
        console.error('AI analysis failed:', error);
        res.status(500).json({
            success: false,
            error: 'Analysis failed',
            details: error.message
        });
    }
});

/**
 * @route   POST /api/ai/analyze/email
 * @desc    Analyze HVAC email content
 * @access  Public
 */
router.post('/analyze/email', async (req, res) => {
    try {
        const { content, metadata = {} } = req.body;
        
        if (!content) {
            return res.status(400).json({
                success: false,
                error: 'Email content is required'
            });
        }
        
        const result = await aiManager.analyzeEmail(content, metadata);
        
        res.json({
            success: true,
            data: result,
            type: 'email_analysis'
        });
    } catch (error) {
        console.error('Email analysis failed:', error);
        res.status(500).json({
            success: false,
            error: 'Email analysis failed',
            details: error.message
        });
    }
});

/**
 * @route   POST /api/ai/analyze/transcription
 * @desc    Analyze HVAC call transcription
 * @access  Public
 */
router.post('/analyze/transcription', async (req, res) => {
    try {
        const { content, metadata = {} } = req.body;
        
        if (!content) {
            return res.status(400).json({
                success: false,
                error: 'Transcription content is required'
            });
        }
        
        const result = await aiManager.analyzeTranscription(content, metadata);
        
        res.json({
            success: true,
            data: result,
            type: 'transcription_analysis'
        });
    } catch (error) {
        console.error('Transcription analysis failed:', error);
        res.status(500).json({
            success: false,
            error: 'Transcription analysis failed',
            details: error.message
        });
    }
});

/**
 * @route   POST /api/ai/classify/issue
 * @desc    Classify HVAC issue priority and type
 * @access  Public
 */
router.post('/classify/issue', async (req, res) => {
    try {
        const { description, metadata = {} } = req.body;
        
        if (!description) {
            return res.status(400).json({
                success: false,
                error: 'Issue description is required'
            });
        }
        
        const result = await aiManager.classifyIssue(description, metadata);
        
        res.json({
            success: true,
            data: result,
            type: 'issue_classification'
        });
    } catch (error) {
        console.error('Issue classification failed:', error);
        res.status(500).json({
            success: false,
            error: 'Issue classification failed',
            details: error.message
        });
    }
});

/**
 * @route   POST /api/ai/generate/response
 * @desc    Generate professional HVAC response
 * @access  Public
 */
router.post('/generate/response', async (req, res) => {
    try {
        const { inquiry, context = {} } = req.body;
        
        if (!inquiry) {
            return res.status(400).json({
                success: false,
                error: 'Inquiry is required'
            });
        }
        
        const result = await aiManager.generateResponse(inquiry, context);
        
        res.json({
            success: true,
            data: result,
            type: 'response_generation'
        });
    } catch (error) {
        console.error('Response generation failed:', error);
        res.status(500).json({
            success: false,
            error: 'Response generation failed',
            details: error.message
        });
    }
});

/**
 * @route   POST /api/ai/stats/reset
 * @desc    Reset AI usage statistics
 * @access  Public
 */
router.post('/stats/reset', (req, res) => {
    try {
        aiManager.resetStats();
        res.json({
            success: true,
            message: 'Statistics reset successfully'
        });
    } catch (error) {
        console.error('Failed to reset stats:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to reset statistics',
            details: error.message
        });
    }
});

/**
 * @route   POST /api/ai/test/bielik
 * @desc    Test Bielik V3 4.5B with HVAC sample
 * @access  Public
 */
router.post('/test/bielik', async (req, res) => {
    try {
        const testPrompt = req.body.prompt || 
            'Przeanalizuj następujące zgłoszenie serwisowe HVAC: "Klimatyzacja w biurze nie chłodzi, robi dziwne dźwięki i kapie woda. Pilne." Określ priorytet, typ problemu i sugerowane działania.';
        
        const result = await aiManager.analyzeText(testPrompt, 'issue_classification', {
            force_model: 'bielik',
            test: true
        });
        
        res.json({
            success: true,
            data: result,
            test_prompt: testPrompt
        });
    } catch (error) {
        console.error('Bielik test failed:', error);
        res.status(500).json({
            success: false,
            error: 'Bielik test failed',
            details: error.message
        });
    }
});

/**
 * @route   GET /api/ai/models
 * @desc    Get available AI models and their status
 * @access  Public
 */
router.get('/models', async (req, res) => {
    try {
        const health = await aiManager.healthCheck();
        
        const models = Object.entries(health.models).map(([name, status]) => ({
            name,
            status: status.status,
            enabled: status.enabled || false,
            url: status.url || 'N/A',
            model: status.model || 'N/A',
            installed: status.installed || false,
            error: status.error || null
        }));
        
        res.json({
            success: true,
            data: {
                models,
                priority: health.priority,
                overall_status: health.overall_status
            }
        });
    } catch (error) {
        console.error('Failed to get models info:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get models information',
            details: error.message
        });
    }
});

module.exports = router;
