/**
 * 📊 BUSINESS OWNER INSIGHTS SERVICE 2025 - TIME-SAVING INTELLIGENCE
 * 
 * Generuje actionable insights dla właściciela firmy HVAC:
 * - Automatyczne podsumowania klientów
 * - Alerty o pilnych sprawach
 * - Możliwości biznesowe (upsell, cross-sell)
 * - Predykcja problemów i rozwiązania
 * - Optymalizacja czasu i zasobów
 * 
 * Features:
 * - Daily/Weekly business summaries
 * - Customer risk alerts
 * - Revenue opportunity detection
 * - Operational efficiency insights
 * - Seasonal trend analysis
 * - Competitive intelligence
 */

const OpenAI = require('openai');
const enhancedCustomer360Service = require('./enhancedCustomer360Service');
const enhancedWeaviateService = require('./enhancedWeaviateService');
const logger = require('../utils/logger');

class BusinessOwnerInsightsService {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  /**
   * 📊 Generate daily business insights for owner
   */
  async generateDailyInsights() {
    try {
      logger.info('📊 Generating daily business insights for owner');

      const insights = {
        date: new Date().toISOString().split('T')[0],
        summary: await this.generateDailySummary(),
        urgentAlerts: await this.getUrgentAlerts(),
        revenueOpportunities: await this.getRevenueOpportunities(),
        customerRisks: await this.getCustomerRisks(),
        operationalInsights: await this.getOperationalInsights(),
        seasonalRecommendations: await this.getSeasonalRecommendations(),
        timeToActionItems: await this.getTimeToActionItems()
      };

      logger.info('✅ Daily business insights generated successfully');
      return insights;

    } catch (error) {
      logger.error(`❌ Failed to generate daily insights: ${error.message}`);
      throw error;
    }
  }

  /**
   * 📈 Generate weekly business report
   */
  async generateWeeklyReport() {
    try {
      logger.info('📈 Generating weekly business report');

      const report = {
        week: this.getCurrentWeek(),
        executiveSummary: await this.generateWeeklyExecutiveSummary(),
        keyMetrics: await this.getWeeklyKeyMetrics(),
        customerInsights: await this.getWeeklyCustomerInsights(),
        revenueAnalysis: await this.getWeeklyRevenueAnalysis(),
        operationalEfficiency: await this.getWeeklyOperationalEfficiency(),
        marketTrends: await this.getWeeklyMarketTrends(),
        nextWeekRecommendations: await this.getNextWeekRecommendations()
      };

      logger.info('✅ Weekly business report generated successfully');
      return report;

    } catch (error) {
      logger.error(`❌ Failed to generate weekly report: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🚨 Get urgent alerts requiring immediate attention
   */
  async getUrgentAlerts() {
    try {
      const alerts = [];

      // Check for high-risk customers
      const highRiskCustomers = await this.getHighRiskCustomers();
      if (highRiskCustomers.length > 0) {
        alerts.push({
          type: 'customer_risk',
          priority: 'high',
          title: `${highRiskCustomers.length} klientów z wysokim ryzykiem utraty`,
          description: 'Klienci wymagający natychmiastowej uwagi',
          action: 'Skontaktuj się w ciągu 24 godzin',
          customers: highRiskCustomers.slice(0, 3) // Top 3 most urgent
        });
      }

      // Check for overdue payments
      const overduePayments = await this.getOverduePayments();
      if (overduePayments.length > 0) {
        alerts.push({
          type: 'payment_overdue',
          priority: 'high',
          title: `${overduePayments.length} zaległych płatności`,
          description: `Łączna kwota: ${this.formatCurrency(overduePayments.reduce((sum, p) => sum + p.amount, 0))}`,
          action: 'Wyślij przypomnienia o płatności',
          payments: overduePayments
        });
      }

      // Check for equipment failures
      const equipmentFailures = await this.getCriticalEquipmentFailures();
      if (equipmentFailures.length > 0) {
        alerts.push({
          type: 'equipment_failure',
          priority: 'critical',
          title: `${equipmentFailures.length} krytycznych awarii sprzętu`,
          description: 'Wymagają natychmiastowej interwencji',
          action: 'Wyślij technika w ciągu 2 godzin',
          failures: equipmentFailures
        });
      }

      return alerts;

    } catch (error) {
      logger.error(`❌ Failed to get urgent alerts: ${error.message}`);
      return [];
    }
  }

  /**
   * 💰 Get revenue opportunities
   */
  async getRevenueOpportunities() {
    try {
      const opportunities = [];

      // Upsell opportunities
      const upsellOpportunities = await this.getUpsellOpportunities();
      opportunities.push(...upsellOpportunities);

      // Maintenance contract opportunities
      const maintenanceOpportunities = await this.getMaintenanceContractOpportunities();
      opportunities.push(...maintenanceOpportunities);

      // Seasonal opportunities
      const seasonalOpportunities = await this.getSeasonalOpportunities();
      opportunities.push(...seasonalOpportunities);

      // Sort by potential value
      opportunities.sort((a, b) => b.potentialValue - a.potentialValue);

      return opportunities.slice(0, 10); // Top 10 opportunities

    } catch (error) {
      logger.error(`❌ Failed to get revenue opportunities: ${error.message}`);
      return [];
    }
  }

  /**
   * ⚠️ Get customer risks
   */
  async getCustomerRisks() {
    try {
      const risks = [];

      // Churn risk customers
      const churnRiskCustomers = await this.getChurnRiskCustomers();
      risks.push(...churnRiskCustomers.map(customer => ({
        type: 'churn_risk',
        customerId: customer.id,
        customerName: customer.name,
        riskScore: customer.churnRisk,
        factors: customer.riskFactors,
        recommendation: customer.recommendation,
        potentialLoss: customer.lifetimeValue
      })));

      // Satisfaction decline
      const satisfactionDecline = await this.getSatisfactionDeclineCustomers();
      risks.push(...satisfactionDecline.map(customer => ({
        type: 'satisfaction_decline',
        customerId: customer.id,
        customerName: customer.name,
        currentSatisfaction: customer.satisfaction,
        trend: customer.satisfactionTrend,
        recommendation: 'Przeprowadź rozmowę o satysfakcji'
      })));

      return risks;

    } catch (error) {
      logger.error(`❌ Failed to get customer risks: ${error.message}`);
      return [];
    }
  }

  /**
   * 🔧 Get operational insights
   */
  async getOperationalInsights() {
    try {
      const insights = {
        efficiency: await this.calculateOperationalEfficiency(),
        bottlenecks: await this.identifyBottlenecks(),
        resourceOptimization: await this.getResourceOptimizationSuggestions(),
        qualityMetrics: await this.getQualityMetrics(),
        customerServiceMetrics: await this.getCustomerServiceMetrics()
      };

      return insights;

    } catch (error) {
      logger.error(`❌ Failed to get operational insights: ${error.message}`);
      return {};
    }
  }

  /**
   * 🌍 Get seasonal recommendations
   */
  async getSeasonalRecommendations() {
    try {
      const currentSeason = this.getCurrentSeason();
      const recommendations = [];

      switch (currentSeason) {
        case 'winter':
          recommendations.push({
            category: 'heating',
            title: 'Sezon grzewczy - zwiększ gotowość serwisową',
            description: 'Przygotuj się na zwiększoną liczbę awarii systemów grzewczych',
            actions: [
              'Zwiększ zapas części zamiennych do kotłów',
              'Przygotuj harmonogram dyżurów weekendowych',
              'Wyślij przypomnienia o przeglądach grzewczych'
            ]
          });
          break;
        case 'summer':
          recommendations.push({
            category: 'cooling',
            title: 'Sezon chłodzący - maksymalna gotowość klimatyzacji',
            description: 'Szczyt sezonu klimatyzacyjnego - przygotuj się na wysokie zapotrzebowanie',
            actions: [
              'Zwiększ zapas części do klimatyzacji',
              'Rozważ dodatkowe zespoły serwisowe',
              'Promuj kontrakty serwisowe na następny rok'
            ]
          });
          break;
        case 'spring':
          recommendations.push({
            category: 'maintenance',
            title: 'Wiosna - czas przeglądów i przygotowań',
            description: 'Idealny czas na przeglądy przed sezonem letnim',
            actions: [
              'Zaproponuj przeglądy klimatyzacji',
              'Promuj modernizacje systemów',
              'Przygotuj oferty na nowe instalacje'
            ]
          });
          break;
        case 'autumn':
          recommendations.push({
            category: 'preparation',
            title: 'Jesień - przygotowania do sezonu grzewczego',
            description: 'Czas przygotowań systemów grzewczych na zimę',
            actions: [
              'Zaproponuj przeglądy systemów grzewczych',
              'Promuj modernizacje kotłów',
              'Przygotuj kampanię na pompy ciepła'
            ]
          });
          break;
      }

      return recommendations;

    } catch (error) {
      logger.error(`❌ Failed to get seasonal recommendations: ${error.message}`);
      return [];
    }
  }

  /**
   * ⏰ Get time-to-action items
   */
  async getTimeToActionItems() {
    try {
      const items = [];

      // Immediate actions (next 2 hours)
      const immediateActions = await this.getImmediateActions();
      items.push(...immediateActions.map(action => ({
        ...action,
        timeframe: 'immediate',
        deadline: new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 hours
      })));

      // Today's actions
      const todayActions = await this.getTodayActions();
      items.push(...todayActions.map(action => ({
        ...action,
        timeframe: 'today',
        deadline: new Date(new Date().setHours(23, 59, 59, 999))
      })));

      // This week's actions
      const weekActions = await this.getWeekActions();
      items.push(...weekActions.map(action => ({
        ...action,
        timeframe: 'this_week',
        deadline: this.getEndOfWeek()
      })));

      return items.sort((a, b) => a.deadline - b.deadline);

    } catch (error) {
      logger.error(`❌ Failed to get time-to-action items: ${error.message}`);
      return [];
    }
  }

  /**
   * 📊 Generate AI-powered business summary
   */
  async generateAIBusinessSummary(data) {
    try {
      const prompt = `
Jako ekspert business intelligence dla firm HVAC, przeanalizuj dane biznesowe i wygeneruj zwięzłe, actionable podsumowanie dla właściciela firmy HVAC:

DANE BIZNESOWE:
${JSON.stringify(data, null, 2)}

KONTEKST:
- Firma HVAC w Polsce
- Sezon: ${this.getCurrentSeason()}
- Właściciel potrzebuje szybkich, konkretnych insights
- Skupienie na oszczędności czasu i zwiększeniu zysków

WYGENERUJ:
1. 3-zdaniowe executive summary
2. Top 3 priorytety na dziś
3. Top 3 możliwości biznesowe
4. Top 3 ryzyka do monitorowania

Używaj konkretnych liczb i actionable recommendations.
`;

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4-1106-preview',
        messages: [
          {
            role: 'system',
            content: 'Jesteś ekspertem business intelligence dla firm HVAC. Generujesz zwięzłe, actionable insights dla busy business owners.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 1000
      });

      return response.choices[0].message.content;

    } catch (error) {
      logger.error(`❌ Failed to generate AI business summary: ${error.message}`);
      return 'Nie udało się wygenerować podsumowania AI';
    }
  }

  // Helper methods (would integrate with actual database queries)
  async generateDailySummary() {
    return {
      totalCustomers: 150,
      newLeads: 5,
      activeServiceOrders: 12,
      completedServices: 8,
      revenue: 15000,
      satisfaction: 4.3
    };
  }

  async getHighRiskCustomers() {
    return []; // Would query actual customer data
  }

  async getOverduePayments() {
    return []; // Would query payment data
  }

  async getCriticalEquipmentFailures() {
    return []; // Would query equipment data
  }

  async getUpsellOpportunities() {
    return []; // Would analyze customer data for upsell opportunities
  }

  async getMaintenanceContractOpportunities() {
    return []; // Would identify customers without maintenance contracts
  }

  async getSeasonalOpportunities() {
    return []; // Would identify seasonal business opportunities
  }

  async getChurnRiskCustomers() {
    return []; // Would identify customers at risk of churning
  }

  async getSatisfactionDeclineCustomers() {
    return []; // Would identify customers with declining satisfaction
  }

  async calculateOperationalEfficiency() {
    return { score: 85, trend: 'improving' };
  }

  async identifyBottlenecks() {
    return [];
  }

  async getResourceOptimizationSuggestions() {
    return [];
  }

  async getQualityMetrics() {
    return {};
  }

  async getCustomerServiceMetrics() {
    return {};
  }

  async getImmediateActions() {
    return [];
  }

  async getTodayActions() {
    return [];
  }

  async getWeekActions() {
    return [];
  }

  // Utility methods
  getCurrentSeason() {
    const month = new Date().getMonth();
    if (month >= 2 && month <= 4) return 'spring';
    if (month >= 5 && month <= 7) return 'summer';
    if (month >= 8 && month <= 10) return 'autumn';
    return 'winter';
  }

  getCurrentWeek() {
    const now = new Date();
    const startOfYear = new Date(now.getFullYear(), 0, 1);
    const pastDaysOfYear = (now - startOfYear) / 86400000;
    return Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
  }

  getEndOfWeek() {
    const now = new Date();
    const endOfWeek = new Date(now);
    endOfWeek.setDate(now.getDate() + (7 - now.getDay()));
    endOfWeek.setHours(23, 59, 59, 999);
    return endOfWeek;
  }

  formatCurrency(amount) {
    return new Intl.NumberFormat('pl-PL', {
      style: 'currency',
      currency: 'PLN'
    }).format(amount);
  }

  async generateWeeklyExecutiveSummary() {
    return 'Weekly executive summary would be generated here';
  }

  async getWeeklyKeyMetrics() {
    return {};
  }

  async getWeeklyCustomerInsights() {
    return {};
  }

  async getWeeklyRevenueAnalysis() {
    return {};
  }

  async getWeeklyOperationalEfficiency() {
    return {};
  }

  async getWeeklyMarketTrends() {
    return {};
  }

  async getNextWeekRecommendations() {
    return [];
  }
}

module.exports = new BusinessOwnerInsightsService();
