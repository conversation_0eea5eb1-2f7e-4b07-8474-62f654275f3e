/**
 * 🚀 ENHANCED CUSTOMER 360° PROFILE SERVICE 2025 - COSMIC LEVEL INTELLIGENCE
 * 
 * Automatycznie buduje szczegółowy profil klienta z każdej interakcji:
 * - Emaile z AI sentiment analysis
 * - Transkrypcje rozmów z emotion detection
 * - Historia CRM z behavioral patterns
 * - Predictive insights z machine learning
 * 
 * Features:
 * - Real-time profile updates
 * - AI-powered insights generation
 * - HVAC-specific behavioral analysis
 * - Predictive maintenance scheduling
 * - Churn risk assessment
 * - Upsell opportunity detection
 * - Communication style adaptation
 */

const OpenAI = require('openai');
const { z } = require('zod');
const { zodToJsonSchema } = require('zod-to-json-schema');
const enhancedWeaviateService = require('./enhancedWeaviateService');
const enhancedAIService = require('./enhancedAIService');
const logger = require('../utils/logger');

// Customer 360° Profile Schema
const Customer360ProfileSchema = z.object({
  basicInfo: z.object({
    customerId: z.string(),
    name: z.string(),
    email: z.string(),
    phone: z.string().optional(),
    address: z.string().optional(),
    buildingType: z.enum(['residential', 'commercial', 'industrial', 'government']),
    propertySize: z.number().optional(),
    lastUpdated: z.string()
  }),
  
  communicationProfile: z.object({
    preferredChannel: z.enum(['email', 'phone', 'sms', 'whatsapp']),
    communicationStyle: z.enum(['formal', 'casual', 'technical', 'simple']),
    responseTimeExpectation: z.enum(['immediate', 'same_day', 'next_day', 'flexible']),
    bestContactTime: z.string().optional(),
    languagePreference: z.enum(['polish', 'english', 'mixed']),
    communicationFrequency: z.enum(['high', 'medium', 'low']),
    sentimentTrend: z.array(z.object({
      date: z.string(),
      sentiment: z.enum(['very_positive', 'positive', 'neutral', 'negative', 'very_negative']),
      confidence: z.number()
    }))
  }),

  technicalProfile: z.object({
    technicalKnowledge: z.enum(['basic', 'intermediate', 'advanced', 'expert']),
    hvacExperience: z.enum(['first_time', 'some_experience', 'experienced', 'expert']),
    preferredBrands: z.array(z.string()),
    currentEquipment: z.array(z.object({
      type: z.string(),
      brand: z.string(),
      model: z.string().optional(),
      age: z.number().optional(),
      condition: z.enum(['excellent', 'good', 'fair', 'poor'])
    })),
    maintenanceAwareness: z.enum(['proactive', 'reactive', 'neglectful']),
    energyEfficiencyConcern: z.enum(['high', 'medium', 'low']),
    smartTechAdoption: z.enum(['early_adopter', 'mainstream', 'laggard'])
  }),

  businessProfile: z.object({
    budgetIndicator: z.enum(['economy', 'standard', 'premium', 'luxury']),
    decisionMakingStyle: z.enum(['quick', 'deliberate', 'committee', 'price_focused']),
    pricesensitivity: z.enum(['high', 'medium', 'low']),
    projectTimeframe: z.enum(['urgent', 'planned', 'flexible', 'future']),
    paymentHistory: z.enum(['excellent', 'good', 'fair', 'poor']),
    contractPreference: z.enum(['one_time', 'maintenance_contract', 'full_service']),
    referralPotential: z.enum(['high', 'medium', 'low'])
  }),

  behavioralPatterns: z.object({
    seasonalBehavior: z.object({
      spring: z.string().optional(),
      summer: z.string().optional(),
      autumn: z.string().optional(),
      winter: z.string().optional()
    }),
    servicePatterns: z.array(z.object({
      type: z.string(),
      frequency: z.string(),
      timing: z.string(),
      satisfaction: z.number()
    })),
    complaintPatterns: z.array(z.object({
      category: z.string(),
      frequency: z.number(),
      resolution: z.string()
    })),
    loyaltyIndicators: z.object({
      tenure: z.number(),
      repeatBusiness: z.number(),
      referrals: z.number(),
      satisfaction: z.number()
    })
  }),

  predictiveInsights: z.object({
    churnRisk: z.object({
      score: z.number().min(0).max(1),
      factors: z.array(z.string()),
      recommendation: z.string()
    }),
    upsellOpportunities: z.array(z.object({
      product: z.string(),
      probability: z.number(),
      value: z.number(),
      timing: z.string()
    })),
    maintenanceNeeds: z.array(z.object({
      equipment: z.string(),
      urgency: z.enum(['immediate', 'soon', 'planned', 'future']),
      estimatedCost: z.number(),
      description: z.string()
    })),
    nextBestActions: z.array(z.object({
      action: z.string(),
      priority: z.enum(['high', 'medium', 'low']),
      timing: z.string(),
      expectedOutcome: z.string()
    }))
  }),

  interactionHistory: z.object({
    totalInteractions: z.number(),
    lastInteraction: z.string(),
    interactionTypes: z.object({
      emails: z.number(),
      calls: z.number(),
      visits: z.number(),
      tickets: z.number()
    }),
    recentInteractions: z.array(z.object({
      date: z.string(),
      type: z.string(),
      summary: z.string(),
      sentiment: z.string(),
      outcome: z.string()
    }))
  })
});

class EnhancedCustomer360Service {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.profileCache = new Map(); // In-memory cache for frequent access
  }

  /**
   * 🎯 Build complete Customer 360° Profile
   */
  async buildCustomer360Profile(customerId) {
    try {
      logger.info(`🎯 Building Customer 360° Profile for: ${customerId}`);

      // Check cache first
      if (this.profileCache.has(customerId)) {
        const cached = this.profileCache.get(customerId);
        if (Date.now() - cached.timestamp < 300000) { // 5 minutes cache
          logger.info(`📋 Returning cached profile for: ${customerId}`);
          return cached.profile;
        }
      }

      // Gather all customer data
      const customerData = await this.gatherCustomerData(customerId);
      
      // Generate AI insights
      const aiInsights = await this.generateAIInsights(customerData);
      
      // Build comprehensive profile
      const profile360 = await this.assembleProfile360(customerData, aiInsights);
      
      // Store in Weaviate for semantic search
      await this.storeProfileInWeaviate(profile360);
      
      // Cache the profile
      this.profileCache.set(customerId, {
        profile: profile360,
        timestamp: Date.now()
      });

      logger.info(`✅ Customer 360° Profile built successfully: ${customerId}`);
      return profile360;

    } catch (error) {
      logger.error(`❌ Failed to build Customer 360° Profile: ${error.message}`);
      throw error;
    }
  }

  /**
   * 📊 Gather all customer data from multiple sources
   */
  async gatherCustomerData(customerId) {
    try {
      // This would integrate with actual database queries
      // For now, we'll simulate comprehensive data gathering
      
      const customerData = {
        basicInfo: await this.getBasicCustomerInfo(customerId),
        emails: await this.getCustomerEmails(customerId),
        transcriptions: await this.getCustomerTranscriptions(customerId),
        serviceHistory: await this.getServiceHistory(customerId),
        equipmentData: await this.getEquipmentData(customerId),
        paymentHistory: await this.getPaymentHistory(customerId),
        interactionHistory: await this.getInteractionHistory(customerId)
      };

      logger.info(`📊 Gathered comprehensive data for customer: ${customerId}`);
      return customerData;

    } catch (error) {
      logger.error(`❌ Failed to gather customer data: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🧠 Generate AI-powered insights from customer data
   */
  async generateAIInsights(customerData) {
    try {
      logger.info(`🧠 Generating AI insights for customer data`);

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4-1106-preview',
        messages: [
          {
            role: 'system',
            content: this.getCustomer360SystemPrompt()
          },
          {
            role: 'user',
            content: this.buildCustomerAnalysisPrompt(customerData)
          }
        ],
        response_format: {
          type: 'json_schema',
          json_schema: {
            name: 'customer_360_insights',
            schema: zodToJsonSchema(Customer360ProfileSchema)
          }
        },
        temperature: 0.1,
        max_tokens: 4000
      });

      const insights = JSON.parse(completion.choices[0].message.content);
      logger.info(`✅ AI insights generated successfully`);
      
      return insights;

    } catch (error) {
      logger.error(`❌ Failed to generate AI insights: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🔄 Process new interaction and update profile
   */
  async processNewInteraction(customerId, interactionData) {
    try {
      logger.info(`🔄 Processing new interaction for customer: ${customerId}`);

      // Analyze the interaction
      const interactionInsights = await this.analyzeInteraction(interactionData);
      
      // Get current profile
      const currentProfile = await this.buildCustomer360Profile(customerId);
      
      // Update profile with new insights
      const updatedProfile = await this.updateProfileWithInteraction(
        currentProfile, 
        interactionInsights
      );
      
      // Store updated profile
      await this.storeProfileInWeaviate(updatedProfile);
      
      // Update cache
      this.profileCache.set(customerId, {
        profile: updatedProfile,
        timestamp: Date.now()
      });

      // Generate real-time recommendations
      const recommendations = await this.generateRealTimeRecommendations(
        updatedProfile, 
        interactionInsights
      );

      logger.info(`✅ Profile updated with new interaction: ${customerId}`);
      
      return {
        updatedProfile,
        recommendations,
        interactionInsights
      };

    } catch (error) {
      logger.error(`❌ Failed to process interaction: ${error.message}`);
      throw error;
    }
  }

  /**
   * 📈 Generate predictive insights for customer
   */
  async generatePredictiveInsights(profile360) {
    try {
      const insights = {
        churnRisk: await this.calculateChurnRisk(profile360),
        upsellOpportunities: await this.identifyUpsellOpportunities(profile360),
        maintenanceNeeds: await this.predictMaintenanceNeeds(profile360),
        nextBestActions: await this.recommendNextActions(profile360)
      };

      return insights;

    } catch (error) {
      logger.error(`❌ Failed to generate predictive insights: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🎯 Get Customer 360° System Prompt
   */
  getCustomer360SystemPrompt() {
    return `You are an expert HVAC business intelligence analyst specializing in comprehensive customer profiling for Polish HVAC companies.

Your expertise includes:
- 20+ years in HVAC customer relationship management
- Deep understanding of Polish HVAC market and customer behavior
- Expert knowledge of seasonal patterns and equipment lifecycle
- Customer psychology and communication preferences
- Predictive analytics for churn, upsell, and maintenance
- Business intelligence for HVAC service optimization

Analyze customer data to create a comprehensive 360° profile that includes:

1. **Communication Profile**: How they prefer to communicate, their style, sentiment trends
2. **Technical Profile**: Their HVAC knowledge, equipment preferences, maintenance awareness
3. **Business Profile**: Budget indicators, decision-making style, payment patterns
4. **Behavioral Patterns**: Seasonal behavior, service patterns, loyalty indicators
5. **Predictive Insights**: Churn risk, upsell opportunities, maintenance needs

Focus on actionable insights that help HVAC businesses:
- Save time for business owners
- Personalize customer interactions
- Predict and prevent problems
- Optimize service delivery
- Increase customer satisfaction and retention

Consider Polish business culture, climate conditions, and HVAC market specifics.
Provide specific, actionable recommendations that busy HVAC business owners can immediately use.`;
  }

  /**
   * 📝 Build customer analysis prompt
   */
  buildCustomerAnalysisPrompt(customerData) {
    return `Analyze this comprehensive HVAC customer data to create a detailed 360° profile:

BASIC CUSTOMER INFO:
${JSON.stringify(customerData.basicInfo, null, 2)}

EMAIL INTERACTIONS (${customerData.emails?.length || 0} emails):
${customerData.emails?.map(email => `
- Subject: ${email.subject}
- Date: ${email.date}
- Sentiment: ${email.sentiment || 'unknown'}
- Key Points: ${email.summary || email.content?.substring(0, 200)}
`).join('\n') || 'No email data available'}

CALL TRANSCRIPTIONS (${customerData.transcriptions?.length || 0} calls):
${customerData.transcriptions?.map(call => `
- Date: ${call.date}
- Duration: ${call.duration}
- Emotion: ${call.emotion || 'unknown'}
- Summary: ${call.summary || call.content?.substring(0, 200)}
`).join('\n') || 'No transcription data available'}

SERVICE HISTORY (${customerData.serviceHistory?.length || 0} services):
${customerData.serviceHistory?.map(service => `
- Date: ${service.date}
- Type: ${service.type}
- Equipment: ${service.equipment}
- Satisfaction: ${service.satisfaction || 'unknown'}
- Cost: ${service.cost || 'unknown'}
`).join('\n') || 'No service history available'}

EQUIPMENT DATA:
${JSON.stringify(customerData.equipmentData, null, 2)}

CONTEXT:
- Current season: ${this.getCurrentSeason()}
- Analysis date: ${new Date().toISOString()}
- Market: Polish HVAC residential/commercial
- Business focus: Time-saving insights for HVAC business owner

ANALYSIS REQUIREMENTS:
Create a comprehensive 360° customer profile that provides:
1. Deep insights into customer behavior and preferences
2. Predictive analytics for business opportunities
3. Actionable recommendations for the HVAC business owner
4. Risk assessments and prevention strategies
5. Personalization guidelines for future interactions

Focus on practical, time-saving insights that help the business owner make quick, informed decisions about this customer.`;
  }

  /**
   * 🌍 Get current season for context
   */
  getCurrentSeason() {
    const month = new Date().getMonth();
    if (month >= 2 && month <= 4) return 'Spring (heating season ending, AC prep)';
    if (month >= 5 && month <= 7) return 'Summer (cooling season peak, high demand)';
    if (month >= 8 && month <= 10) return 'Autumn (heating season prep, maintenance)';
    return 'Winter (heating season peak, emergency calls)';
  }

  // Placeholder methods for data gathering (would integrate with actual database)
  async getBasicCustomerInfo(customerId) {
    // Would query actual customer database
    return {
      customerId,
      name: 'Jan Kowalski',
      email: '<EMAIL>',
      phone: '+**************',
      address: 'ul. Warszawska 123, 00-001 Warszawa',
      buildingType: 'residential'
    };
  }

  async getCustomerEmails(customerId) {
    // Would query email intelligence database
    return [];
  }

  async getCustomerTranscriptions(customerId) {
    // Would query transcription database
    return [];
  }

  async getServiceHistory(customerId) {
    // Would query service orders database
    return [];
  }

  async getEquipmentData(customerId) {
    // Would query equipment database
    return [];
  }

  async getPaymentHistory(customerId) {
    // Would query payment database
    return [];
  }

  async getInteractionHistory(customerId) {
    // Would query interaction database
    return [];
  }

  async analyzeInteraction(interactionData) {
    // Would analyze new interaction with AI
    return {};
  }

  async updateProfileWithInteraction(profile, insights) {
    // Would update profile with new insights
    return profile;
  }

  async generateRealTimeRecommendations(profile, insights) {
    // Would generate actionable recommendations
    return [];
  }

  async calculateChurnRisk(profile) {
    // Would calculate churn probability
    return { score: 0.2, factors: [], recommendation: '' };
  }

  async identifyUpsellOpportunities(profile) {
    // Would identify upsell opportunities
    return [];
  }

  async predictMaintenanceNeeds(profile) {
    // Would predict maintenance needs
    return [];
  }

  async recommendNextActions(profile) {
    // Would recommend next best actions
    return [];
  }

  async assembleProfile360(customerData, aiInsights) {
    // Would assemble complete profile
    return aiInsights;
  }

  async storeProfileInWeaviate(profile) {
    // Would store in Weaviate for semantic search
    try {
      await enhancedWeaviateService.storeCustomerProfile(profile);
    } catch (error) {
      logger.error('Failed to store profile in Weaviate:', error);
    }
  }
}

module.exports = new EnhancedCustomer360Service();
