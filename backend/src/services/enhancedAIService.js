const OpenAI = require('openai');
const { z } = require('zod');
const { zodToJsonSchema } = require('zod-to-json-schema');
const { Equipment } = require('@/models/appModels/Equipment');
const { ServiceOrder } = require('@/models/appModels/ServiceOrder');
const { Opportunity } = require('@/models/appModels/Opportunity');
const { Client } = require('@/models/appModels/Client');

// Zod Schemas for Structured AI Responses
const EquipmentDiagnosisSchema = z.object({
  equipmentId: z.string(),
  healthScore: z.number().min(0).max(100),
  failureProbability: z.number().min(0).max(1),
  urgency: z.enum(['low', 'medium', 'high', 'critical']),
  recommendations: z.array(z.string()),
  estimatedCost: z.number().min(0),
  nextMaintenanceDate: z.string(),
  riskFactors: z.array(z.string()),
  insights: z.string()
});

const LeadScoringSchema = z.object({
  opportunityId: z.string(),
  leadScore: z.number().min(0).max(100),
  conversionProbability: z.number().min(0).max(1),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  recommendations: z.array(z.string()),
  riskFactors: z.array(z.string()),
  strengths: z.array(z.string()),
  nextActions: z.array(z.string()),
  insights: z.string()
});

const CustomerAnalysisSchema = z.object({
  clientId: z.string(),
  healthScore: z.number().min(0).max(100),
  churnProbability: z.number().min(0).max(1),
  lifetimeValue: z.number().min(0),
  segment: z.enum(['high_value', 'medium_value', 'low_value', 'at_risk']),
  recommendations: z.array(z.string()),
  upsellOpportunities: z.array(z.string()),
  retentionStrategies: z.array(z.string()),
  insights: z.string()
});

const MaintenanceScheduleSchema = z.object({
  equipmentId: z.string(),
  scheduledDate: z.string(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  estimatedDuration: z.number().min(0),
  estimatedCost: z.number().min(0),
  requiredParts: z.array(z.string()),
  technician: z.string().optional(),
  description: z.string(),
  preventiveMeasures: z.array(z.string())
});

class EnhancedAIService {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  /**
   * ADVANCED EQUIPMENT DIAGNOSTICS WITH FUNCTION CALLING
   * Uses OpenAI's function calling to perform comprehensive equipment analysis
   */
  async performAdvancedEquipmentDiagnostics(equipmentId) {
    try {
      const equipment = await Equipment.findById(equipmentId).populate('client');
      if (!equipment) throw new Error('Equipment not found');

      const serviceHistory = await ServiceOrder.find({ 
        equipment: equipmentId 
      }).sort({ created: -1 }).limit(20);

      const runner = this.openai.chat.completions.runTools({
        model: 'gpt-4-1106-preview', // Latest GPT-4.1 with 128k context
        messages: [
          {
            role: 'system',
            content: `You are an expert HVAC diagnostician with 20+ years of experience. 
            Analyze equipment data and provide comprehensive diagnostics with actionable recommendations.
            Focus on Polish HVAC market conditions and regulations.`
          },
          {
            role: 'user',
            content: `Perform comprehensive diagnostics for HVAC equipment:
            
            Equipment Details:
            - Name: ${equipment.name}
            - Type: ${equipment.type}
            - Manufacturer: ${equipment.manufacturer}
            - Model: ${equipment.model}
            - Age: ${this.calculateEquipmentAge(equipment.installationDate)} years
            - Last Maintenance: ${equipment.lastMaintenanceDate?.toLocaleDateString('pl-PL') || 'Unknown'}
            - Current Health Score: ${equipment.healthScore || 'Not assessed'}
            
            Service History (last 20 services):
            ${serviceHistory.map(s => `- ${s.created.toLocaleDateString('pl-PL')}: ${s.title} (${s.stage})`).join('\n')}
            
            Provide detailed analysis with health score, failure probability, and specific recommendations.`
          }
        ],
        tools: [
          {
            type: 'function',
            function: {
              name: 'analyzeEquipmentHealth',
              description: 'Analyze equipment health and provide diagnostic insights',
              parameters: zodToJsonSchema(EquipmentDiagnosisSchema)
            }
          },
          {
            type: 'function',
            function: {
              name: 'scheduleMaintenanceTask',
              description: 'Schedule maintenance based on diagnostic results',
              parameters: zodToJsonSchema(MaintenanceScheduleSchema)
            }
          },
          {
            type: 'function',
            function: {
              name: 'calculateMaintenanceCost',
              description: 'Calculate estimated maintenance costs',
              parameters: z.object({
                equipmentType: z.string(),
                urgency: z.enum(['low', 'medium', 'high', 'critical']),
                requiredActions: z.array(z.string())
              })
            }
          }
        ]
      });

      const result = await runner.finalContent();
      const toolCalls = await runner.finalFunctionCall();

      // Update equipment with AI insights
      await Equipment.findByIdAndUpdate(equipmentId, {
        aiInsights: {
          lastAnalysis: new Date(),
          diagnostics: result,
          toolCalls: toolCalls,
          confidence: 0.95
        }
      });

      return {
        diagnostics: result,
        toolCalls: toolCalls,
        confidence: 0.95,
        timestamp: new Date()
      };

    } catch (error) {
      console.error('Advanced equipment diagnostics error:', error);
      throw error;
    }
  }

  /**
   * INTELLIGENT LEAD SCORING WITH STRUCTURED OUTPUT
   * Uses structured output for type-safe lead analysis
   */
  async performIntelligentLeadScoring(opportunityId) {
    try {
      const opportunity = await Opportunity.findById(opportunityId).populate('client');
      if (!opportunity) throw new Error('Opportunity not found');

      const client = opportunity.client;
      const clientEquipment = await Equipment.find({ client: client._id });
      const clientHistory = await ServiceOrder.find({ client: client._id }).sort({ created: -1 }).limit(10);

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4o-2024-08-06',
        messages: [
          {
            role: 'system',
            content: `You are an expert HVAC sales analyst specializing in the Polish market. 
            Analyze leads with deep understanding of HVAC industry dynamics, seasonal patterns, 
            and customer behavior in Poland. Provide actionable insights for sales teams.`
          },
          {
            role: 'user',
            content: `Analyze this HVAC sales opportunity for intelligent lead scoring:

            Opportunity Details:
            - Name: ${opportunity.name}
            - Value: ${opportunity.value?.toLocaleString('pl-PL')} PLN
            - Stage: ${opportunity.stage}
            - Service Type: ${opportunity.serviceType}
            - Complexity: ${opportunity.installationComplexity}
            - Expected Close: ${opportunity.expectedCloseDate?.toLocaleDateString('pl-PL')}
            
            Client Profile:
            - Name: ${client.name}
            - Building Type: ${client.buildingType}
            - Contract Type: ${client.contractType}
            - Priority: ${client.priority}
            - Existing Equipment: ${clientEquipment.length} units
            
            Service History:
            ${clientHistory.map(s => `- ${s.created.toLocaleDateString('pl-PL')}: ${s.title} (${s.customerSatisfaction || 'N/A'}/5)`).join('\n')}
            
            Current Season: ${this.getCurrentSeason()}
            Market Conditions: High demand for energy-efficient solutions
            
            Provide comprehensive lead scoring with specific recommendations for Polish HVAC market.`
          }
        ],
        response_format: {
          type: 'json_schema',
          json_schema: {
            name: 'lead_scoring_analysis',
            schema: zodToJsonSchema(LeadScoringSchema)
          }
        }
      });

      const analysis = JSON.parse(completion.choices[0].message.content);

      // Update opportunity with AI insights
      await Opportunity.findByIdAndUpdate(opportunityId, {
        leadScore: analysis.leadScore,
        aiInsights: {
          analysis,
          lastAnalysis: new Date(),
          confidence: 0.92
        }
      });

      return analysis;

    } catch (error) {
      console.error('Intelligent lead scoring error:', error);
      throw error;
    }
  }

  /**
   * STREAMING AI INSIGHTS FOR REAL-TIME DASHBOARD
   * Provides streaming AI insights for live dashboard updates
   */
  async *streamDashboardInsights() {
    try {
      const stream = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: 'You are an AI business intelligence analyst for HVAC operations. Provide real-time insights and recommendations.'
          },
          {
            role: 'user',
            content: 'Generate real-time business insights for HVAC CRM dashboard. Include equipment alerts, sales opportunities, and operational recommendations.'
          }
        ],
        stream: true
      });

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content;
        if (content) {
          yield {
            type: 'insight',
            content,
            timestamp: new Date()
          };
        }
      }

    } catch (error) {
      console.error('Streaming insights error:', error);
      throw error;
    }
  }

  /**
   * COMPREHENSIVE CUSTOMER ANALYSIS WITH AI
   * Advanced customer intelligence with structured output
   */
  async performComprehensiveCustomerAnalysis(clientId) {
    try {
      const client = await Client.findById(clientId);
      if (!client) throw new Error('Client not found');

      const equipment = await Equipment.find({ client: clientId });
      const serviceOrders = await ServiceOrder.find({ client: clientId }).sort({ created: -1 });
      const opportunities = await Opportunity.find({ client: clientId }).sort({ created: -1 });

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4o-2024-08-06',
        messages: [
          {
            role: 'system',
            content: `You are a customer success expert specializing in HVAC business relationships. 
            Analyze customer data to provide actionable insights for retention, growth, and satisfaction.
            Focus on Polish market dynamics and HVAC industry best practices.`
          },
          {
            role: 'user',
            content: `Perform comprehensive customer analysis:

            Customer Profile:
            - Name: ${client.name}
            - Building Type: ${client.buildingType}
            - Service Area: ${client.serviceArea}
            - Contract Type: ${client.contractType}
            - Current Health Score: ${client.healthScore || 'Not assessed'}
            
            Equipment Portfolio:
            - Total Units: ${equipment.length}
            - Average Age: ${this.calculateAverageEquipmentAge(equipment)} years
            - Health Distribution: ${this.getEquipmentHealthDistribution(equipment)}
            
            Service History:
            - Total Orders: ${serviceOrders.length}
            - Completed: ${serviceOrders.filter(s => s.stage === 'COMPLETED').length}
            - Average Satisfaction: ${this.calculateAverageSatisfaction(serviceOrders)}/5
            
            Sales History:
            - Total Opportunities: ${opportunities.length}
            - Won Deals: ${opportunities.filter(o => o.stage === 'CLOSED_WON').length}
            - Total Value: ${opportunities.reduce((sum, o) => sum + (o.value || 0), 0).toLocaleString('pl-PL')} PLN
            
            Provide detailed customer analysis with segmentation, retention strategies, and growth opportunities.`
          }
        ],
        response_format: {
          type: 'json_schema',
          json_schema: {
            name: 'customer_analysis',
            schema: zodToJsonSchema(CustomerAnalysisSchema)
          }
        }
      });

      const analysis = JSON.parse(completion.choices[0].message.content);

      // Update client with AI insights
      await Client.findByIdAndUpdate(clientId, {
        healthScore: analysis.healthScore,
        churnProbability: analysis.churnProbability,
        lifetimeValue: analysis.lifetimeValue,
        aiInsights: {
          analysis,
          lastAnalysis: new Date(),
          confidence: 0.94
        }
      });

      return analysis;

    } catch (error) {
      console.error('Comprehensive customer analysis error:', error);
      throw error;
    }
  }

  /**
   * PREDICTIVE MAINTENANCE SCHEDULER WITH AI
   * Uses AI to optimize maintenance scheduling across all equipment
   */
  async generatePredictiveMaintenanceSchedule() {
    try {
      const equipment = await Equipment.find({ enabled: true }).populate('client');
      
      const runner = this.openai.chat.completions.runTools({
        model: 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: `You are an expert HVAC maintenance scheduler with deep knowledge of Polish climate conditions, 
            equipment lifecycles, and optimal maintenance timing. Create efficient maintenance schedules that 
            minimize downtime and maximize equipment lifespan.`
          },
          {
            role: 'user',
            content: `Create optimized maintenance schedule for ${equipment.length} HVAC units:

            Equipment Summary:
            ${equipment.map(eq => `- ${eq.name} (${eq.type}, ${this.calculateEquipmentAge(eq.installationDate)}y old, Health: ${eq.healthScore || 'N/A'})`).join('\n')}
            
            Current Season: ${this.getCurrentSeason()}
            Technician Availability: Standard (Mon-Fri 8-16)
            Priority: Minimize emergency calls, optimize costs
            
            Generate comprehensive maintenance schedule with priorities and resource allocation.`
          }
        ],
        tools: [
          {
            type: 'function',
            function: {
              name: 'scheduleMaintenanceTask',
              description: 'Schedule individual maintenance task',
              parameters: zodToJsonSchema(MaintenanceScheduleSchema)
            }
          },
          {
            type: 'function',
            function: {
              name: 'optimizeSchedule',
              description: 'Optimize overall maintenance schedule',
              parameters: z.object({
                totalTasks: z.number(),
                estimatedCost: z.number(),
                timeframe: z.string(),
                priorities: z.array(z.string()),
                resourceRequirements: z.array(z.string())
              })
            }
          }
        ]
      });

      const schedule = await runner.finalContent();
      const optimizations = await runner.finalFunctionCall();

      return {
        schedule,
        optimizations,
        generatedAt: new Date(),
        equipmentCount: equipment.length
      };

    } catch (error) {
      console.error('Predictive maintenance scheduling error:', error);
      throw error;
    }
  }

  // Helper Methods
  calculateEquipmentAge(installationDate) {
    if (!installationDate) return 0;
    return Math.floor((Date.now() - installationDate.getTime()) / (1000 * 60 * 60 * 24 * 365));
  }

  calculateAverageEquipmentAge(equipment) {
    if (equipment.length === 0) return 0;
    const totalAge = equipment.reduce((sum, eq) => sum + this.calculateEquipmentAge(eq.installationDate), 0);
    return Math.round(totalAge / equipment.length);
  }

  getEquipmentHealthDistribution(equipment) {
    const healthy = equipment.filter(eq => (eq.healthScore || 50) >= 80).length;
    const medium = equipment.filter(eq => (eq.healthScore || 50) >= 50 && (eq.healthScore || 50) < 80).length;
    const poor = equipment.filter(eq => (eq.healthScore || 50) < 50).length;
    return `${healthy} healthy, ${medium} medium, ${poor} poor`;
  }

  calculateAverageSatisfaction(serviceOrders) {
    const withSatisfaction = serviceOrders.filter(s => s.customerSatisfaction);
    if (withSatisfaction.length === 0) return 0;
    const total = withSatisfaction.reduce((sum, s) => sum + s.customerSatisfaction, 0);
    return Math.round((total / withSatisfaction.length) * 10) / 10;
  }

  getCurrentSeason() {
    const month = new Date().getMonth();
    if (month >= 2 && month <= 4) return 'Spring';
    if (month >= 5 && month <= 7) return 'Summer';
    if (month >= 8 && month <= 10) return 'Autumn';
    return 'Winter';
  }
}

module.exports = new EnhancedAIService();
