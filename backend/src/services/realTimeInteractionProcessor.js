/**
 * 🔄 REAL-TIME INTERACTION PROCESSOR 2025 - COSMIC LEVEL AUTOMATION
 * 
 * Automatycznie przetwarza każdą interakcję klienta i aktualizuje profil 360°:
 * - Emaile w czasie rzeczywistym
 * - Transkrypcje rozmów telefonicznych
 * - Interakcje CRM (zlecenia, płatności, reklamacje)
 * - Wizyty techniczne i protokoły serwisowe
 * 
 * Features:
 * - Real-time processing z event-driven architecture
 * - AI-powered sentiment i emotion analysis
 * - Automatic profile updates w Weaviate
 * - Predictive insights generation
 * - Business owner notifications
 * - Time-saving automation
 */

const EventEmitter = require('events');
const enhancedCustomer360Service = require('./enhancedCustomer360Service');
const enhancedEmailIntelligenceService = require('./enhancedEmailIntelligenceService');
const enhancedAIService = require('./enhancedAIService');
const logger = require('../utils/logger');

class RealTimeInteractionProcessor extends EventEmitter {
  constructor() {
    super();
    this.processingQueue = [];
    this.isProcessing = false;
    this.setupEventListeners();
  }

  /**
   * 🎧 Setup event listeners for different interaction types
   */
  setupEventListeners() {
    // Email interactions
    this.on('email_received', this.processEmailInteraction.bind(this));
    this.on('email_sent', this.processEmailInteraction.bind(this));
    
    // Call interactions
    this.on('call_completed', this.processCallInteraction.bind(this));
    this.on('transcription_ready', this.processTranscriptionInteraction.bind(this));
    
    // CRM interactions
    this.on('service_order_created', this.processServiceInteraction.bind(this));
    this.on('service_order_updated', this.processServiceInteraction.bind(this));
    this.on('payment_received', this.processPaymentInteraction.bind(this));
    this.on('complaint_filed', this.processComplaintInteraction.bind(this));
    
    // Technical interactions
    this.on('site_visit_completed', this.processSiteVisitInteraction.bind(this));
    this.on('equipment_installed', this.processEquipmentInteraction.bind(this));
    this.on('maintenance_completed', this.processMaintenanceInteraction.bind(this));

    logger.info('🎧 Real-time interaction processor initialized');
  }

  /**
   * 📧 Process email interaction
   */
  async processEmailInteraction(emailData) {
    try {
      logger.info(`📧 Processing email interaction: ${emailData.subject}`);

      // Extract customer ID from email
      const customerId = await this.extractCustomerIdFromEmail(emailData);
      if (!customerId) {
        logger.warn('❌ Could not identify customer from email');
        return;
      }

      // Analyze email with enhanced AI
      const emailAnalysis = await enhancedEmailIntelligenceService.analyzeEmail(emailData);
      
      // Create interaction record
      const interaction = {
        type: 'email',
        customerId: customerId,
        timestamp: new Date(),
        data: emailData,
        analysis: emailAnalysis,
        source: 'email_processor'
      };

      // Update customer 360° profile
      const profileUpdate = await enhancedCustomer360Service.processNewInteraction(
        customerId, 
        interaction
      );

      // Generate business owner insights
      const businessInsights = await this.generateBusinessOwnerInsights(
        customerId,
        interaction,
        profileUpdate
      );

      // Send notifications if needed
      await this.sendNotificationsIfNeeded(businessInsights);

      logger.info(`✅ Email interaction processed successfully for customer: ${customerId}`);
      
      return {
        customerId,
        interaction,
        profileUpdate,
        businessInsights
      };

    } catch (error) {
      logger.error(`❌ Failed to process email interaction: ${error.message}`);
      throw error;
    }
  }

  /**
   * 📞 Process call/transcription interaction
   */
  async processCallInteraction(callData) {
    try {
      logger.info(`📞 Processing call interaction: ${callData.duration}min call`);

      const customerId = await this.extractCustomerIdFromCall(callData);
      if (!customerId) {
        logger.warn('❌ Could not identify customer from call');
        return;
      }

      // Analyze transcription with AI
      const transcriptionAnalysis = await this.analyzeTranscription(callData.transcription);
      
      // Extract emotional insights
      const emotionalInsights = await this.analyzeCallEmotions(callData);
      
      // Create interaction record
      const interaction = {
        type: 'call',
        customerId: customerId,
        timestamp: new Date(),
        data: callData,
        analysis: transcriptionAnalysis,
        emotions: emotionalInsights,
        source: 'call_processor'
      };

      // Update customer 360° profile
      const profileUpdate = await enhancedCustomer360Service.processNewInteraction(
        customerId, 
        interaction
      );

      // Generate business insights
      const businessInsights = await this.generateBusinessOwnerInsights(
        customerId,
        interaction,
        profileUpdate
      );

      logger.info(`✅ Call interaction processed successfully for customer: ${customerId}`);
      
      return {
        customerId,
        interaction,
        profileUpdate,
        businessInsights
      };

    } catch (error) {
      logger.error(`❌ Failed to process call interaction: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🔧 Process service interaction
   */
  async processServiceInteraction(serviceData) {
    try {
      logger.info(`🔧 Processing service interaction: ${serviceData.type}`);

      const customerId = serviceData.customerId;
      
      // Analyze service data
      const serviceAnalysis = await this.analyzeServiceData(serviceData);
      
      // Create interaction record
      const interaction = {
        type: 'service',
        customerId: customerId,
        timestamp: new Date(),
        data: serviceData,
        analysis: serviceAnalysis,
        source: 'service_processor'
      };

      // Update customer 360° profile
      const profileUpdate = await enhancedCustomer360Service.processNewInteraction(
        customerId, 
        interaction
      );

      // Check for upsell opportunities
      const upsellOpportunities = await this.identifyServiceUpsellOpportunities(
        serviceData,
        profileUpdate.updatedProfile
      );

      // Generate business insights
      const businessInsights = await this.generateBusinessOwnerInsights(
        customerId,
        interaction,
        profileUpdate,
        { upsellOpportunities }
      );

      logger.info(`✅ Service interaction processed successfully for customer: ${customerId}`);
      
      return {
        customerId,
        interaction,
        profileUpdate,
        businessInsights,
        upsellOpportunities
      };

    } catch (error) {
      logger.error(`❌ Failed to process service interaction: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🧠 Generate business owner insights
   */
  async generateBusinessOwnerInsights(customerId, interaction, profileUpdate, additionalData = {}) {
    try {
      const insights = {
        customerId,
        timestamp: new Date(),
        interactionSummary: this.summarizeInteraction(interaction),
        profileChanges: this.summarizeProfileChanges(profileUpdate),
        actionableInsights: [],
        alerts: [],
        opportunities: additionalData.upsellOpportunities || [],
        timeToAction: this.calculateTimeToAction(interaction, profileUpdate)
      };

      // Generate AI-powered insights
      const aiInsights = await this.generateAIBusinessInsights(
        interaction,
        profileUpdate.updatedProfile
      );

      insights.actionableInsights = aiInsights.actionableInsights;
      insights.alerts = aiInsights.alerts;

      // Check for immediate actions needed
      if (this.requiresImmediateAttention(interaction, profileUpdate)) {
        insights.alerts.push({
          type: 'immediate_attention',
          message: 'Ten klient wymaga natychmiastowej uwagi',
          priority: 'high',
          suggestedAction: 'Skontaktuj się w ciągu 2 godzin'
        });
      }

      return insights;

    } catch (error) {
      logger.error(`❌ Failed to generate business insights: ${error.message}`);
      return { error: error.message };
    }
  }

  /**
   * 🤖 Generate AI-powered business insights
   */
  async generateAIBusinessInsights(interaction, customerProfile) {
    try {
      const prompt = `
Jako ekspert HVAC business intelligence, przeanalizuj tę interakcję klienta i wygeneruj actionable insights dla właściciela firmy HVAC:

INTERAKCJA:
Typ: ${interaction.type}
Data: ${interaction.timestamp}
Analiza: ${JSON.stringify(interaction.analysis, null, 2)}

PROFIL KLIENTA 360°:
${JSON.stringify(customerProfile, null, 2)}

WYGENERUJ:
1. 3 najważniejsze actionable insights (co właściciel powinien zrobić)
2. Potencjalne alerty (problemy wymagające uwagi)
3. Możliwości biznesowe (upsell, cross-sell)
4. Rekomendacje czasowe (kiedy działać)

Skup się na oszczędności czasu i zwiększeniu zysków.
`;

      const response = await enhancedAIService.generateBusinessInsights(prompt);
      
      return {
        actionableInsights: response.insights || [],
        alerts: response.alerts || []
      };

    } catch (error) {
      logger.error(`❌ Failed to generate AI business insights: ${error.message}`);
      return { actionableInsights: [], alerts: [] };
    }
  }

  /**
   * 📊 Analyze transcription with AI
   */
  async analyzeTranscription(transcriptionText) {
    try {
      if (!transcriptionText) return {};

      // Use enhanced AI service for transcription analysis
      const analysis = await enhancedAIService.analyzeTranscription(transcriptionText);
      
      return {
        sentiment: analysis.sentiment,
        keyTopics: analysis.keyTopics,
        technicalIssues: analysis.technicalIssues,
        customerNeeds: analysis.customerNeeds,
        urgency: analysis.urgency,
        satisfaction: analysis.satisfaction
      };

    } catch (error) {
      logger.error(`❌ Failed to analyze transcription: ${error.message}`);
      return {};
    }
  }

  /**
   * 😊 Analyze call emotions
   */
  async analyzeCallEmotions(callData) {
    try {
      // This would integrate with emotion detection API
      // For now, we'll simulate emotion analysis
      
      const emotions = {
        primary: 'neutral',
        confidence: 0.8,
        emotionTimeline: [],
        stressLevel: 'medium',
        satisfactionLevel: 'good'
      };

      return emotions;

    } catch (error) {
      logger.error(`❌ Failed to analyze call emotions: ${error.message}`);
      return {};
    }
  }

  /**
   * 🔍 Extract customer ID from email
   */
  async extractCustomerIdFromEmail(emailData) {
    try {
      // This would query customer database by email
      // For now, we'll simulate customer lookup
      
      const customerEmail = emailData.from || emailData.sender;
      if (!customerEmail) return null;

      // Simulate database lookup
      // In real implementation, this would query the customer database
      return 'customer_123'; // Placeholder

    } catch (error) {
      logger.error(`❌ Failed to extract customer ID from email: ${error.message}`);
      return null;
    }
  }

  /**
   * 📞 Extract customer ID from call
   */
  async extractCustomerIdFromCall(callData) {
    try {
      // This would query customer database by phone number
      const phoneNumber = callData.phoneNumber || callData.from;
      if (!phoneNumber) return null;

      // Simulate database lookup
      return 'customer_123'; // Placeholder

    } catch (error) {
      logger.error(`❌ Failed to extract customer ID from call: ${error.message}`);
      return null;
    }
  }

  /**
   * 🔧 Analyze service data
   */
  async analyzeServiceData(serviceData) {
    try {
      return {
        serviceType: serviceData.type,
        complexity: this.assessServiceComplexity(serviceData),
        customerSatisfaction: serviceData.satisfaction || 'unknown',
        technicalIssues: serviceData.issues || [],
        equipmentInvolved: serviceData.equipment || [],
        cost: serviceData.cost || 0,
        duration: serviceData.duration || 0
      };

    } catch (error) {
      logger.error(`❌ Failed to analyze service data: ${error.message}`);
      return {};
    }
  }

  /**
   * 💰 Identify service upsell opportunities
   */
  async identifyServiceUpsellOpportunities(serviceData, customerProfile) {
    try {
      const opportunities = [];

      // Example upsell logic
      if (serviceData.type === 'maintenance' && !customerProfile.hasMaintenanceContract) {
        opportunities.push({
          type: 'maintenance_contract',
          value: 2000,
          probability: 0.7,
          description: 'Klient może być zainteresowany kontraktem serwisowym'
        });
      }

      return opportunities;

    } catch (error) {
      logger.error(`❌ Failed to identify upsell opportunities: ${error.message}`);
      return [];
    }
  }

  /**
   * 📋 Summarize interaction
   */
  summarizeInteraction(interaction) {
    return {
      type: interaction.type,
      timestamp: interaction.timestamp,
      key_points: interaction.analysis?.keyPoints || [],
      sentiment: interaction.analysis?.sentiment || 'unknown'
    };
  }

  /**
   * 📊 Summarize profile changes
   */
  summarizeProfileChanges(profileUpdate) {
    return {
      updated_fields: ['communication_style', 'sentiment_trend', 'last_interaction'],
      new_insights: profileUpdate.recommendations || [],
      risk_changes: profileUpdate.riskChanges || []
    };
  }

  /**
   * ⏰ Calculate time to action
   */
  calculateTimeToAction(interaction, profileUpdate) {
    // Logic to determine how quickly business owner should respond
    const urgency = interaction.analysis?.urgency || 'medium';
    
    const timeMap = {
      'critical': '15 minutes',
      'high': '2 hours',
      'medium': '24 hours',
      'low': '3 days'
    };

    return timeMap[urgency] || '24 hours';
  }

  /**
   * 🚨 Check if requires immediate attention
   */
  requiresImmediateAttention(interaction, profileUpdate) {
    const urgentKeywords = ['awaria', 'emergency', 'pilne', 'nie działa', 'problem'];
    const content = JSON.stringify(interaction.data).toLowerCase();
    
    return urgentKeywords.some(keyword => content.includes(keyword));
  }

  /**
   * 📱 Send notifications if needed
   */
  async sendNotificationsIfNeeded(businessInsights) {
    try {
      if (businessInsights.alerts && businessInsights.alerts.length > 0) {
        // This would integrate with notification system
        logger.info(`📱 Sending ${businessInsights.alerts.length} notifications to business owner`);
      }
    } catch (error) {
      logger.error(`❌ Failed to send notifications: ${error.message}`);
    }
  }

  /**
   * 🔧 Assess service complexity
   */
  assessServiceComplexity(serviceData) {
    // Simple complexity assessment logic
    const factors = [
      serviceData.duration > 4 ? 1 : 0, // Long duration
      serviceData.equipment?.length > 2 ? 1 : 0, // Multiple equipment
      serviceData.cost > 5000 ? 1 : 0, // High cost
      serviceData.issues?.length > 1 ? 1 : 0 // Multiple issues
    ];

    const score = factors.reduce((sum, factor) => sum + factor, 0);
    
    if (score >= 3) return 'high';
    if (score >= 2) return 'medium';
    return 'low';
  }

  /**
   * 🚀 Start processing queue
   */
  startProcessing() {
    this.isProcessing = true;
    logger.info('🚀 Real-time interaction processor started');
  }

  /**
   * ⏹️ Stop processing queue
   */
  stopProcessing() {
    this.isProcessing = false;
    logger.info('⏹️ Real-time interaction processor stopped');
  }
}

module.exports = new RealTimeInteractionProcessor();
