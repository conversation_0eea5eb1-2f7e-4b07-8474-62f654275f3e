/**
 * 🚀 ENHANCED WEAVIATE SERVICE 2025 - <PERSON><PERSON><PERSON> LEVEL INTELLIGENCE
 * 
 * <PERSON>yk<PERSON>zy<PERSON><PERSON><PERSON> najn<PERSON>zy Weaviate Client v3.5.3 z gRPC support
 * Zapewnia semantic search, customer profiling i AI-powered insights
 * 
 * Features:
 * - Advanced customer profiling z 360-degree view
 * - Semantic email intelligence
 * - HVAC equipment knowledge base
 * - Real-time vector search
 * - AI-powered recommendations
 */

const weaviate = require('weaviate-client');
const logger = require('../utils/logger');

class EnhancedWeaviateService {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.collections = {
      customers: 'HVACCustomers',
      emails: 'EmailIntelligence', 
      equipment: 'HVACEquipment',
      interactions: 'CustomerInteractions',
      insights: 'BusinessInsights'
    };
  }

  /**
   * 🔌 Initialize Weaviate connection with v3.5.3 client
   */
  async initialize() {
    try {
      this.client = weaviate.connectToLocal({
        host: process.env.WEAVIATE_HOST || 'localhost',
        port: process.env.WEAVIATE_PORT || 8080,
        grpc: {
          host: process.env.WEAVIATE_GRPC_HOST || 'localhost',
          port: process.env.WEAVIATE_GRPC_PORT || 50051,
        }
      });

      // Test connection
      const isReady = await this.client.isReady();
      if (isReady) {
        this.isConnected = true;
        logger.info('🚀 Enhanced Weaviate Service connected successfully');
        await this.ensureCollections();
      } else {
        throw new Error('Weaviate not ready');
      }
    } catch (error) {
      logger.error('❌ Weaviate connection failed:', error);
      this.isConnected = false;
    }
  }

  /**
   * 🏗️ Ensure all required collections exist with proper schemas
   */
  async ensureCollections() {
    try {
      // HVAC Customers Collection
      await this.createCollection(this.collections.customers, {
        description: 'HVAC customers with enhanced profiling',
        properties: [
          { name: 'name', dataType: 'text' },
          { name: 'email', dataType: 'text' },
          { name: 'phone', dataType: 'text' },
          { name: 'address', dataType: 'text' },
          { name: 'buildingType', dataType: 'text' },
          { name: 'heatingSystem', dataType: 'text' },
          { name: 'coolingSystem', dataType: 'text' },
          { name: 'serviceArea', dataType: 'text' },
          { name: 'healthScore', dataType: 'number' },
          { name: 'churnProbability', dataType: 'number' },
          { name: 'lifetimeValue', dataType: 'number' },
          { name: 'communicationStyle', dataType: 'text' },
          { name: 'technicalKnowledge', dataType: 'text' },
          { name: 'budgetIndicator', dataType: 'text' },
          { name: 'lastInteraction', dataType: 'date' },
          { name: 'preferredBrands', dataType: 'text[]' },
          { name: 'seasonalPatterns', dataType: 'text' },
          { name: 'satisfactionLevel', dataType: 'number' }
        ]
      });

      // Email Intelligence Collection  
      await this.createCollection(this.collections.emails, {
        description: 'AI-processed email intelligence',
        properties: [
          { name: 'subject', dataType: 'text' },
          { name: 'content', dataType: 'text' },
          { name: 'sender', dataType: 'text' },
          { name: 'recipient', dataType: 'text' },
          { name: 'timestamp', dataType: 'date' },
          { name: 'sentiment', dataType: 'text' },
          { name: 'urgency', dataType: 'text' },
          { name: 'category', dataType: 'text' },
          { name: 'extractedEntities', dataType: 'text[]' },
          { name: 'actionItems', dataType: 'text[]' },
          { name: 'customerNeeds', dataType: 'text[]' },
          { name: 'technicalIssues', dataType: 'text[]' },
          { name: 'budgetMentions', dataType: 'text[]' },
          { name: 'timelineMentions', dataType: 'text[]' },
          { name: 'competitorMentions', dataType: 'text[]' },
          { name: 'satisfactionIndicators', dataType: 'text[]' }
        ]
      });

      logger.info('✅ All Weaviate collections ensured');
    } catch (error) {
      logger.error('❌ Error ensuring collections:', error);
    }
  }

  /**
   * 🏗️ Create collection with schema
   */
  async createCollection(name, schema) {
    try {
      const collection = this.client.collections.get(name);
      const exists = await collection.exists();
      
      if (!exists) {
        await this.client.collections.create({
          name: name,
          description: schema.description,
          properties: schema.properties,
          vectorizer: 'text2vec-openai',
          moduleConfig: {
            'text2vec-openai': {
              model: 'text-embedding-3-large',
              dimensions: 3072,
              type: 'text'
            }
          }
        });
        logger.info(`✅ Created collection: ${name}`);
      }
    } catch (error) {
      logger.error(`❌ Error creating collection ${name}:`, error);
    }
  }

  /**
   * 👤 Store enhanced customer profile
   */
  async storeCustomerProfile(customerData) {
    try {
      if (!this.isConnected) await this.initialize();
      
      const collection = this.client.collections.get(this.collections.customers);
      
      const result = await collection.data.insert({
        properties: {
          name: customerData.name,
          email: customerData.email,
          phone: customerData.phone,
          address: customerData.address,
          buildingType: customerData.buildingType,
          heatingSystem: customerData.heatingSystem,
          coolingSystem: customerData.coolingSystem,
          serviceArea: customerData.serviceArea,
          healthScore: customerData.healthScore || 75,
          churnProbability: customerData.churnProbability || 0.2,
          lifetimeValue: customerData.lifetimeValue || 0,
          communicationStyle: customerData.communicationStyle || 'professional',
          technicalKnowledge: customerData.technicalKnowledge || 'basic',
          budgetIndicator: customerData.budgetIndicator || 'medium',
          lastInteraction: new Date().toISOString(),
          preferredBrands: customerData.preferredBrands || [],
          seasonalPatterns: customerData.seasonalPatterns || 'standard',
          satisfactionLevel: customerData.satisfactionLevel || 4.0
        }
      });

      logger.info(`✅ Customer profile stored: ${customerData.name}`);
      return result;
    } catch (error) {
      logger.error('❌ Error storing customer profile:', error);
      throw error;
    }
  }

  /**
   * 📧 Store email intelligence
   */
  async storeEmailIntelligence(emailData) {
    try {
      if (!this.isConnected) await this.initialize();
      
      const collection = this.client.collections.get(this.collections.emails);
      
      const result = await collection.data.insert({
        properties: {
          subject: emailData.subject,
          content: emailData.content,
          sender: emailData.sender,
          recipient: emailData.recipient,
          timestamp: emailData.timestamp || new Date().toISOString(),
          sentiment: emailData.sentiment || 'neutral',
          urgency: emailData.urgency || 'medium',
          category: emailData.category || 'general',
          extractedEntities: emailData.extractedEntities || [],
          actionItems: emailData.actionItems || [],
          customerNeeds: emailData.customerNeeds || [],
          technicalIssues: emailData.technicalIssues || [],
          budgetMentions: emailData.budgetMentions || [],
          timelineMentions: emailData.timelineMentions || [],
          competitorMentions: emailData.competitorMentions || [],
          satisfactionIndicators: emailData.satisfactionIndicators || []
        }
      });

      logger.info(`✅ Email intelligence stored: ${emailData.subject}`);
      return result;
    } catch (error) {
      logger.error('❌ Error storing email intelligence:', error);
      throw error;
    }
  }

  /**
   * 🔍 Semantic search for customer insights
   */
  async searchCustomerInsights(query, limit = 10) {
    try {
      if (!this.isConnected) await this.initialize();
      
      const collection = this.client.collections.get(this.collections.customers);
      
      const result = await collection.query.nearText(query, {
        limit: limit,
        returnMetadata: ['score', 'distance']
      });

      logger.info(`🔍 Customer insights search completed: ${result.objects.length} results`);
      return result.objects;
    } catch (error) {
      logger.error('❌ Error searching customer insights:', error);
      throw error;
    }
  }

  /**
   * 📊 Get customer 360-degree view
   */
  async getCustomer360View(customerEmail) {
    try {
      if (!this.isConnected) await this.initialize();
      
      // Get customer profile
      const customerCollection = this.client.collections.get(this.collections.customers);
      const customerResult = await customerCollection.query.where({
        path: 'email',
        operator: 'Equal',
        valueText: customerEmail
      });

      // Get related emails
      const emailCollection = this.client.collections.get(this.collections.emails);
      const emailResult = await emailCollection.query.where({
        path: 'sender',
        operator: 'Equal', 
        valueText: customerEmail
      });

      const customer360 = {
        profile: customerResult.objects[0] || null,
        emails: emailResult.objects || [],
        totalInteractions: emailResult.objects.length,
        lastInteraction: emailResult.objects[0]?.properties.timestamp || null,
        insights: await this.generateCustomerInsights(customerEmail)
      };

      logger.info(`📊 Customer 360 view generated for: ${customerEmail}`);
      return customer360;
    } catch (error) {
      logger.error('❌ Error generating customer 360 view:', error);
      throw error;
    }
  }

  /**
   * 🧠 Generate AI-powered customer insights
   */
  async generateCustomerInsights(customerEmail) {
    try {
      // This would integrate with OpenAI for advanced insights
      const insights = {
        healthScore: 85,
        churnRisk: 'low',
        upsellOpportunities: ['heat pump upgrade', 'smart thermostat'],
        communicationPreferences: 'email, technical details',
        nextBestAction: 'schedule maintenance check',
        predictedLifetimeValue: 15000
      };

      return insights;
    } catch (error) {
      logger.error('❌ Error generating customer insights:', error);
      return {};
    }
  }
}

module.exports = new EnhancedWeaviateService();
