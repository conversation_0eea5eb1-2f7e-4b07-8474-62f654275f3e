/**
 * 🚀 ENHANCED INTELLIGENCE ROUTES 2025 - <PERSON><PERSON><PERSON> LEVEL API
 * 
 * Advanced API endpoints for Email Intelligence & Weaviate integration
 * Wykorzyst<PERSON><PERSON> najnowsze AI services z GPT-4.1 i Weaviate v3.5.3
 * 
 * Endpoints:
 * - POST /api/intelligence/email/analyze - Analyze single email
 * - POST /api/intelligence/email/batch - Batch email processing
 * - GET /api/intelligence/email/search - Semantic email search
 * - GET /api/intelligence/dashboard - Intelligence dashboard data
 * - GET /api/intelligence/customer/:id/360 - Customer 360 view
 * - POST /api/intelligence/weaviate/sync - Sync data to Weaviate
 */

const express = require('express');
const router = express.Router();
const enhancedEmailIntelligenceService = require('../services/enhancedEmailIntelligenceService');
const enhancedWeaviateService = require('../services/enhancedWeaviateService');
const enhancedAIService = require('../services/enhancedAIService');
const enhancedCustomer360Service = require('../services/enhancedCustomer360Service');
const realTimeInteractionProcessor = require('../services/realTimeInteractionProcessor');
const { requireAuth } = require('../middleware/requireAuth');
const logger = require('../utils/logger');

// Apply authentication to all routes
router.use(requireAuth);

/**
 * 🧠 Analyze single email with GPT-4.1
 */
router.post('/email/analyze', async (req, res) => {
  try {
    const { emailData } = req.body;
    
    if (!emailData || !emailData.subject || !emailData.content) {
      return res.status(400).json({
        success: false,
        message: 'Email data with subject and content is required'
      });
    }

    logger.info(`🧠 Email analysis request: ${emailData.subject}`);
    
    const analysis = await enhancedEmailIntelligenceService.analyzeEmail(emailData);
    
    res.json({
      success: true,
      data: analysis,
      message: 'Email analyzed successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('❌ Email analysis error:', error);
    res.status(500).json({
      success: false,
      message: 'Email analysis failed',
      error: error.message
    });
  }
});

/**
 * 📧 Batch email processing
 */
router.post('/email/batch', async (req, res) => {
  try {
    const { emails } = req.body;
    
    if (!emails || !Array.isArray(emails) || emails.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Array of emails is required'
      });
    }

    if (emails.length > 50) {
      return res.status(400).json({
        success: false,
        message: 'Maximum 50 emails per batch'
      });
    }

    logger.info(`📧 Batch processing request: ${emails.length} emails`);
    
    const results = await enhancedEmailIntelligenceService.processEmailBatch(emails);
    
    res.json({
      success: true,
      data: results,
      message: `Batch processing completed: ${results.successRate.toFixed(1)}% success rate`,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('❌ Batch processing error:', error);
    res.status(500).json({
      success: false,
      message: 'Batch processing failed',
      error: error.message
    });
  }
});

/**
 * 🔍 Semantic email search
 */
router.get('/email/search', async (req, res) => {
  try {
    const { query, limit = 10 } = req.query;
    
    if (!query) {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }

    logger.info(`🔍 Semantic search request: ${query}`);
    
    const results = await enhancedEmailIntelligenceService.findSimilarEmails(query, parseInt(limit));
    
    res.json({
      success: true,
      data: results,
      message: `Found ${results.length} similar emails`,
      query: query,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('❌ Semantic search error:', error);
    res.status(500).json({
      success: false,
      message: 'Semantic search failed',
      error: error.message
    });
  }
});

/**
 * 📊 Intelligence dashboard data
 */
router.get('/dashboard', async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    
    logger.info(`📊 Dashboard insights request: ${timeframe}`);
    
    const insights = await enhancedEmailIntelligenceService.generateDashboardInsights(timeframe);
    
    res.json({
      success: true,
      data: insights,
      timeframe: timeframe,
      message: 'Dashboard insights generated successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('❌ Dashboard insights error:', error);
    res.status(500).json({
      success: false,
      message: 'Dashboard insights generation failed',
      error: error.message
    });
  }
});

/**
 * 👤 Customer 360-degree view - ENHANCED VERSION
 */
router.get('/customer/:id/360', async (req, res) => {
  try {
    const { id } = req.params;

    logger.info(`👤 Enhanced Customer 360 view request: ${id}`);

    const customer360 = await enhancedCustomer360Service.buildCustomer360Profile(id);

    res.json({
      success: true,
      data: customer360,
      customerId: id,
      message: 'Enhanced Customer 360 view generated successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('❌ Enhanced Customer 360 view error:', error);
    res.status(500).json({
      success: false,
      message: 'Enhanced Customer 360 view generation failed',
      error: error.message
    });
  }
});

/**
 * 🔄 Process new customer interaction
 */
router.post('/customer/:id/interaction', async (req, res) => {
  try {
    const { id } = req.params;
    const { interactionData } = req.body;

    if (!interactionData) {
      return res.status(400).json({
        success: false,
        message: 'Interaction data is required'
      });
    }

    logger.info(`🔄 Processing new interaction for customer: ${id}`);

    const result = await enhancedCustomer360Service.processNewInteraction(id, interactionData);

    res.json({
      success: true,
      data: result,
      customerId: id,
      message: 'Interaction processed successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('❌ Interaction processing error:', error);
    res.status(500).json({
      success: false,
      message: 'Interaction processing failed',
      error: error.message
    });
  }
});

/**
 * 📧 Process email interaction (webhook endpoint)
 */
router.post('/interaction/email', async (req, res) => {
  try {
    const { emailData } = req.body;

    if (!emailData) {
      return res.status(400).json({
        success: false,
        message: 'Email data is required'
      });
    }

    logger.info(`📧 Processing email interaction: ${emailData.subject}`);

    // Emit email event to real-time processor
    realTimeInteractionProcessor.emit('email_received', emailData);

    res.json({
      success: true,
      message: 'Email interaction queued for processing',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('❌ Email interaction processing error:', error);
    res.status(500).json({
      success: false,
      message: 'Email interaction processing failed',
      error: error.message
    });
  }
});

/**
 * 📞 Process call interaction (webhook endpoint)
 */
router.post('/interaction/call', async (req, res) => {
  try {
    const { callData } = req.body;

    if (!callData) {
      return res.status(400).json({
        success: false,
        message: 'Call data is required'
      });
    }

    logger.info(`📞 Processing call interaction: ${callData.duration}min call`);

    // Emit call event to real-time processor
    realTimeInteractionProcessor.emit('call_completed', callData);

    res.json({
      success: true,
      message: 'Call interaction queued for processing',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('❌ Call interaction processing error:', error);
    res.status(500).json({
      success: false,
      message: 'Call interaction processing failed',
      error: error.message
    });
  }
});

/**
 * 🔧 Process service interaction (webhook endpoint)
 */
router.post('/interaction/service', async (req, res) => {
  try {
    const { serviceData } = req.body;

    if (!serviceData) {
      return res.status(400).json({
        success: false,
        message: 'Service data is required'
      });
    }

    logger.info(`🔧 Processing service interaction: ${serviceData.type}`);

    // Emit service event to real-time processor
    realTimeInteractionProcessor.emit('service_order_created', serviceData);

    res.json({
      success: true,
      message: 'Service interaction queued for processing',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('❌ Service interaction processing error:', error);
    res.status(500).json({
      success: false,
      message: 'Service interaction processing failed',
      error: error.message
    });
  }
});

/**
 * 🔄 Sync data to Weaviate
 */
router.post('/weaviate/sync', async (req, res) => {
  try {
    const { dataType, data } = req.body;
    
    if (!dataType || !data) {
      return res.status(400).json({
        success: false,
        message: 'Data type and data are required'
      });
    }

    logger.info(`🔄 Weaviate sync request: ${dataType}`);
    
    let result;
    
    switch (dataType) {
      case 'customer':
        result = await enhancedWeaviateService.storeCustomerProfile(data);
        break;
      case 'email':
        result = await enhancedWeaviateService.storeEmailIntelligence(data);
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid data type. Supported: customer, email'
        });
    }
    
    res.json({
      success: true,
      data: result,
      dataType: dataType,
      message: 'Data synced to Weaviate successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('❌ Weaviate sync error:', error);
    res.status(500).json({
      success: false,
      message: 'Weaviate sync failed',
      error: error.message
    });
  }
});

/**
 * 🤖 AI Equipment Diagnostics
 */
router.post('/ai/equipment/:id/diagnose', async (req, res) => {
  try {
    const { id } = req.params;
    
    logger.info(`🤖 Equipment diagnostics request: ${id}`);
    
    const diagnostics = await enhancedAIService.performAdvancedEquipmentDiagnostics(id);
    
    res.json({
      success: true,
      data: diagnostics,
      equipmentId: id,
      message: 'Equipment diagnostics completed successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('❌ Equipment diagnostics error:', error);
    res.status(500).json({
      success: false,
      message: 'Equipment diagnostics failed',
      error: error.message
    });
  }
});

/**
 * 📈 AI Lead Scoring
 */
router.post('/ai/opportunity/:id/score', async (req, res) => {
  try {
    const { id } = req.params;
    
    logger.info(`📈 Lead scoring request: ${id}`);
    
    const scoring = await enhancedAIService.performIntelligentLeadScoring(id);
    
    res.json({
      success: true,
      data: scoring,
      opportunityId: id,
      message: 'Lead scoring completed successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('❌ Lead scoring error:', error);
    res.status(500).json({
      success: false,
      message: 'Lead scoring failed',
      error: error.message
    });
  }
});

/**
 * 👥 AI Customer Analysis
 */
router.post('/ai/customer/:id/analyze', async (req, res) => {
  try {
    const { id } = req.params;
    
    logger.info(`👥 Customer analysis request: ${id}`);
    
    const analysis = await enhancedAIService.performComprehensiveCustomerAnalysis(id);
    
    res.json({
      success: true,
      data: analysis,
      customerId: id,
      message: 'Customer analysis completed successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('❌ Customer analysis error:', error);
    res.status(500).json({
      success: false,
      message: 'Customer analysis failed',
      error: error.message
    });
  }
});

/**
 * 🔧 AI Maintenance Scheduling
 */
router.post('/ai/maintenance/schedule', async (req, res) => {
  try {
    logger.info(`🔧 Maintenance scheduling request`);
    
    const schedule = await enhancedAIService.generatePredictiveMaintenanceSchedule();
    
    res.json({
      success: true,
      data: schedule,
      message: 'Maintenance schedule generated successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('❌ Maintenance scheduling error:', error);
    res.status(500).json({
      success: false,
      message: 'Maintenance scheduling failed',
      error: error.message
    });
  }
});

/**
 * 📊 System Health Check
 */
router.get('/health', async (req, res) => {
  try {
    const health = {
      status: 'healthy',
      services: {
        emailIntelligence: 'operational',
        weaviate: enhancedWeaviateService.isConnected ? 'connected' : 'disconnected',
        ai: 'operational'
      },
      version: '2025.1.0',
      uptime: process.uptime(),
      timestamp: new Date()
    };
    
    res.json({
      success: true,
      data: health,
      message: 'System health check completed'
    });

  } catch (error) {
    logger.error('❌ Health check error:', error);
    res.status(500).json({
      success: false,
      message: 'Health check failed',
      error: error.message
    });
  }
});

module.exports = router;
