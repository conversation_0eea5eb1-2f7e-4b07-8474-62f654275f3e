const express = require('express');
const router = express.Router();

const enhancedAnalyticsController = require('@/controllers/appControllers/enhancedAnalyticsController');

/**
 * 🚀 ENHANCED ANALYTICS ROUTES
 * Zaawansowane funkcje analityczne dla HVAC CRM
 */

// Dashboard Overview - główne metryki
router.get('/dashboard', enhancedAnalyticsController.getDashboardOverview);

// Email Intelligence Analytics
router.get('/email-intelligence', enhancedAnalyticsController.getEmailAnalytics);

// Transcription Intelligence Analytics  
router.get('/transcription-intelligence', enhancedAnalyticsController.getTranscriptionAnalytics);

// Customer Insights Analytics
router.get('/customer-insights', enhancedAnalyticsController.getCustomerAnalytics);

// Business Metrics Analytics
router.get('/business-metrics', enhancedAnalyticsController.getBusinessAnalytics);

// Real-Time Metrics
router.get('/real-time', enhancedAnalyticsController.getRealTimeMetrics);

module.exports = router;
