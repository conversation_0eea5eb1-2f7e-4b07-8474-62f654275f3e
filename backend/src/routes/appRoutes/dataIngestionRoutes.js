const express = require('express');
const router = express.Router();

const dataIngestionController = require('@/controllers/appControllers/dataIngestionController');

/**
 * 🚀 DATA INGESTION ROUTES
 * Zaawansowany system importu i analizy danych archiwalnych
 */

// Start full data ingestion process
router.post('/start', dataIngestionController.startIngestion);

// Discover available data files
router.get('/discover', dataIngestionController.discoverFiles);

// Analyze data file structures
router.get('/analyze', dataIngestionController.analyzeFiles);

// Create processing plan
router.get('/plan', dataIngestionController.createPlan);

// Get ingestion status
router.get('/status', dataIngestionController.getStatus);

// Process specific file
router.post('/process/:filename', dataIngestionController.processFile);

// Get data insights
router.get('/insights', dataIngestionController.getInsights);

// Cleanup and optimize data
router.post('/cleanup', dataIngestionController.cleanupData);

// Get import history
router.get('/history', dataIngestionController.getImportHistory);

module.exports = router;
