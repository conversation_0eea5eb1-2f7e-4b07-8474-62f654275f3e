const express = require('express');
const router = express.Router();

const aiInsightsController = require('@/controllers/appControllers/aiInsightsController');

/**
 * 🤖 AI INSIGHTS ROUTES
 * Zaawansowany silnik insights oparty na AI
 */

// Comprehensive business insights
router.get('/business', aiInsightsController.generateBusinessInsights);

// Customer insights and analysis
router.get('/customer', aiInsightsController.getCustomerInsights);

// Operational efficiency insights
router.get('/operational', aiInsightsController.getOperationalInsights);

// Financial analysis and insights
router.get('/financial', aiInsightsController.getFinancialInsights);

// Predictive insights and forecasting
router.get('/predictive', aiInsightsController.getPredictiveInsights);

// Actionable recommendations
router.get('/recommendations', aiInsightsController.getRecommendations);

// Risk assessment
router.get('/risks', aiInsightsController.getRiskAssessment);

// Opportunity analysis
router.get('/opportunities', aiInsightsController.getOpportunityAnalysis);

// Complete insights dashboard
router.get('/dashboard', aiInsightsController.getInsightsDashboard);

module.exports = router;
