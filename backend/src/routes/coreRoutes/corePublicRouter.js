const express = require('express');
const router = express.Router();

const path = require('path');
const { isPathInside } = require('../../utils/is-path-inside');

// Public health check endpoint
router.get('/health', async (req, res) => {
  try {
    // Check Email Intelligence connection
    let emailIntelligenceStatus = 'disconnected';
    try {
      const response = await fetch('http://localhost:8001/health');
      if (response.ok) {
        emailIntelligenceStatus = 'connected';
      }
    } catch (error) {
      // Email Intelligence not available
    }

    // Check Weaviate connection
    let weaviateStatus = 'disconnected';
    try {
      const response = await fetch('http://localhost:8082/v1/meta');
      if (response.ok) {
        weaviateStatus = 'connected';
      }
    } catch (error) {
      // Weaviate not available
    }

    res.json({
      success: true,
      status: 'healthy',
      services: {
        backend: 'operational',
        emailIntelligence: emailIntelligenceStatus,
        weaviate: weaviateStatus,
        transcription: 'operational'
      },
      version: '2025.1.0',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

router.route('/:subPath/:directory/:file').get(function (req, res) {
  try {
    const { subPath, directory, file } = req.params;

    // Decode each parameter separately
    const decodedSubPath = decodeURIComponent(subPath);
    const decodedDirectory = decodeURIComponent(directory);
    const decodedFile = decodeURIComponent(file);

    // Define the trusted root directory
    const rootDir = path.join(__dirname, '../../public');

    // Safely join the decoded path segments
    const relativePath = path.join(decodedSubPath, decodedDirectory, decodedFile);
    const absolutePath = path.join(rootDir, relativePath);

    // Check if the resulting path stays inside rootDir
    if (!isPathInside(absolutePath, rootDir)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid filepath',
      });
    }

    return res.sendFile(absolutePath, (error) => {
      if (error) {
        return res.status(404).json({
          success: false,
          result: null,
          message: 'we could not find : ' + file,
        });
      }
    });
  } catch (error) {
    return res.status(503).json({
      success: false,
      result: null,
      message: error.message,
      error: error,
    });
  }
});

module.exports = router;
