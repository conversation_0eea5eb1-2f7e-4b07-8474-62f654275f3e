const axios = require('axios');

/**
 * 🤖 OPENAI SERVICE - FALLBACK AI MODEL
 * OpenAI GPT integration for HVAC CRM (fallback when <PERSON><PERSON><PERSON> fails)
 */
class OpenAIService {
    constructor() {
        this.apiKey = process.env.OPENAI_API_KEY;
        this.baseURL = 'https://api.openai.com/v1';
        this.model = 'gpt-3.5-turbo';
        this.isEnabled = !!this.apiKey;
    }

    async generateResponse(prompt, options = {}) {
        if (!this.isEnabled) {
            throw new Error('OpenAI service is not configured (missing API key)');
        }

        try {
            const response = await axios.post(`${this.baseURL}/chat/completions`, {
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: 'You are a helpful assistant specialized in HVAC (heating, ventilation, air conditioning) business analysis. Respond in Polish when analyzing Polish content.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: options.temperature || 0.7,
                max_tokens: options.max_tokens || 1000
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 30000
            });

            return response.data.choices[0].message.content;
        } catch (error) {
            console.error('OpenAI API error:', error.response?.data || error.message);
            throw new Error(`OpenAI API failed: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    async analyzeText(text, type = 'general', options = {}) {
        const prompt = this.buildPrompt(text, type);
        const response = await this.generateResponse(prompt, options);
        return this.parseResponse(response, type);
    }

    buildPrompt(text, type) {
        const prompts = {
            email_analysis: 'Analyze the following HVAC email and extract key information in Polish:',
            transcription_analysis: 'Analyze the following HVAC service call transcription in Polish:',
            issue_classification: 'Classify the following HVAC issue by priority and type in Polish:',
            response_generation: 'Generate a professional response to the following HVAC inquiry in Polish:'
        };
        
        const basePrompt = prompts[type] || 'Analyze the following text in Polish:';
        
        return `${basePrompt}

${text}

Respond in JSON format with the following fields in Polish:
- analiza: detailed analysis
- kategoria: issue/inquiry type (klimatyzacja/wentylacja/pompa_ciepla/serwis/instalacja/ogólne)
- priorytet: priority level (niski/średni/wysoki/krytyczny)
- akcje: suggested actions array
- sentiment: sentiment analysis (pozytywny/neutralny/negatywny)`;
    }

    parseResponse(response, type) {
        try {
            // Try to extract JSON from response
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
            
            // Fallback to text analysis
            return {
                analiza: response,
                kategoria: this.extractCategory(response),
                priorytet: this.extractPriority(response),
                akcje: this.extractActions(response),
                sentiment: this.extractSentiment(response),
                raw_response: response
            };
        } catch (error) {
            console.warn('Failed to parse OpenAI response as JSON:', error.message);
            return {
                analiza: response,
                raw_response: response,
                error: 'Failed to parse structured response'
            };
        }
    }

    extractCategory(text) {
        const categories = ['klimatyzacja', 'wentylacja', 'pompa_ciepla', 'serwis', 'instalacja'];
        for (const cat of categories) {
            if (text.toLowerCase().includes(cat)) return cat;
        }
        return 'ogólne';
    }

    extractPriority(text) {
        const priorities = ['krytyczny', 'wysoki', 'średni', 'niski'];
        for (const priority of priorities) {
            if (text.toLowerCase().includes(priority)) return priority;
        }
        return 'średni';
    }

    extractActions(text) {
        const actionKeywords = ['sprawdź', 'wymień', 'napraw', 'oczyść', 'skontaktuj'];
        const actions = [];
        for (const keyword of actionKeywords) {
            if (text.toLowerCase().includes(keyword)) {
                actions.push(keyword);
            }
        }
        return actions.length > 0 ? actions : ['analiza_wymagana'];
    }

    extractSentiment(text) {
        const positive = ['zadowolony', 'dziękuję', 'świetnie', 'dobrze'];
        const negative = ['problem', 'awaria', 'nie działa', 'zepsuty', 'pilne'];
        
        const positiveCount = positive.filter(word => text.toLowerCase().includes(word)).length;
        const negativeCount = negative.filter(word => text.toLowerCase().includes(word)).length;
        
        if (negativeCount > positiveCount) return 'negatywny';
        if (positiveCount > negativeCount) return 'pozytywny';
        return 'neutralny';
    }

    async healthCheck() {
        try {
            if (!this.isEnabled) {
                return {
                    status: 'disabled',
                    error: 'OpenAI API key not configured',
                    enabled: false
                };
            }

            // Simple test request
            const response = await axios.post(`${this.baseURL}/chat/completions`, {
                model: this.model,
                messages: [{ role: 'user', content: 'Test' }],
                max_tokens: 5
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });

            return {
                status: 'healthy',
                model: this.model,
                enabled: true,
                url: this.baseURL
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: error.response?.data?.error?.message || error.message,
                enabled: this.isEnabled,
                url: this.baseURL
            };
        }
    }
}

module.exports = new OpenAIService();
