const axios = require('axios');

/**
 * 🖥️ LM STUDIO SERVICE - TERTIARY AI MODEL
 * LM Studio local model integration (tertiary fallback)
 */
class LMStudioService {
    constructor() {
        this.baseURL = process.env.LM_STUDIO_URL || 'http://192.168.0.179:1234';
        this.model = process.env.LM_STUDIO_MODEL || 'gemma-2-2b-it';
        this.isEnabled = true; // Assume enabled if URL is configured
    }

    async generate(prompt, options = {}) {
        try {
            const response = await axios.post(`${this.baseURL}/v1/chat/completions`, {
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: 'You are a helpful assistant specialized in HVAC business analysis. Respond in Polish when analyzing Polish content.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: options.temperature || 0.7,
                max_tokens: options.max_tokens || 1000,
                stream: false
            }, {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            return response.data.choices[0].message.content;
        } catch (error) {
            console.error('LM Studio API error:', error.response?.data || error.message);
            throw new Error(`LM Studio API failed: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    async analyzeText(text, type = 'general', options = {}) {
        const prompt = this.buildPrompt(text, type);
        const response = await this.generate(prompt, options);
        return this.parseResponse(response, type);
    }

    buildPrompt(text, type) {
        const prompts = {
            email_analysis: 'Analyze the following HVAC email and extract key information in Polish:',
            transcription_analysis: 'Analyze the following HVAC service call transcription in Polish:',
            issue_classification: 'Classify the following HVAC issue by priority and type in Polish:',
            response_generation: 'Generate a professional response to the following HVAC inquiry in Polish:'
        };
        
        const basePrompt = prompts[type] || 'Analyze the following text in Polish:';
        
        return `${basePrompt}

${text}

Respond in JSON format with the following fields in Polish:
- analiza: detailed analysis
- kategoria: issue/inquiry type (klimatyzacja/wentylacja/pompa_ciepla/serwis/instalacja/ogólne)
- priorytet: priority level (niski/średni/wysoki/krytyczny)
- akcje: suggested actions array
- sentiment: sentiment analysis (pozytywny/neutralny/negatywny)`;
    }

    parseResponse(response, type) {
        try {
            // Try to extract JSON from response
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
            
            // Fallback to text analysis
            return {
                analiza: response,
                kategoria: this.extractCategory(response),
                priorytet: this.extractPriority(response),
                akcje: this.extractActions(response),
                sentiment: this.extractSentiment(response),
                raw_response: response
            };
        } catch (error) {
            console.warn('Failed to parse LM Studio response as JSON:', error.message);
            return {
                analiza: response,
                raw_response: response,
                error: 'Failed to parse structured response'
            };
        }
    }

    extractCategory(text) {
        const categories = ['klimatyzacja', 'wentylacja', 'pompa_ciepla', 'serwis', 'instalacja'];
        for (const cat of categories) {
            if (text.toLowerCase().includes(cat)) return cat;
        }
        return 'ogólne';
    }

    extractPriority(text) {
        const priorities = ['krytyczny', 'wysoki', 'średni', 'niski'];
        for (const priority of priorities) {
            if (text.toLowerCase().includes(priority)) return priority;
        }
        return 'średni';
    }

    extractActions(text) {
        const actionKeywords = ['sprawdź', 'wymień', 'napraw', 'oczyść', 'skontaktuj'];
        const actions = [];
        for (const keyword of actionKeywords) {
            if (text.toLowerCase().includes(keyword)) {
                actions.push(keyword);
            }
        }
        return actions.length > 0 ? actions : ['analiza_wymagana'];
    }

    extractSentiment(text) {
        const positive = ['zadowolony', 'dziękuję', 'świetnie', 'dobrze'];
        const negative = ['problem', 'awaria', 'nie działa', 'zepsuty', 'pilne'];
        
        const positiveCount = positive.filter(word => text.toLowerCase().includes(word)).length;
        const negativeCount = negative.filter(word => text.toLowerCase().includes(word)).length;
        
        if (negativeCount > positiveCount) return 'negatywny';
        if (positiveCount > negativeCount) return 'pozytywny';
        return 'neutralny';
    }

    async healthCheck() {
        try {
            // Test connection to LM Studio
            const response = await axios.get(`${this.baseURL}/v1/models`, {
                timeout: 10000
            });

            const models = response.data.data || [];
            const currentModel = models.find(m => m.id === this.model);

            return {
                status: 'healthy',
                model: this.model,
                model_loaded: !!currentModel,
                available_models: models.map(m => m.id),
                enabled: this.isEnabled,
                url: this.baseURL
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
                enabled: this.isEnabled,
                url: this.baseURL
            };
        }
    }
}

module.exports = new LMStudioService();
