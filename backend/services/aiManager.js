const bielikService = require('./bielikService');
const openaiService = require('./openaiService');
const lmStudioService = require('./lmStudioService');

/**
 * 🧠 AI MANAGER - HYBRID MULTI-MODEL INTELLIGENCE
 * Manages Bielik V3 4.5B (primary), OpenAI (fallback), LM Studio (tertiary)
 * Optimized for Polish HVAC business intelligence
 */
class AIManager {
    constructor() {
        this.models = {
            bielik: bielikService,
            openai: openaiService,
            lm_studio: lmStudioService
        };
        
        this.priority = [
            process.env.AI_PRIMARY_MODEL || 'bielik',
            process.env.AI_FALLBACK_MODEL || 'openai',
            process.env.AI_TERTIARY_MODEL || 'lm_studio'
        ];
        
        this.stats = {
            requests: 0,
            successes: 0,
            failures: 0,
            model_usage: {},
            avg_response_time: 0
        };
        
        this.initializeStats();
    }

    initializeStats() {
        this.priority.forEach(model => {
            this.stats.model_usage[model] = {
                requests: 0,
                successes: 0,
                failures: 0,
                avg_time: 0
            };
        });
    }

    /**
     * 🎯 MAIN AI ANALYSIS METHOD
     * Tries models in priority order with intelligent fallback
     */
    async analyzeText(text, type = 'general', options = {}) {
        const startTime = Date.now();
        this.stats.requests++;
        
        let lastError = null;
        
        for (const modelName of this.priority) {
            try {
                console.log(`🧠 Trying ${modelName} for ${type} analysis...`);
                
                const modelStartTime = Date.now();
                const result = await this.callModel(modelName, text, type, options);
                const responseTime = Date.now() - modelStartTime;
                
                // Update statistics
                this.updateStats(modelName, true, responseTime);
                this.stats.successes++;
                
                console.log(`✅ ${modelName} succeeded in ${responseTime}ms`);
                
                return {
                    ...result,
                    model_used: modelName,
                    response_time: responseTime,
                    timestamp: new Date().toISOString()
                };
                
            } catch (error) {
                console.warn(`❌ ${modelName} failed: ${error.message}`);
                this.updateStats(modelName, false, Date.now() - startTime);
                lastError = error;
                continue;
            }
        }
        
        // All models failed
        this.stats.failures++;
        throw new Error(`All AI models failed. Last error: ${lastError?.message}`);
    }

    async callModel(modelName, text, type, options) {
        const model = this.models[modelName];
        if (!model) {
            throw new Error(`Model ${modelName} not available`);
        }

        // Model-specific adaptations
        switch (modelName) {
            case 'bielik':
                return await model.analyzeText(text, type, options);
                
            case 'openai':
                return await this.adaptOpenAICall(model, text, type, options);
                
            case 'lm_studio':
                return await this.adaptLMStudioCall(model, text, type, options);
                
            default:
                throw new Error(`Unknown model: ${modelName}`);
        }
    }

    async adaptOpenAICall(service, text, type, options) {
        // Adapt for OpenAI service interface
        const prompt = this.buildOpenAIPrompt(text, type);
        const response = await service.generateResponse(prompt, {
            temperature: options.temperature || 0.7,
            max_tokens: options.max_tokens || 1000
        });
        
        return this.parseOpenAIResponse(response, type);
    }

    async adaptLMStudioCall(service, text, type, options) {
        // Adapt for LM Studio service interface
        const prompt = this.buildLMStudioPrompt(text, type);
        const response = await service.generate(prompt, options);
        
        return this.parseLMStudioResponse(response, type);
    }

    buildOpenAIPrompt(text, type) {
        const prompts = {
            email_analysis: 'Analyze the following HVAC email and extract key information in Polish:',
            transcription_analysis: 'Analyze the following HVAC service call transcription in Polish:',
            issue_classification: 'Classify the following HVAC issue by priority and type in Polish:',
            response_generation: 'Generate a professional response to the following HVAC inquiry in Polish:'
        };
        
        const basePrompt = prompts[type] || 'Analyze the following text in Polish:';
        
        return `${basePrompt}

${text}

Respond in JSON format with the following fields in Polish:
- analiza: detailed analysis
- kategoria: issue/inquiry type
- priorytet: niski/średni/wysoki/krytyczny
- akcje: suggested actions
- sentiment: pozytywny/neutralny/negatywny`;
    }

    buildLMStudioPrompt(text, type) {
        // Similar to OpenAI but adapted for LM Studio format
        return this.buildOpenAIPrompt(text, type);
    }

    parseOpenAIResponse(response, type) {
        try {
            // Try to parse as JSON first
            const parsed = JSON.parse(response);
            return parsed;
        } catch (error) {
            // Fallback to text analysis
            return {
                analiza: response,
                kategoria: this.extractCategory(response),
                priorytet: this.extractPriority(response),
                akcje: this.extractActions(response),
                sentiment: this.extractSentiment(response),
                raw_response: response
            };
        }
    }

    parseLMStudioResponse(response, type) {
        return this.parseOpenAIResponse(response, type);
    }

    // Utility methods for text analysis (shared with Bielik service)
    extractCategory(text) {
        const categories = ['klimatyzacja', 'wentylacja', 'pompa_ciepla', 'serwis', 'instalacja'];
        for (const cat of categories) {
            if (text.toLowerCase().includes(cat)) return cat;
        }
        return 'ogólne';
    }

    extractPriority(text) {
        const priorities = ['krytyczny', 'wysoki', 'średni', 'niski'];
        for (const priority of priorities) {
            if (text.toLowerCase().includes(priority)) return priority;
        }
        return 'średni';
    }

    extractActions(text) {
        const actionKeywords = ['sprawdź', 'wymień', 'napraw', 'oczyść', 'skontaktuj'];
        const actions = [];
        for (const keyword of actionKeywords) {
            if (text.toLowerCase().includes(keyword)) {
                actions.push(keyword);
            }
        }
        return actions.length > 0 ? actions : ['analiza_wymagana'];
    }

    extractSentiment(text) {
        const positive = ['zadowolony', 'dziękuję', 'świetnie', 'dobrze'];
        const negative = ['problem', 'awaria', 'nie działa', 'zepsuty', 'pilne'];
        
        const positiveCount = positive.filter(word => text.toLowerCase().includes(word)).length;
        const negativeCount = negative.filter(word => text.toLowerCase().includes(word)).length;
        
        if (negativeCount > positiveCount) return 'negatywny';
        if (positiveCount > negativeCount) return 'pozytywny';
        return 'neutralny';
    }

    updateStats(modelName, success, responseTime) {
        const modelStats = this.stats.model_usage[modelName];
        if (!modelStats) return;
        
        modelStats.requests++;
        if (success) {
            modelStats.successes++;
            modelStats.avg_time = (modelStats.avg_time * (modelStats.successes - 1) + responseTime) / modelStats.successes;
        } else {
            modelStats.failures++;
        }
        
        // Update global average
        this.stats.avg_response_time = (this.stats.avg_response_time * (this.stats.requests - 1) + responseTime) / this.stats.requests;
    }

    /**
     * 📊 HEALTH CHECK FOR ALL MODELS
     */
    async healthCheck() {
        const health = {
            timestamp: new Date().toISOString(),
            overall_status: 'healthy',
            models: {},
            stats: this.stats,
            priority: this.priority
        };
        
        for (const modelName of this.priority) {
            try {
                const model = this.models[modelName];
                if (model && typeof model.healthCheck === 'function') {
                    health.models[modelName] = await model.healthCheck();
                } else {
                    health.models[modelName] = {
                        status: 'unknown',
                        error: 'Health check not implemented'
                    };
                }
            } catch (error) {
                health.models[modelName] = {
                    status: 'unhealthy',
                    error: error.message
                };
            }
        }
        
        // Determine overall status
        const healthyModels = Object.values(health.models).filter(m => m.status === 'healthy').length;
        if (healthyModels === 0) {
            health.overall_status = 'critical';
        } else if (healthyModels < this.priority.length) {
            health.overall_status = 'degraded';
        }
        
        return health;
    }

    /**
     * 🎯 HVAC-SPECIFIC ANALYSIS METHODS
     */
    async analyzeEmail(emailContent, metadata = {}) {
        return await this.analyzeText(emailContent, 'email_analysis', {
            context: 'hvac_email',
            metadata
        });
    }

    async analyzeTranscription(transcriptionText, metadata = {}) {
        return await this.analyzeText(transcriptionText, 'transcription_analysis', {
            context: 'hvac_call',
            metadata
        });
    }

    async classifyIssue(issueDescription, metadata = {}) {
        return await this.analyzeText(issueDescription, 'issue_classification', {
            context: 'hvac_issue',
            metadata
        });
    }

    async generateResponse(inquiry, context = {}) {
        return await this.analyzeText(inquiry, 'response_generation', {
            context: 'hvac_response',
            ...context
        });
    }

    /**
     * 📈 STATISTICS AND MONITORING
     */
    getStats() {
        return {
            ...this.stats,
            uptime: process.uptime(),
            memory_usage: process.memoryUsage(),
            timestamp: new Date().toISOString()
        };
    }

    resetStats() {
        this.stats = {
            requests: 0,
            successes: 0,
            failures: 0,
            model_usage: {},
            avg_response_time: 0
        };
        this.initializeStats();
    }
}

module.exports = new AIManager();
