const axios = require('axios');
const fs = require('fs');
const path = require('path');

class BielikService {
    constructor() {
        this.config = this.loadConfig();
        this.isEnabled = process.env.BIELIK_ENABLED === 'true';
        this.baseURL = process.env.BIELIK_URL || this.config.url;
        this.model = process.env.BIELIK_MODEL || this.config.model;
    }

    loadConfig() {
        try {
            const configPath = path.join(__dirname, '../config/bielik.json');
            return JSON.parse(fs.readFileSync(configPath, 'utf8'));
        } catch (error) {
            console.warn('Bielik config not found, using defaults');
            return {
                model: 'SpeakLeash/bielik-4.5b-v3.0-instruct:Q8_0',
                url: 'http://localhost:11434',
                options: { temperature: 0.7, top_p: 0.9, max_tokens: 1000 }
            };
        }
    }

    async analyzeText(text, type = 'general', options = {}) {
        if (!this.isEnabled) {
            throw new Error('Bielik service is disabled');
        }

        try {
            const prompt = this.buildPrompt(text, type);
            const response = await this.callBielik(prompt, options);
            return this.parseResponse(response, type);
        } catch (error) {
            console.error('Bielik analysis failed:', error.message);
            throw error;
        }
    }

    buildPrompt(text, type) {
        const prompts = {
            email_analysis: 'Przeanalizuj następujący email HVAC i wyciągnij kluczowe informacje:',
            transcription_analysis: 'Przeanalizuj następującą transkrypcję rozmowy serwisowej HVAC:',
            issue_classification: 'Sklasyfikuj następujący problem HVAC według priorytetu i typu:',
            response_generation: 'Wygeneruj profesjonalną odpowiedź na następujące zgłoszenie HVAC:'
        };
        
        const basePrompt = prompts[type] || 'Przeanalizuj następujący tekst:';
        
        return `${basePrompt}

${text}

Odpowiedz w formacie JSON z następującymi polami:
- analiza: szczegółowa analiza tekstu
- kategoria: typ problemu/zapytania
- priorytet: niski/średni/wysoki/krytyczny
- akcje: sugerowane działania
- sentiment: pozytywny/neutralny/negatywny`;
    }

    async callBielik(prompt, options = {}) {
        const requestOptions = {
            temperature: 0.7,
            top_p: 0.9,
            max_tokens: 1000,
            ...options
        };

        const response = await axios.post(`${this.baseURL}/api/generate`, {
            model: this.model,
            prompt: prompt,
            stream: false,
            options: requestOptions
        }, {
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json'
            }
        });

        return response.data;
    }

    parseResponse(response, type) {
        try {
            const text = response.response || '';
            
            // Try to extract JSON from response
            const jsonMatch = text.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
            
            // Fallback to structured text parsing
            return {
                analiza: text,
                kategoria: this.extractCategory(text),
                priorytet: this.extractPriority(text),
                akcje: this.extractActions(text),
                sentiment: this.extractSentiment(text),
                raw_response: text
            };
        } catch (error) {
            console.warn('Failed to parse Bielik response as JSON:', error.message);
            return {
                analiza: response.response || '',
                raw_response: response.response || '',
                error: 'Failed to parse structured response'
            };
        }
    }

    extractCategory(text) {
        const categories = ['klimatyzacja', 'wentylacja', 'pompa_ciepla', 'serwis', 'instalacja'];
        for (const cat of categories) {
            if (text.toLowerCase().includes(cat)) return cat;
        }
        return 'ogólne';
    }

    extractPriority(text) {
        const priorities = ['krytyczny', 'wysoki', 'średni', 'niski'];
        for (const priority of priorities) {
            if (text.toLowerCase().includes(priority)) return priority;
        }
        return 'średni';
    }

    extractActions(text) {
        const actionKeywords = ['sprawdź', 'wymień', 'napraw', 'oczyść', 'skontaktuj'];
        const actions = [];
        for (const keyword of actionKeywords) {
            if (text.toLowerCase().includes(keyword)) {
                actions.push(keyword);
            }
        }
        return actions.length > 0 ? actions : ['analiza_wymagana'];
    }

    extractSentiment(text) {
        const positive = ['zadowolony', 'dziękuję', 'świetnie', 'dobrze'];
        const negative = ['problem', 'awaria', 'nie działa', 'zepsuty', 'pilne'];
        
        const positiveCount = positive.filter(word => text.toLowerCase().includes(word)).length;
        const negativeCount = negative.filter(word => text.toLowerCase().includes(word)).length;
        
        if (negativeCount > positiveCount) return 'negatywny';
        if (positiveCount > negativeCount) return 'pozytywny';
        return 'neutralny';
    }

    async healthCheck() {
        try {
            const response = await axios.get(`${this.baseURL}/api/tags`, { timeout: 5000 });
            const models = response.data.models || [];
            const bielikInstalled = models.some(m => m.name.includes('bielik'));
            
            return {
                status: 'healthy',
                url: this.baseURL,
                model: this.model,
                installed: bielikInstalled,
                enabled: this.isEnabled
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
                url: this.baseURL,
                enabled: this.isEnabled
            };
        }
    }
}

module.exports = new BielikService();
