#!/usr/bin/env python3
"""
🔍 Test Email Connection Script
Testuje połączenie z kontami email HVAC
"""

import imaplib
import ssl
import sys
from typing import Dict, Any

def test_email_connection(email: str, password: str, server: str, port: int = 993) -> Dict[str, Any]:
    """Test IMAP connection to email account"""
    result = {
        "email": email,
        "server": server,
        "port": port,
        "success": False,
        "error": None,
        "details": {}
    }
    
    try:
        print(f"🔍 Testing connection to {email} on {server}:{port}")
        
        # Create IMAP connection
        mail = imaplib.IMAP4_SSL(server, port)
        
        # Try to login
        mail.login(email, password)
        
        # Select INBOX
        mail.select('INBOX')
        
        # Get mailbox status
        status, messages = mail.search(None, 'ALL')
        if status == 'OK':
            message_count = len(messages[0].split()) if messages[0] else 0
            result["details"]["message_count"] = message_count
            print(f"✅ Connection successful! Found {message_count} messages")
        
        # Logout
        mail.logout()
        
        result["success"] = True
        
    except imaplib.IMAP4.error as e:
        result["error"] = f"IMAP Error: {str(e)}"
        print(f"❌ IMAP Error: {e}")
    except ssl.SSLError as e:
        result["error"] = f"SSL Error: {str(e)}"
        print(f"❌ SSL Error: {e}")
    except Exception as e:
        result["error"] = f"General Error: {str(e)}"
        print(f"❌ General Error: {e}")
    
    return result

def main():
    """Main test function"""
    print("🚀 HVAC Email Connection Test")
    print("=" * 50)
    
    # Test configurations
    test_configs = [
        {
            "email": "<EMAIL>",
            "password": "Blaeritipol1",
            "server": "serwer2440139.home.pl",
            "port": 993
        },
        {
            "email": "<EMAIL>", 
            "password": "Blaeritipol1",
            "server": "serwer2440139.home.pl",
            "port": 993
        },
        # Fallback test with Gmail settings
        {
            "email": "<EMAIL>",
            "password": "Blaeritipol1",
            "server": "imap.gmail.com",
            "port": 993
        }
    ]
    
    results = []
    
    for config in test_configs:
        print(f"\n📧 Testing: {config['email']} @ {config['server']}")
        result = test_email_connection(**config)
        results.append(result)
        
        if result["success"]:
            print(f"✅ SUCCESS: {config['email']}")
        else:
            print(f"❌ FAILED: {config['email']} - {result['error']}")
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY:")
    
    successful = [r for r in results if r["success"]]
    failed = [r for r in results if not r["success"]]
    
    print(f"✅ Successful connections: {len(successful)}")
    print(f"❌ Failed connections: {len(failed)}")
    
    if successful:
        print("\n🎯 Working configurations:")
        for result in successful:
            print(f"  - {result['email']} @ {result['server']}:{result['port']}")
    
    if failed:
        print("\n⚠️ Failed configurations:")
        for result in failed:
            print(f"  - {result['email']} @ {result['server']}:{result['port']} - {result['error']}")
    
    return len(successful) > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
