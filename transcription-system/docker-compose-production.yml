version: '3.8'

services:
  # 🎤 NVIDIA NeMo STT Service - Production Ready
  nvidia-nemo-stt:
    build:
      context: ./nvidia-nemo-stt
      dockerfile: Dockerfile
    container_name: hvac-nvidia-nemo-stt
    restart: unless-stopped
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - REDIS_URL=redis://redis-krabulon:6379
      - POSTGRES_URL=${POSTGRES_URL}
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
      - LOG_LEVEL=INFO
    volumes:
      - ./audio_input:/app/audio_input
      - ./transcriptions:/app/transcriptions
      - ./logs:/app/logs
      - ./models:/app/models
      - nvidia_cache:/root/.cache
    ports:
      - "8889:8889"  # STT Service
    networks:
      - hvac-network
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8889/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  # 🔄 Audio Converter Service (M4A -> WAV)
  audio-converter:
    build:
      context: ./audio-converter
      dockerfile: Dockerfile
    container_name: hvac-audio-converter
    restart: unless-stopped
    environment:
      - REDIS_URL=redis://redis-krabulon:6379
      - INPUT_FORMAT=m4a
      - OUTPUT_FORMAT=wav
      - SAMPLE_RATE=16000
      - CHANNELS=1
      - LOG_LEVEL=INFO
    volumes:
      - ./audio_input:/app/input
      - ./audio_converted:/app/output
      - ./logs:/app/logs
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 📧 Email Processor Service - Enhanced
  email-processor:
    build:
      context: ./email-processor
      dockerfile: Dockerfile
    container_name: hvac-email-processor
    restart: unless-stopped
    environment:
      - IMAP_SERVER=serwer2440139.home.pl
      - IMAP_PORT=993
      - EMAIL_DOLORES=<EMAIL>
      - EMAIL_GRZEGORZ=<EMAIL>
      - DOLORES_EMAIL_PASSWORD=${DOLORES_EMAIL_PASSWORD}
      - GRZEGORZ_EMAIL_PASSWORD=${GRZEGORZ_EMAIL_PASSWORD}
      - REDIS_URL=redis://redis-krabulon:6379
      - POSTGRES_URL=${POSTGRES_URL}
      - MINIO_ENDPOINT=**************:9000
      - MINIO_ACCESS_KEY=koldbringer
      - MINIO_SECRET_KEY=Blaeritipol1
      - LOG_LEVEL=INFO
      - CIRCUIT_BREAKER_ENABLED=true
      - MAX_RETRY_ATTEMPTS=3
      - RETRY_DELAY_SECONDS=5
    volumes:
      - ./email_attachments:/app/attachments
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - audio-converter
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 🧠 Gemma 3 4B Integration Service - Enhanced
  gemma-integration:
    build:
      context: ./gemma-integration
      dockerfile: Dockerfile
    container_name: hvac-gemma-integration
    restart: unless-stopped
    environment:
      - LM_STUDIO_URL=http://*************:1234
      - MODEL_NAME=gemma-3-4b
      - LANGUAGE=pl
      - MAX_TOKENS=2048
      - TEMPERATURE=0.3
      - REDIS_URL=redis://redis-krabulon:6379
      - LOG_LEVEL=INFO
      - CIRCUIT_BREAKER_ENABLED=true
      - HVAC_CONTEXT_ENABLED=true
    volumes:
      - ./transcriptions:/app/transcriptions
      - ./analysis_results:/app/analysis
      - ./logs:/app/logs
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 🎯 Transcription Orchestrator - Production Enhanced
  transcription-orchestrator:
    build:
      context: ./orchestrator
      dockerfile: Dockerfile
    container_name: hvac-transcription-orchestrator
    restart: unless-stopped
    environment:
      - NVIDIA_STT_URL=http://nvidia-nemo-stt:8889
      - AUDIO_CONVERTER_URL=http://audio-converter:8080
      - EMAIL_PROCESSOR_URL=http://email-processor:8080
      - GEMMA_INTEGRATION_URL=http://gemma-integration:8080
      - GOBACKEND_URL=http://host.docker.internal:8080
      - REDIS_URL=redis://redis-krabulon:6379
      - POSTGRES_URL=${POSTGRES_URL}
      - LOG_LEVEL=INFO
      - CIRCUIT_BREAKER_ENABLED=true
      - MONITORING_ENABLED=true
      - ALERT_WEBHOOK_URL=${ALERT_WEBHOOK_URL}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
    ports:
      - "9000:8080"  # Main API
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
      - ./audio_input:/app/audio_input
      - ./audio_converted:/app/audio_converted
      - ./transcriptions:/app/transcriptions
      - ./monitoring:/app/monitoring
    depends_on:
      - nvidia-nemo-stt
      - audio-converter
      - email-processor
      - gemma-integration
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 📊 Monitoring Service (Optional)
  monitoring-dashboard:
    image: grafana/grafana:latest
    container_name: hvac-monitoring
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - hvac-network

  # 🔍 Log Aggregation (Optional)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: hvac-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - hvac-network

volumes:
  nvidia_cache:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  hvac-network:
    external: true
    name: python_mixer_hvac-network
