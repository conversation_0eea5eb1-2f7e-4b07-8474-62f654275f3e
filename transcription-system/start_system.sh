#!/bin/bash

# 🚀 HVAC NVIDIA STT System Startup Script
# Uruchomienie kompletnego systemu transkrypcji

echo "🎤 =========================================="
echo "🎤 HVAC NVIDIA STT SYSTEM STARTUP"
echo "🎤 =========================================="

# 🔧 Sprawdzenie wymagań
echo "🔍 Sprawdzanie wymagań systemowych..."

# Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker nie jest zainstalowany!"
    exit 1
fi

# Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose nie jest zainstalowany!"
    exit 1
fi

# NVIDIA Docker
if ! docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi &> /dev/null; then
    echo "⚠️ NVIDIA Docker może nie być dostępny - sprawdź konfigurację GPU"
fi

echo "✅ Wymagania systemowe spełnione"

# 📁 Tworzenie katalogów
echo "📁 Tworzenie katalogów roboczych..."
mkdir -p models audio_input audio_converted transcriptions analysis_results logs
mkdir -p email_attachments temp config

# 🔧 Sprawdzenie zmiennych środowiskowych
echo "🔧 Sprawdzanie konfiguracji..."

if [ -z "$DOLORES_EMAIL_PASSWORD" ]; then
    echo "⚠️ Brak hasł<NAME_EMAIL> (DOLORES_EMAIL_PASSWORD)"
fi

if [ -z "$GRZEGORZ_EMAIL_PASSWORD" ]; then
    echo "⚠️ Brak hasł<NAME_EMAIL> (GRZEGORZ_EMAIL_PASSWORD)"
fi

# 🐳 Budowanie obrazów Docker
echo "🐳 Budowanie obrazów Docker..."

echo "🔄 Budowanie Audio Converter..."
docker-compose build audio-converter

echo "📧 Budowanie Email Processor..."
docker-compose build email-processor

echo "🧠 Budowanie Gemma Integration..."
docker-compose build gemma-integration

echo "🎯 Budowanie Orchestrator..."
docker-compose build transcription-orchestrator

# 🚀 Uruchomienie systemu
echo "🚀 Uruchamianie systemu..."

# Uruchomienie w tle
docker-compose up -d

echo "⏳ Oczekiwanie na inicjalizację serwisów..."
sleep 30

# 🔍 Sprawdzenie statusu serwisów
echo "🔍 Sprawdzanie statusu serwisów..."

services=(
    "redis:6379"
    "audio-converter:8080" 
    "email-processor:8080"
    "gemma-integration:8080"
    "transcription-orchestrator:8080"
    "nvidia-stt-polish:8889"
)

for service in "${services[@]}"; do
    IFS=':' read -r name port <<< "$service"
    if curl -s "http://localhost:$port/health" > /dev/null 2>&1; then
        echo "✅ $name ($port)"
    else
        echo "❌ $name ($port) - sprawdź logi"
    fi
done

# 📊 Wyświetlenie informacji o systemie
echo ""
echo "🎯 =========================================="
echo "🎯 SYSTEM URUCHOMIONY!"
echo "🎯 =========================================="
echo ""
echo "📡 Dostępne endpointy:"
echo "🎯 Orchestrator:      http://localhost:9000"
echo "🎤 NVIDIA STT:        http://localhost:8888"
echo "🔄 Audio Converter:   http://localhost:8081"
echo "📧 Email Processor:   http://localhost:8082"
echo "🧠 Gemma Integration: http://localhost:8083"
echo "📊 Redis:             http://localhost:6379"
echo ""
echo "📋 Przydatne komendy:"
echo "📊 Status:            curl http://localhost:9000/health"
echo "📈 Statystyki:        curl http://localhost:9000/stats"
echo "📧 Test emaili:       curl -X POST http://localhost:9000/trigger/email-check"
echo "📋 Logi:              docker-compose logs -f"
echo "🛑 Stop:              docker-compose down"
echo ""
echo "🔗 WebSocket (real-time): ws://localhost:9000/ws"
echo ""

# 🧪 Test podstawowej funkcjonalności
echo "🧪 Test podstawowej funkcjonalności..."

# Test health check orchestratora
if curl -s "http://localhost:9000/health" | grep -q "healthy"; then
    echo "✅ Orchestrator działa poprawnie"
else
    echo "⚠️ Orchestrator może mieć problemy - sprawdź logi"
fi

# Test LM Studio (jeśli dostępne)
if curl -s "http://*************:1234/v1/models" > /dev/null 2>&1; then
    echo "✅ LM Studio z Gemma 3 4B dostępne"
else
    echo "⚠️ LM Studio niedostępne - sprawdź konfigurację"
fi

echo ""
echo "🎉 System gotowy do pracy!"
echo "📧 Automatyczne monitorowanie emaili uruchomione"
echo "🎤 Pipeline transkrypcji aktywny"
echo ""
echo "📖 Dokumentacja: README.md"
echo "🐛 Problemy: sprawdź logi w ./logs/"
