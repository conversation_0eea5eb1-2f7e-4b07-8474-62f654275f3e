# 🎤 HVAC NVIDIA STT System - Polski System Transkrypcji

## 🎯 Opis Systemu

Kompletny system transkrypcji dla firmy HVAC wykorzystujący:
- **NVIDIA NeMo** - FastConformer Hybrid Large PC dla języka polskiego
- **Gemma 3 4B** - Analiza transkrypcji przez LM Studio
- **Automatyczne przetwarzanie emaili** z załącznikami M4A
- **Pipeline konwersji** M4A → WAV → STT → Analiza → Baza danych

## 🏗️ Architektura Systemu

```
📧 Email (M4A) → 🔄 Converter → 🎤 NVIDIA STT → 🧠 Gemma → 💾 Database
     ↓              ↓              ↓              ↓           ↓
  dolores@      M4A→WAV        Polski STT    AI Analysis   PostgreSQL
  Grzegorz@     16kHz/Mono     FastConformer  Insights     + Redis
```

### 🔧 Komponenty

1. **📧 Email Processor** - Automatyczne pobieranie załączników M4A
2. **🔄 Audio Converter** - Konwersja M4A → WAV (16kHz, mono)
3. **🎤 NVIDIA STT** - Transkrypcja polska z NeMo FastConformer
4. **🧠 Gemma Integration** - Analiza AI z LM Studio
5. **🎯 Orchestrator** - Koordynacja całego pipeline'u
6. **💾 Redis + PostgreSQL** - Cache i trwałe przechowywanie

## 🚀 Instalacja i Uruchomienie

### Wymagania

- **Docker** + **Docker Compose**
- **NVIDIA GPU** z CUDA (dla STT)
- **LM Studio** z Gemma 3 4B na `*************:1234`
- **PostgreSQL** na `**************:5432`
- **Hasła email** w zmiennych środowiskowych

### Konfiguracja

```bash
# 🔧 Zmienne środowiskowe
export DOLORES_EMAIL_PASSWORD="hasło_dolores"
export GRZEGORZ_EMAIL_PASSWORD="hasło_grzegorz"

# 🚀 Uruchomienie systemu
chmod +x start_system.sh
./start_system.sh
```

### Ręczne uruchomienie

```bash
# 🐳 Budowanie
docker-compose build

# 🚀 Uruchomienie
docker-compose up -d

# 📊 Sprawdzenie statusu
curl http://localhost:9000/health
```

## 📡 API Endpoints

### 🎯 Orchestrator (Port 9000)

```bash
# 🏥 Health check
GET /health

# 📊 Statystyki
GET /stats

# 🎤 Transkrypcja pliku
POST /transcribe/file
{
  "source_file": "/path/to/audio.m4a",
  "email_source": "<EMAIL>"
}

# 📊 Status zadania
GET /transcribe/status/{job_id}

# 📧 Ręczne sprawdzenie emaili
POST /trigger/email-check

# 🔌 WebSocket real-time
WS /ws
```

### 🎤 NVIDIA STT (Port 8888)

```bash
# 🎤 Transkrypcja
POST /transcribe
- audio_file: plik audio
- language: "pl"
- model_type: "fastconformer"
- hvac_context: true

# 📋 Dostępne modele
GET /models
```

### 🔄 Audio Converter (Port 8081)

```bash
# 🔄 Konwersja
POST /convert
- audio_file: plik M4A
- output_format: "wav"
- sample_rate: 16000
- channels: 1
```

### 🧠 Gemma Integration (Port 8083)

```bash
# 🧠 Analiza transkrypcji
POST /analyze
{
  "transcript": "tekst transkrypcji",
  "source_file": "nazwa_pliku.m4a",
  "hvac_context": true
}
```

## 📧 Konfiguracja Email

System automatycznie monitoruje konta:

- **<EMAIL>** - Transkrypcje (załączniki M4A)
- **<EMAIL>** - Emaile klientów

### Wymagane zmienne

```bash
export DOLORES_EMAIL_PASSWORD="hasło"
export GRZEGORZ_EMAIL_PASSWORD="hasło"
```

## 🎯 Workflow Transkrypcji

1. **📧 Monitoring** - System sprawdza emaile co 5 minut
2. **📎 Załączniki** - Pobiera pliki M4A z emaili
3. **🔄 Konwersja** - M4A → WAV (16kHz, mono)
4. **🎤 STT** - NVIDIA NeMo FastConformer (polski)
5. **🧠 Analiza** - Gemma 3 4B przez LM Studio
6. **💾 Zapis** - PostgreSQL + Redis cache
7. **📊 Powiadomienia** - WebSocket real-time

## 📊 Monitorowanie

### Logi

```bash
# 📋 Wszystkie serwisy
docker-compose logs -f

# 🎯 Konkretny serwis
docker-compose logs -f transcription-orchestrator
docker-compose logs -f nvidia-stt-polish
docker-compose logs -f email-processor
```

### Metryki

```bash
# 📊 Statystyki pipeline'u
curl http://localhost:9000/stats

# 🏥 Status wszystkich serwisów
curl http://localhost:9000/health
```

### WebSocket Real-time

```javascript
const ws = new WebSocket('ws://localhost:9000/ws');
ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log('Nowa transkrypcja:', data);
};
```

## 🔧 Konfiguracja Zaawansowana

### NVIDIA STT Models

Dostępne modele polskie:
- `stt_multilingual_fastconformer_hybrid_large_pc` (domyślny)
- `stt_pl_quartznet15x5` (backup)

### Gemma 3 4B

Konfiguracja LM Studio:
- URL: `http://*************:1234`
- Model: `gemma-3-4b`
- Temperature: `0.3`
- Max tokens: `2048`

### Baza Danych

PostgreSQL schema:
```sql
-- Zadania transkrypcji
transcription_jobs (
    job_id, source_file, email_source, 
    status, transcript, confidence, 
    analysis, processing_time, created_at
)

-- Komunikacja email
email_communications (
    message_id, from_email, to_email,
    subject, body, attachments_count,
    account_type, processed_at
)
```

## 🐛 Rozwiązywanie Problemów

### Częste problemy

1. **NVIDIA GPU niedostępne**
   ```bash
   # Sprawdzenie NVIDIA Docker
   docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi
   ```

2. **LM Studio niedostępne**
   ```bash
   # Test połączenia
   curl http://*************:1234/v1/models
   ```

3. **Błędy email**
   ```bash
   # Sprawdzenie logów
   docker-compose logs email-processor
   ```

4. **Problemy z konwersją audio**
   ```bash
   # Test FFmpeg
   docker-compose exec audio-converter ffmpeg -version
   ```

### Restart serwisów

```bash
# 🔄 Restart konkretnego serwisu
docker-compose restart transcription-orchestrator

# 🔄 Restart całego systemu
docker-compose down && docker-compose up -d
```

## 📈 Wydajność

### Benchmarki

- **Konwersja M4A→WAV**: ~2-5s dla 1min audio
- **NVIDIA STT**: ~10-30s dla 1min audio (GPU)
- **Gemma analiza**: ~5-15s dla transkrypcji
- **Całkowity pipeline**: ~20-60s dla 1min audio

### Optymalizacja

- GPU NVIDIA dla STT (wymagane)
- SSD dla szybkiego I/O
- Minimum 16GB RAM
- Redis cache dla powtarzających się analiz

## 🔐 Bezpieczeństwo

- Hasła email w zmiennych środowiskowych
- Komunikacja wewnętrzna przez Docker network
- Brak ekspozycji haseł w logach
- Automatyczne czyszczenie plików tymczasowych

## 📞 Wsparcie

W przypadku problemów:
1. Sprawdź logi: `docker-compose logs -f`
2. Sprawdź status: `curl http://localhost:9000/health`
3. Sprawdź konfigurację GPU/LM Studio
4. Sprawdź zmienne środowiskowe

---

## 🎉 Gotowe do pracy!

System automatycznie:
- ✅ Monitoruje emaile z załącznikami M4A
- ✅ Konwertuje audio do formatu STT
- ✅ Transkrybuje w języku polskim
- ✅ Analizuje treść z AI
- ✅ Zapisuje wyniki do bazy
- ✅ Powiadamia o nowych transkrypcjach

**Ciesz się automatyczną transkrypcją rozmów HVAC!** 🎤🏠❄️
