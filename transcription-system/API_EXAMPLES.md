# 🎤 HVAC NVIDIA STT API - Przyk<PERSON>dy <PERSON>

## 🚀 Podstawowe Endpointy

### 🏥 Health Check

```bash
# Sprawdzenie stanu orchestratora
curl http://localhost:9000/health

# Sprawdzenie stanu NVIDIA STT
curl http://localhost:8888/health

# Sprawdzenie stanu przez Go Backend
curl http://localhost:8080/api/transcription/health
```

### 📊 Statystyki

```bash
# Statystyki pipeline'u
curl http://localhost:9000/stats

# Statystyki przez Go Backend
curl http://localhost:8080/api/transcription/stats
```

## 🎤 Transkrypcja Audio

### Upload przez Go Backend

```bash
# Upload pliku M4A
curl -X POST \
  -F "audio_file=@nagranie.m4a" \
  -F "email_source=<EMAIL>" \
  http://localhost:8080/api/transcription/upload
```

### Upload bezpośrednio do NVIDIA STT

```bash
# Transkrypcja WAV
curl -X POST \
  -F "audio_file=@nagranie.wav" \
  -F "language=pl" \
  -F "model_type=fastconformer" \
  -F "hvac_context=true" \
  http://localhost:8888/transcribe
```

### Sprawdzenie statusu zadania

```bash
# Status zadania
curl http://localhost:8080/api/transcription/status/job_1234567890

# Odpowiedź:
{
  "success": true,
  "result": {
    "job_id": "job_1234567890",
    "transcript": "Dzień dobry, mam problem z klimatyzacją LG...",
    "confidence": 0.92,
    "analysis": {
      "sentiment": "neutralny",
      "intent": "serwis",
      "hvac_keywords": ["klimatyzacja", "LG", "serwis"]
    },
    "processing_time": 45.2,
    "status": "completed"
  }
}
```

## 🔄 Konwersja Audio

### M4A → WAV

```bash
# Konwersja pliku
curl -X POST \
  -F "audio_file=@nagranie.m4a" \
  -F "output_format=wav" \
  -F "sample_rate=16000" \
  -F "channels=1" \
  -F "normalize=true" \
  http://localhost:8081/convert
```

### Wsadowa konwersja

```bash
# Konwersja wielu plików
curl -X POST \
  -F "files=@nagranie1.m4a" \
  -F "files=@nagranie2.m4a" \
  -F "output_format=wav" \
  -F "sample_rate=16000" \
  http://localhost:8081/convert/batch
```

## 🧠 Analiza z Gemma 3 4B

### Analiza transkrypcji

```bash
# Analiza tekstu
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "transcript": "Dzień dobry, mam problem z klimatyzacją LG. Nie chłodzi jak powinna.",
    "source_file": "nagranie_klient_123.m4a",
    "hvac_context": true
  }' \
  http://localhost:8083/analyze
```

### Odpowiedź analizy

```json
{
  "summary": "Klient zgłasza problem z wydajnością chłodzenia klimatyzacji LG",
  "sentiment": "negatywny",
  "intent": "serwis",
  "hvac_keywords": ["klimatyzacja", "LG", "chłodzenie"],
  "customer_insights": {
    "problem_type": "wydajność",
    "urgency": "średnia",
    "equipment_brand": "LG"
  },
  "action_items": [
    "Umówić wizytę serwisową",
    "Sprawdzić filtr klimatyzacji",
    "Zweryfikować poziom czynnika chłodniczego"
  ],
  "priority_score": 7.5,
  "confidence": 0.89,
  "processing_time": 12.3
}
```

## 📧 Zarządzanie Emailami

### Ręczne sprawdzenie emaili

```bash
# Sprawdzenie nowych emaili
curl -X POST http://localhost:9000/trigger/email-check

# Sprawdzenie przez Go Backend
curl -X POST http://localhost:8080/api/transcription/trigger-email-check
```

### Statystyki emaili

```bash
# Statystyki przetwarzania emaili
curl http://localhost:8082/stats
```

## 📊 Dashboard i Monitoring

### Dashboard transkrypcji

```bash
# Dashboard przez Go Backend
curl http://localhost:8080/api/transcription/dashboard
```

### WebSocket Real-time

```javascript
// JavaScript WebSocket
const ws = new WebSocket('ws://localhost:9000/ws');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Nowa transkrypcja:', data);
    
    if (data.status === 'completed') {
        console.log('Transkrypcja zakończona:', data.transcript);
        console.log('Analiza AI:', data.analysis);
    }
};

ws.onopen = function() {
    console.log('Połączono z systemem transkrypcji');
};
```

## 🔧 Konfiguracja i Zarządzanie

### Dostępne modele STT

```bash
# Lista modeli NVIDIA
curl http://localhost:8888/models

# Odpowiedź:
{
  "available_models": ["fastconformer", "quartznet"],
  "default_model": "fastconformer",
  "supported_languages": ["pl", "en", "de", "fr", "es"],
  "hvac_keywords_count": 25
}
```

### Obsługiwane formaty audio

```bash
# Formaty konwertera
curl http://localhost:8081/formats

# Odpowiedź:
{
  "input": ["m4a", "mp3", "wav", "flac", "aac", "ogg"],
  "output": ["wav", "mp3", "flac"]
}
```

## 🧪 Przykłady Testowe

### Test kompletnego pipeline'u

```bash
#!/bin/bash

# 1. Upload pliku M4A
echo "🎤 Upload pliku M4A..."
response=$(curl -s -X POST \
  -F "audio_file=@test.m4a" \
  -F "email_source=<EMAIL>" \
  http://localhost:8080/api/transcription/upload)

# 2. Wyodrębnienie job_id
job_id=$(echo $response | jq -r '.result.job_id')
echo "📋 Job ID: $job_id"

# 3. Oczekiwanie na zakończenie
echo "⏳ Oczekiwanie na zakończenie..."
while true; do
    status=$(curl -s http://localhost:8080/api/transcription/status/$job_id | jq -r '.result.status')
    
    if [ "$status" = "completed" ]; then
        echo "✅ Transkrypcja zakończona!"
        break
    elif [ "$status" = "failed" ]; then
        echo "❌ Transkrypcja nieudana!"
        break
    fi
    
    sleep 5
done

# 4. Pobranie wyników
echo "📊 Pobieranie wyników..."
curl -s http://localhost:8080/api/transcription/status/$job_id | jq '.result'
```

### Test analizy AI

```bash
# Test analizy polskiego tekstu HVAC
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "transcript": "Witam, klimatyzacja Samsung przestała działać po burzy. Migają diody i słychać dziwne dźwięki. To pilne bo mamy dzieci.",
    "source_file": "pilne_naprawa.m4a",
    "customer_context": {
      "customer_type": "residential",
      "priority": "high"
    },
    "hvac_context": true
  }' \
  http://localhost:8083/analyze | jq '.'
```

## 🔍 Debugging i Troubleshooting

### Sprawdzenie logów

```bash
# Logi wszystkich serwisów
docker-compose logs -f

# Logi konkretnego serwisu
docker-compose logs -f nvidia-stt-polish
docker-compose logs -f transcription-orchestrator
```

### Test połączeń

```bash
# Test Redis
redis-cli -h localhost -p 6379 ping

# Test PostgreSQL
psql -h ************** -U koldbringer -d hvac_crm -c "SELECT 1;"

# Test LM Studio
curl http://*************:1234/v1/models
```

### Restart serwisów

```bash
# Restart konkretnego serwisu
docker-compose restart transcription-orchestrator

# Restart całego systemu
docker-compose down && docker-compose up -d
```

## 📈 Metryki Wydajności

### Typowe czasy przetwarzania

- **Konwersja M4A→WAV**: 2-5s dla 1min audio
- **NVIDIA STT**: 10-30s dla 1min audio (z GPU)
- **Gemma analiza**: 5-15s dla transkrypcji
- **Całkowity pipeline**: 20-60s dla 1min audio

### Monitoring zasobów

```bash
# Wykorzystanie kontenerów
docker stats --no-stream

# Wykorzystanie GPU (jeśli dostępne)
nvidia-smi
```

---

## 🎯 Gotowe Skrypty

Wszystkie powyższe przykłady są dostępne jako gotowe skrypty w katalogu `examples/`:

- `test_upload.sh` - Test uploadu i transkrypcji
- `test_analysis.sh` - Test analizy AI
- `monitor_system.sh` - Monitoring systemu
- `batch_process.sh` - Przetwarzanie wsadowe

**Uruchom system i ciesz się automatyczną transkrypcją!** 🎤🏠❄️
