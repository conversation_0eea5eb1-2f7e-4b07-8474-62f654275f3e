FROM python:3.11-slim

# 🔧 Instalacja zależności systemowych
RUN apt-get update && apt-get install -y \
    && rm -rf /var/lib/apt/lists/*

# 📁 Utworzenie katalogu roboczego
WORKDIR /app

# 📦 Kopiowanie requirements
COPY requirements.txt .

# 🔧 Instalacja zależności Python
RUN pip install --no-cache-dir -r requirements.txt

# 📁 Kopiowanie kodu aplikacji
COPY . .

# 📁 Utworzenie katalogów
RUN mkdir -p /app/attachments /app/logs /app/temp

# 🚀 Uruchomienie aplikacji
CMD ["python", "email_processor.py"]
