#!/usr/bin/env python3
"""
📊 Ultimate Dashboard 2.0 - COSMIC INTERFACE REVOLUTION
Next-generation monitoring dashboard with advanced features and cosmic design
"""

import asyncio
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import base64
import io
import os
import tempfile
from pathlib import Path
import librosa
import soundfile as sf
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet
import zipfile

from ultimate_system_integrator import UltimateSystemIntegrator

# 🌍 Multi-language support
LANGUAGES = {
    "en": {
        "title": "🚀 NVIDIA STT Ultimate Dashboard 2.0",
        "system_overview": "🎯 System Overview",
        "real_time_metrics": "📊 Real-Time Performance Metrics",
        "historical_trends": "📈 Historical Trends (24h)",
        "service_health": "🏥 Service Health",
        "ai_insights": "🧠 AI Insights",
        "recommendations": "💡 Smart Recommendations",
        "alerts": "🚨 Recent Alerts",
        "optimization": "🎯 Optimization Opportunities",
        "upload_audio": "🎤 Upload Audio for Testing",
        "hvac_glossary": "🔧 HVAC Terminology Glossary",
        "export_reports": "📤 Export Reports",
        "settings": "⚙️ Settings",
        "theme": "🎨 Theme",
        "language": "🌍 Language",
        "alerts_config": "🚨 Alert Configuration"
    },
    "pl": {
        "title": "🚀 NVIDIA STT Ultimate Dashboard 2.0",
        "system_overview": "🎯 Przegląd Systemu",
        "real_time_metrics": "📊 Metryki Czasu Rzeczywistego",
        "historical_trends": "📈 Trendy Historyczne (24h)",
        "service_health": "🏥 Stan Serwisów",
        "ai_insights": "🧠 Analizy AI",
        "recommendations": "💡 Inteligentne Rekomendacje",
        "alerts": "🚨 Ostatnie Alerty",
        "optimization": "🎯 Możliwości Optymalizacji",
        "upload_audio": "🎤 Prześlij Audio do Testów",
        "hvac_glossary": "🔧 Słownik Terminologii HVAC",
        "export_reports": "📤 Eksport Raportów",
        "settings": "⚙️ Ustawienia",
        "theme": "🎨 Motyw",
        "language": "🌍 Język",
        "alerts_config": "🚨 Konfiguracja Alertów"
    }
}

# 🔧 HVAC Glossary Data
HVAC_GLOSSARY = {
    "en": {
        "klimatyzacja": "Air conditioning - System for cooling and dehumidifying indoor air",
        "pompa ciepła": "Heat pump - Device that transfers heat from one location to another",
        "VRF": "Variable Refrigerant Flow - Advanced HVAC system with variable refrigerant flow",
        "split": "Split system - Air conditioning system with separate indoor and outdoor units",
        "kompresor": "Compressor - Device that compresses refrigerant gas in HVAC systems",
        "parownik": "Evaporator - Component that absorbs heat from indoor air",
        "skraplacz": "Condenser - Component that releases heat to outdoor air",
        "freon": "Refrigerant - Chemical compound used for heat transfer in HVAC systems",
        "serwis": "Service - Maintenance and repair of HVAC equipment",
        "instalacja": "Installation - Process of setting up HVAC equipment"
    },
    "pl": {
        "klimatyzacja": "System chłodzenia i osuszania powietrza w pomieszczeniach",
        "pompa ciepła": "Urządzenie przenoszące ciepło z jednego miejsca do drugiego",
        "VRF": "Variable Refrigerant Flow - Zaawansowany system HVAC ze zmiennym przepływem czynnika",
        "split": "System klimatyzacji z oddzielnymi jednostkami wewnętrzną i zewnętrzną",
        "kompresor": "Urządzenie sprężające gaz chłodniczy w systemach HVAC",
        "parownik": "Komponent pochłaniający ciepło z powietrza wewnętrznego",
        "skraplacz": "Komponent oddający ciepło do powietrza zewnętrznego",
        "freon": "Związek chemiczny używany do przenoszenia ciepła w systemach HVAC",
        "serwis": "Konserwacja i naprawa urządzeń HVAC",
        "instalacja": "Proces montażu urządzeń HVAC"
    }
}

# 🎨 Page configuration
st.set_page_config(
    page_title="🚀 NVIDIA STT Ultimate Dashboard",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

def get_theme_css(theme="dark"):
    """🎨 Get theme-specific CSS"""

    if theme == "dark":
        return """
        <style>
        .main-header {
            font-size: 3rem;
            background: linear-gradient(90deg, #00d4ff, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            margin-bottom: 2rem;
            font-weight: bold;
            text-shadow: 0 0 20px rgba(0,212,255,0.5);
        }

        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1.5rem;
            border-radius: 15px;
            color: white;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            margin: 0.5rem 0;
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }

        .status-healthy {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            box-shadow: 0 8px 32px rgba(17,153,142,0.3);
        }

        .status-degraded {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            box-shadow: 0 8px 32px rgba(240,147,251,0.3);
        }

        .status-critical {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            box-shadow: 0 8px 32px rgba(255,65,108,0.3);
        }

        .ai-insight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1rem;
            border-radius: 10px;
            margin: 0.5rem 0;
            color: white;
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }

        .recommendation-card {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 1rem;
            border-radius: 10px;
            margin: 0.5rem 0;
            border-left: 4px solid #ff6b6b;
            color: #333;
        }

        .glossary-term {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.8rem;
            border-radius: 8px;
            margin: 0.3rem 0;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .glossary-term:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.2);
        }

        .upload-zone {
            border: 2px dashed #667eea;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            background: linear-gradient(135deg, rgba(102,126,234,0.1) 0%, rgba(118,75,162,0.1) 100%);
            margin: 1rem 0;
            transition: all 0.3s ease;
        }

        .upload-zone:hover {
            border-color: #764ba2;
            background: linear-gradient(135deg, rgba(102,126,234,0.2) 0%, rgba(118,75,162,0.2) 100%);
        }

        .resource-widget {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1rem;
            border-radius: 10px;
            color: white;
            margin: 0.5rem 0;
            text-align: center;
        }

        .alert-config {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            padding: 1rem;
            border-radius: 10px;
            margin: 0.5rem 0;
            color: #333;
        }

        .waveform-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
        </style>
        """
    else:  # light theme
        return """
        <style>
        .main-header {
            font-size: 3rem;
            background: linear-gradient(90deg, #1f77b4, #ff7f0e, #2ca02c);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            margin-bottom: 2rem;
            font-weight: bold;
        }

        .metric-card {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            padding: 1.5rem;
            border-radius: 15px;
            color: #333;
            text-align: center;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            margin: 0.5rem 0;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .status-healthy {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            color: #2e7d32;
        }

        .status-degraded {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
            color: #f57c00;
        }

        .status-critical {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            color: #d32f2f;
        }

        .ai-insight {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            padding: 1rem;
            border-radius: 10px;
            margin: 0.5rem 0;
            color: #333;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .recommendation-card {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
            padding: 1rem;
            border-radius: 10px;
            margin: 0.5rem 0;
            border-left: 4px solid #ff9800;
            color: #333;
        }

        .glossary-term {
            background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
            padding: 0.8rem;
            border-radius: 8px;
            margin: 0.3rem 0;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-zone {
            border: 2px dashed #1976d2;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            background: linear-gradient(135deg, rgba(25,118,210,0.1) 0%, rgba(25,118,210,0.05) 100%);
            margin: 1rem 0;
        }

        .resource-widget {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            padding: 1rem;
            border-radius: 10px;
            color: #333;
            margin: 0.5rem 0;
            text-align: center;
            border: 1px solid rgba(0,0,0,0.1);
        }
        </style>
        """

class UltimateDashboard:
    """📊 Ultimate Dashboard 2.0 with Advanced Features"""

    def __init__(self):
        self.integrator = UltimateSystemIntegrator()

        # 🌍 Initialize session state for user preferences
        if 'language' not in st.session_state:
            st.session_state.language = 'en'
        if 'theme' not in st.session_state:
            st.session_state.theme = 'dark'
        if 'alert_thresholds' not in st.session_state:
            st.session_state.alert_thresholds = {
                'accuracy_min': 90.0,
                'processing_time_max': 30.0,
                'confidence_min': 0.8,
                'gpu_utilization_max': 90.0
            }
        if 'uploaded_files' not in st.session_state:
            st.session_state.uploaded_files = []
        if 'transcription_history' not in st.session_state:
            st.session_state.transcription_history = []

    def get_text(self, key: str) -> str:
        """🌍 Get localized text"""
        return LANGUAGES[st.session_state.language].get(key, key)

    def render_audio_waveform(self, audio_data: np.ndarray, sample_rate: int = 16000):
        """🎵 Render audio waveform visualization"""

        # Create time axis
        time_axis = np.linspace(0, len(audio_data) / sample_rate, len(audio_data))

        # Create waveform plot
        fig = go.Figure()

        fig.add_trace(go.Scatter(
            x=time_axis,
            y=audio_data,
            mode='lines',
            name='Waveform',
            line=dict(color='#00d4ff', width=1),
            fill='tonexty',
            fillcolor='rgba(0, 212, 255, 0.3)'
        ))

        fig.update_layout(
            title="🎵 Audio Waveform",
            xaxis_title="Time (seconds)",
            yaxis_title="Amplitude",
            height=300,
            showlegend=False,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white' if st.session_state.theme == 'dark' else 'black')
        )

        return fig

    def render_hvac_glossary(self):
        """🔧 Render interactive HVAC glossary"""

        st.markdown(f"## {self.get_text('hvac_glossary')}")

        # Search functionality
        search_term = st.text_input("🔍 Search HVAC terms", placeholder="Enter term to search...")

        glossary = HVAC_GLOSSARY[st.session_state.language]

        # Filter terms based on search
        if search_term:
            filtered_terms = {k: v for k, v in glossary.items()
                            if search_term.lower() in k.lower() or search_term.lower() in v.lower()}
        else:
            filtered_terms = glossary

        # Display terms in expandable cards
        for term, definition in filtered_terms.items():
            with st.expander(f"🔧 {term.title()}", expanded=False):
                st.markdown(f"""
                <div class="glossary-term">
                    <strong>{term}</strong><br>
                    {definition}
                </div>
                """, unsafe_allow_html=True)

    def render_audio_upload_zone(self):
        """🎤 Render drag-and-drop audio upload interface"""

        st.markdown(f"## {self.get_text('upload_audio')}")

        # File uploader with drag-and-drop
        uploaded_files = st.file_uploader(
            "🎤 Drop audio files here or click to browse",
            type=['wav', 'mp3', 'm4a', 'flac'],
            accept_multiple_files=True,
            help="Supported formats: WAV, MP3, M4A, FLAC"
        )

        if uploaded_files:
            st.success(f"✅ {len(uploaded_files)} file(s) uploaded successfully!")

            for uploaded_file in uploaded_files:
                with st.expander(f"🎵 {uploaded_file.name}", expanded=True):
                    col1, col2, col3 = st.columns([2, 1, 1])

                    with col1:
                        # Audio player
                        st.audio(uploaded_file, format='audio/wav')

                    with col2:
                        # File info
                        st.write(f"**Size:** {uploaded_file.size / 1024:.1f} KB")
                        st.write(f"**Type:** {uploaded_file.type}")

                    with col3:
                        # Transcribe button
                        if st.button(f"🎤 Transcribe", key=f"transcribe_{uploaded_file.name}"):
                            with st.spinner("🎤 Transcribing audio..."):
                                # Save uploaded file temporarily
                                with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
                                    tmp_file.write(uploaded_file.getvalue())
                                    tmp_file_path = tmp_file.name

                                try:
                                    # Load audio for waveform
                                    audio_data, sample_rate = librosa.load(tmp_file_path, sr=16000)

                                    # Display waveform
                                    st.markdown('<div class="waveform-container">', unsafe_allow_html=True)
                                    waveform_fig = self.render_audio_waveform(audio_data, sample_rate)
                                    st.plotly_chart(waveform_fig, use_container_width=True)
                                    st.markdown('</div>', unsafe_allow_html=True)

                                    # Mock transcription result (replace with actual STT call)
                                    transcription_result = {
                                        "transcript": "Dzień dobry, mam problem z klimatyzacją LG. Urządzenie nie chłodzi prawidłowo.",
                                        "confidence": 0.92,
                                        "processing_time": 2.3,
                                        "hvac_keywords": ["klimatyzacja", "LG", "problem"],
                                        "timestamp": datetime.now().isoformat()
                                    }

                                    # Display results
                                    st.success("✅ Transcription completed!")
                                    st.write(f"**Transcript:** {transcription_result['transcript']}")
                                    st.write(f"**Confidence:** {transcription_result['confidence']:.2f}")
                                    st.write(f"**Processing Time:** {transcription_result['processing_time']:.1f}s")
                                    st.write(f"**HVAC Keywords:** {', '.join(transcription_result['hvac_keywords'])}")

                                    # Add to history
                                    st.session_state.transcription_history.append({
                                        "filename": uploaded_file.name,
                                        "result": transcription_result
                                    })

                                except Exception as e:
                                    st.error(f"❌ Transcription failed: {e}")
                                finally:
                                    # Cleanup temp file
                                    if os.path.exists(tmp_file_path):
                                        os.unlink(tmp_file_path)

    def render_live_resource_monitoring(self):
        """🖥️ Render live system resource monitoring"""

        st.markdown("## 🖥️ Live System Resources")

        # Mock resource data (replace with actual system monitoring)
        cpu_usage = np.random.uniform(20, 80)
        memory_usage = np.random.uniform(30, 90)
        gpu_usage = np.random.uniform(10, 95)
        disk_usage = np.random.uniform(40, 70)

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.markdown(f"""
            <div class="resource-widget">
                <h4>💻 CPU Usage</h4>
                <h2>{cpu_usage:.1f}%</h2>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            st.markdown(f"""
            <div class="resource-widget">
                <h4>🧠 Memory</h4>
                <h2>{memory_usage:.1f}%</h2>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            st.markdown(f"""
            <div class="resource-widget">
                <h4>🎮 GPU</h4>
                <h2>{gpu_usage:.1f}%</h2>
            </div>
            """, unsafe_allow_html=True)

        with col4:
            st.markdown(f"""
            <div class="resource-widget">
                <h4>💾 Disk</h4>
                <h2>{disk_usage:.1f}%</h2>
            </div>
            """, unsafe_allow_html=True)

        # Resource usage chart
        fig = go.Figure()

        resources = ['CPU', 'Memory', 'GPU', 'Disk']
        values = [cpu_usage, memory_usage, gpu_usage, disk_usage]
        colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4']

        fig.add_trace(go.Bar(
            x=resources,
            y=values,
            marker_color=colors,
            text=[f"{v:.1f}%" for v in values],
            textposition='auto'
        ))

        fig.update_layout(
            title="System Resource Usage",
            yaxis_title="Usage (%)",
            height=300,
            showlegend=False,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white' if st.session_state.theme == 'dark' else 'black')
        )

        st.plotly_chart(fig, use_container_width=True)

    def render_export_functionality(self, dashboard_data: dict):
        """📤 Render export functionality"""

        st.markdown(f"## {self.get_text('export_reports')}")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📄 Export PDF Report", type="primary"):
                pdf_data = self.generate_pdf_report(dashboard_data)
                st.download_button(
                    label="📥 Download PDF",
                    data=pdf_data,
                    file_name=f"nvidia_stt_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                    mime="application/pdf"
                )

        with col2:
            if st.button("📊 Export CSV Data", type="primary"):
                csv_data = self.generate_csv_export(dashboard_data)
                st.download_button(
                    label="📥 Download CSV",
                    data=csv_data,
                    file_name=f"nvidia_stt_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )

        with col3:
            if st.button("🔧 Export JSON Config", type="primary"):
                json_data = self.generate_json_export(dashboard_data)
                st.download_button(
                    label="📥 Download JSON",
                    data=json_data,
                    file_name=f"nvidia_stt_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )

    def generate_pdf_report(self, dashboard_data: dict) -> bytes:
        """📄 Generate PDF report"""

        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=letter)
        styles = getSampleStyleSheet()
        story = []

        # Title
        title = Paragraph("NVIDIA STT Ultimate Dashboard Report", styles['Title'])
        story.append(title)
        story.append(Spacer(1, 12))

        # System overview
        overview = dashboard_data.get("system_overview", {})
        story.append(Paragraph("System Overview", styles['Heading1']))
        story.append(Paragraph(f"Status: {overview.get('status', 'Unknown')}", styles['Normal']))
        story.append(Paragraph(f"Grade: {overview.get('grade', 'N/A')}", styles['Normal']))
        story.append(Paragraph(f"Uptime: {overview.get('uptime', 'N/A')}", styles['Normal']))
        story.append(Spacer(1, 12))

        # Performance metrics
        metrics = dashboard_data.get("real_time_metrics", {})
        if metrics:
            story.append(Paragraph("Performance Metrics", styles['Heading1']))
            accuracy = metrics.get("accuracy", {})
            story.append(Paragraph(f"Word Accuracy: {accuracy.get('word_accuracy', 0):.1f}%", styles['Normal']))
            story.append(Paragraph(f"HVAC Accuracy: {accuracy.get('hvac_accuracy', 0):.1f}%", styles['Normal']))

        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()

    def generate_csv_export(self, dashboard_data: dict) -> str:
        """📊 Generate CSV export"""

        # Create DataFrame from dashboard data
        data = []

        # Add system overview
        overview = dashboard_data.get("system_overview", {})
        data.append({
            "Category": "System",
            "Metric": "Status",
            "Value": overview.get("status", "Unknown"),
            "Timestamp": datetime.now().isoformat()
        })

        data.append({
            "Category": "System",
            "Metric": "Grade",
            "Value": overview.get("grade", "N/A"),
            "Timestamp": datetime.now().isoformat()
        })

        # Add performance metrics
        metrics = dashboard_data.get("real_time_metrics", {})
        if metrics:
            accuracy = metrics.get("accuracy", {})
            performance = metrics.get("performance", {})

            data.append({
                "Category": "Performance",
                "Metric": "Word Accuracy",
                "Value": accuracy.get("word_accuracy", 0),
                "Timestamp": datetime.now().isoformat()
            })

            data.append({
                "Category": "Performance",
                "Metric": "Processing Time",
                "Value": performance.get("processing_time", 0),
                "Timestamp": datetime.now().isoformat()
            })

        df = pd.DataFrame(data)
        return df.to_csv(index=False)

    def generate_json_export(self, dashboard_data: dict) -> str:
        """🔧 Generate JSON export"""

        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "dashboard_data": dashboard_data,
            "user_preferences": {
                "language": st.session_state.language,
                "theme": st.session_state.theme,
                "alert_thresholds": st.session_state.alert_thresholds
            },
            "transcription_history": st.session_state.transcription_history
        }

        return json.dumps(export_data, indent=2, ensure_ascii=False)

    def render_enhanced_sidebar(self):
        """🎛️ Enhanced sidebar with all controls"""

        st.sidebar.markdown("## 🎛️ Ultimate Dashboard Controls")

        # Language selector
        st.sidebar.markdown("### 🌍 Language / Język")
        language_options = {"English": "en", "Polski": "pl"}
        selected_language = st.sidebar.selectbox(
            "Select Language",
            options=list(language_options.keys()),
            index=0 if st.session_state.language == "en" else 1
        )
        st.session_state.language = language_options[selected_language]

        # Theme selector
        st.sidebar.markdown("### 🎨 Theme / Motyw")
        theme_options = {"Dark": "dark", "Light": "light"}
        selected_theme = st.sidebar.selectbox(
            "Select Theme",
            options=list(theme_options.keys()),
            index=0 if st.session_state.theme == "dark" else 1
        )
        st.session_state.theme = theme_options[selected_theme]

        # Auto refresh
        st.sidebar.markdown("### 🔄 Auto Refresh")
        auto_refresh = st.sidebar.checkbox("🔄 Enable Auto Refresh", value=True)
        refresh_interval = st.sidebar.slider("Refresh Interval (seconds)", 10, 300, 60)

        # Manual refresh
        if st.sidebar.button("🔄 Refresh Now", type="primary"):
            st.experimental_rerun()

        # Alert configuration
        st.sidebar.markdown("### 🚨 Alert Thresholds")

        st.session_state.alert_thresholds['accuracy_min'] = st.sidebar.slider(
            "Min Accuracy (%)", 70.0, 100.0,
            st.session_state.alert_thresholds['accuracy_min']
        )

        st.session_state.alert_thresholds['processing_time_max'] = st.sidebar.slider(
            "Max Processing Time (s)", 10.0, 60.0,
            st.session_state.alert_thresholds['processing_time_max']
        )

        st.session_state.alert_thresholds['confidence_min'] = st.sidebar.slider(
            "Min Confidence", 0.5, 1.0,
            st.session_state.alert_thresholds['confidence_min']
        )

        st.session_state.alert_thresholds['gpu_utilization_max'] = st.sidebar.slider(
            "Max GPU Usage (%)", 50.0, 100.0,
            st.session_state.alert_thresholds['gpu_utilization_max']
        )

        # Time range selector
        st.sidebar.markdown("### 📅 Time Range")
        time_range = st.sidebar.selectbox(
            "Select Range",
            ["Last Hour", "Last 6 Hours", "Last 24 Hours", "Last 7 Days"]
        )

        # Metric filters
        st.sidebar.markdown("### 📊 Metrics Filter")
        show_accuracy = st.sidebar.checkbox("📝 Accuracy Metrics", value=True)
        show_performance = st.sidebar.checkbox("⚡ Performance Metrics", value=True)
        show_resources = st.sidebar.checkbox("🖥️ Resource Metrics", value=True)
        show_ai_insights = st.sidebar.checkbox("🧠 AI Insights", value=True)

        # Integration status
        st.sidebar.markdown("### 🔗 Integration Status")

        # Mock integration status (replace with actual checks)
        services = {
            "NVIDIA STT": "🟢 Online",
            "Orchestrator": "🟢 Online",
            "Gemma AI": "🟡 Degraded",
            "GoBackend": "🟢 Online"
        }

        for service, status in services.items():
            st.sidebar.markdown(f"**{service}:** {status}")

        # System info
        st.sidebar.markdown("### ℹ️ System Info")
        st.sidebar.markdown(f"**Version:** 2.0.0")
        st.sidebar.markdown(f"**Build:** {datetime.now().strftime('%Y%m%d')}")
        st.sidebar.markdown(f"**Uptime:** 99.9%")

        return {
            "auto_refresh": auto_refresh,
            "refresh_interval": refresh_interval,
            "time_range": time_range,
            "filters": {
                "accuracy": show_accuracy,
                "performance": show_performance,
                "resources": show_resources,
                "ai_insights": show_ai_insights
            }
        }

    async def render_dashboard(self):
        """🎨 Render Ultimate Dashboard 2.0"""

        # Apply theme CSS
        st.markdown(get_theme_css(st.session_state.theme), unsafe_allow_html=True)

        # Header with localization
        st.markdown(f'<h1 class="main-header">{self.get_text("title")}</h1>',
                   unsafe_allow_html=True)

        # Enhanced sidebar
        sidebar_config = self.render_enhanced_sidebar()

        # Main dashboard tabs
        tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
            "📊 Dashboard",
            "🎤 Audio Upload",
            "🔧 HVAC Glossary",
            "📤 Export",
            "🖥️ Resources",
            "⚙️ Settings"
        ])

        with tab1:
            # Get dashboard data
            try:
                dashboard_data = await self.integrator.generate_ultimate_dashboard_data()
            except Exception as e:
                st.error(f"❌ Failed to load dashboard data: {e}")
                dashboard_data = {}

            if dashboard_data:
                # System overview
                self.render_system_overview(dashboard_data.get("system_overview", {}))

                # Main content layout
                col1, col2 = st.columns([2, 1])

                with col1:
                    if sidebar_config["filters"]["performance"]:
                        self.render_real_time_metrics(dashboard_data.get("real_time_metrics", {}))

                    if sidebar_config["filters"]["accuracy"]:
                        self.render_historical_trends(dashboard_data.get("historical_trends", {}))

                with col2:
                    self.render_service_health(dashboard_data.get("service_health", {}))

                    if sidebar_config["filters"]["ai_insights"]:
                        self.render_ai_insights(dashboard_data.get("ai_insights", {}))

                # Full width sections
                self.render_recommendations(dashboard_data.get("recommendations", []))
                self.render_alerts_and_optimization(
                    dashboard_data.get("alerts", []),
                    dashboard_data.get("optimization_opportunities", [])
                )

        with tab2:
            # Audio upload and transcription
            self.render_audio_upload_zone()

            # Transcription history
            if st.session_state.transcription_history:
                st.markdown("## 📜 Transcription History")

                # Search and filter
                search_query = st.text_input("🔍 Search transcriptions", placeholder="Search by filename or content...")

                filtered_history = st.session_state.transcription_history
                if search_query:
                    filtered_history = [
                        item for item in st.session_state.transcription_history
                        if search_query.lower() in item["filename"].lower() or
                           search_query.lower() in item["result"]["transcript"].lower()
                    ]

                # Display history
                for i, item in enumerate(reversed(filtered_history[-10:])):  # Last 10 items
                    with st.expander(f"🎵 {item['filename']} - {item['result']['timestamp'][:19]}", expanded=False):
                        st.write(f"**Transcript:** {item['result']['transcript']}")
                        st.write(f"**Confidence:** {item['result']['confidence']:.2f}")
                        st.write(f"**Processing Time:** {item['result']['processing_time']:.1f}s")
                        st.write(f"**HVAC Keywords:** {', '.join(item['result']['hvac_keywords'])}")

        with tab3:
            # HVAC Glossary
            self.render_hvac_glossary()

        with tab4:
            # Export functionality
            if dashboard_data:
                self.render_export_functionality(dashboard_data)
            else:
                st.warning("📊 No data available for export. Please ensure the system is running.")

        with tab5:
            # Live resource monitoring
            if sidebar_config["filters"]["resources"]:
                self.render_live_resource_monitoring()
            else:
                st.info("🖥️ Resource monitoring is disabled. Enable it in the sidebar filters.")

        with tab6:
            # Settings and configuration
            st.markdown("## ⚙️ Advanced Settings")

            # Alert configuration
            st.markdown("### 🚨 Alert Configuration")
            st.markdown(f"""
            <div class="alert-config">
                <h4>Current Alert Thresholds</h4>
                <p><strong>Min Accuracy:</strong> {st.session_state.alert_thresholds['accuracy_min']:.1f}%</p>
                <p><strong>Max Processing Time:</strong> {st.session_state.alert_thresholds['processing_time_max']:.1f}s</p>
                <p><strong>Min Confidence:</strong> {st.session_state.alert_thresholds['confidence_min']:.2f}</p>
                <p><strong>Max GPU Usage:</strong> {st.session_state.alert_thresholds['gpu_utilization_max']:.1f}%</p>
            </div>
            """, unsafe_allow_html=True)

            # System configuration
            st.markdown("### 🔧 System Configuration")

            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**Service Endpoints:**")
                st.code("""
NVIDIA STT: http://localhost:8889
Orchestrator: http://localhost:9000
Gemma AI: http://*************:1234
GoBackend: http://localhost:8080
                """)

            with col2:
                st.markdown("**Performance Targets:**")
                st.code("""
Word Accuracy: >95%
HVAC Accuracy: >92%
Processing Time: <30s
RTF Factor: >2000
Confidence: >0.85
                """)

            # Reset settings
            if st.button("🔄 Reset to Defaults", type="secondary"):
                st.session_state.alert_thresholds = {
                    'accuracy_min': 90.0,
                    'processing_time_max': 30.0,
                    'confidence_min': 0.8,
                    'gpu_utilization_max': 90.0
                }
                st.session_state.language = 'en'
                st.session_state.theme = 'dark'
                st.success("✅ Settings reset to defaults!")
                st.experimental_rerun()

        # Auto refresh functionality
        if sidebar_config["auto_refresh"]:
            import time
            time.sleep(sidebar_config["refresh_interval"])
            st.experimental_rerun()

# 🚀 Main dashboard execution
async def main():
    """🎯 Main dashboard function"""

    dashboard = UltimateDashboard()
    await dashboard.render_dashboard()

# Run the dashboard
if __name__ == "__main__":
    asyncio.run(main())
        """🎛️ Sidebar z kontrolkami"""
        
        st.sidebar.markdown("## 🎛️ Dashboard Controls")
        
        # Auto refresh
        auto_refresh = st.sidebar.checkbox("🔄 Auto Refresh", value=True)
        refresh_interval = st.sidebar.slider("Refresh Interval (seconds)", 10, 300, 60)
        
        # Manual refresh
        if st.sidebar.button("🔄 Refresh Now", type="primary"):
            st.experimental_rerun()
        
        # Time range selector
        st.sidebar.markdown("### 📅 Time Range")
        time_range = st.sidebar.selectbox(
            "Select Range",
            ["Last Hour", "Last 6 Hours", "Last 24 Hours", "Last 7 Days"]
        )
        
        # Metric filters
        st.sidebar.markdown("### 📊 Metrics Filter")
        show_accuracy = st.sidebar.checkbox("📝 Accuracy Metrics", value=True)
        show_performance = st.sidebar.checkbox("⚡ Performance Metrics", value=True)
        show_resources = st.sidebar.checkbox("🖥️ Resource Metrics", value=True)
        
        # Alert settings
        st.sidebar.markdown("### 🚨 Alert Settings")
        alert_threshold = st.sidebar.slider("Alert Threshold", 0.0, 1.0, 0.8)
        
        # Export options
        st.sidebar.markdown("### 📤 Export")
        if st.sidebar.button("📊 Export Report"):
            st.sidebar.success("Report exported!")
        
        if st.sidebar.button("📈 Export Charts"):
            st.sidebar.success("Charts exported!")
    
    def render_system_overview(self, overview_data: dict):
        """🎯 System overview section"""
        
        st.markdown("## 🎯 System Overview")
        
        col1, col2, col3, col4, col5 = st.columns(5)
        
        # Overall status
        with col1:
            status = overview_data.get("status", "unknown")
            status_class = f"status-{status}" if status in ["healthy", "degraded", "critical"] else "metric-card"
            
            st.markdown(f"""
            <div class="metric-card {status_class}">
                <h3>🎯 Status</h3>
                <h2>{status.upper()}</h2>
            </div>
            """, unsafe_allow_html=True)
        
        # Performance grade
        with col2:
            grade = overview_data.get("grade", "N/A")
            grade_class = "performance-excellent" if grade in ["A+", "A"] else \
                         "performance-good" if grade in ["B+", "B"] else "performance-poor"
            
            st.markdown(f"""
            <div class="metric-card {grade_class}">
                <h3>🏆 Grade</h3>
                <h2>{grade}</h2>
            </div>
            """, unsafe_allow_html=True)
        
        # Uptime
        with col3:
            uptime = overview_data.get("uptime", "N/A")
            st.markdown(f"""
            <div class="metric-card">
                <h3>⏱️ Uptime</h3>
                <h2>{uptime}</h2>
            </div>
            """, unsafe_allow_html=True)
        
        # Total requests
        with col4:
            requests = overview_data.get("total_requests", 0)
            st.markdown(f"""
            <div class="metric-card">
                <h3>📊 Requests</h3>
                <h2>{requests:,}</h2>
            </div>
            """, unsafe_allow_html=True)
        
        # Last updated
        with col5:
            last_updated = overview_data.get("last_updated", "")
            if last_updated:
                update_time = datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
                time_ago = datetime.now() - update_time.replace(tzinfo=None)
                time_str = f"{int(time_ago.total_seconds())}s ago"
            else:
                time_str = "N/A"
            
            st.markdown(f"""
            <div class="metric-card">
                <h3>🕐 Updated</h3>
                <h2>{time_str}</h2>
            </div>
            """, unsafe_allow_html=True)
    
    def render_real_time_metrics(self, metrics_data: dict):
        """📊 Real-time metrics visualization"""
        
        st.markdown("## 📊 Real-Time Performance Metrics")
        
        if not metrics_data:
            st.warning("📊 No real-time metrics available")
            return
        
        # Create metrics charts
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=("Accuracy Metrics", "Performance Metrics", "Resource Usage", "Quality Scores"),
            specs=[[{"type": "indicator"}, {"type": "indicator"}],
                   [{"type": "bar"}, {"type": "scatter"}]]
        )
        
        # Accuracy gauge
        accuracy_data = metrics_data.get("accuracy", {})
        word_acc = accuracy_data.get("word_accuracy", 0)
        
        fig.add_trace(
            go.Indicator(
                mode="gauge+number+delta",
                value=word_acc,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "Word Accuracy (%)"},
                delta={'reference': 95},
                gauge={
                    'axis': {'range': [None, 100]},
                    'bar': {'color': "darkblue"},
                    'steps': [
                        {'range': [0, 70], 'color': "lightgray"},
                        {'range': [70, 85], 'color': "yellow"},
                        {'range': [85, 95], 'color': "orange"},
                        {'range': [95, 100], 'color': "green"}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': 95
                    }
                }
            ),
            row=1, col=1
        )
        
        # Performance gauge
        performance_data = metrics_data.get("performance", {})
        proc_time = performance_data.get("processing_time", 0)
        
        fig.add_trace(
            go.Indicator(
                mode="gauge+number+delta",
                value=proc_time,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "Processing Time (s)"},
                delta={'reference': 30},
                gauge={
                    'axis': {'range': [0, 60]},
                    'bar': {'color': "darkgreen"},
                    'steps': [
                        {'range': [0, 10], 'color': "green"},
                        {'range': [10, 30], 'color': "yellow"},
                        {'range': [30, 60], 'color': "red"}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': 30
                    }
                }
            ),
            row=1, col=2
        )
        
        # Resource usage bar chart
        resources_data = metrics_data.get("resources", {})
        resource_names = ["CPU", "Memory", "GPU", "Disk"]
        resource_values = [
            resources_data.get("cpu_usage", 0),
            resources_data.get("memory_usage", 0),
            resources_data.get("gpu_utilization", 0),
            resources_data.get("disk_usage", 0)
        ]
        
        fig.add_trace(
            go.Bar(
                x=resource_names,
                y=resource_values,
                marker_color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'],
                name="Resource Usage"
            ),
            row=2, col=1
        )
        
        # Quality trend (mock data for now)
        quality_timestamps = [datetime.now() - timedelta(minutes=i*5) for i in range(12, 0, -1)]
        quality_scores = np.random.normal(90, 5, 12)  # Mock quality scores
        
        fig.add_trace(
            go.Scatter(
                x=quality_timestamps,
                y=quality_scores,
                mode='lines+markers',
                name='Quality Score',
                line=dict(color='purple', width=3)
            ),
            row=2, col=2
        )
        
        fig.update_layout(height=600, showlegend=False)
        st.plotly_chart(fig, use_container_width=True)
    
    def render_historical_trends(self, trends_data: dict):
        """📈 Historical trends visualization"""
        
        st.markdown("## 📈 Historical Trends (24h)")
        
        if not trends_data or trends_data.get("status") == "insufficient_data":
            st.info("📈 Insufficient historical data. Run system for longer to see trends.")
            return
        
        # Create trends chart
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=("Word Accuracy", "Processing Time", "RTF Factor", "GPU Utilization"),
            vertical_spacing=0.1
        )
        
        timestamps = [datetime.fromisoformat(ts) for ts in trends_data.get("timestamps", [])]
        
        # Word accuracy trend
        fig.add_trace(
            go.Scatter(
                x=timestamps,
                y=trends_data.get("word_accuracy", []),
                mode='lines+markers',
                name='Word Accuracy',
                line=dict(color='blue', width=2)
            ),
            row=1, col=1
        )
        
        # Processing time trend
        fig.add_trace(
            go.Scatter(
                x=timestamps,
                y=trends_data.get("processing_time", []),
                mode='lines+markers',
                name='Processing Time',
                line=dict(color='orange', width=2)
            ),
            row=1, col=2
        )
        
        # RTF factor trend
        fig.add_trace(
            go.Scatter(
                x=timestamps,
                y=trends_data.get("rtf_factor", []),
                mode='lines+markers',
                name='RTF Factor',
                line=dict(color='green', width=2)
            ),
            row=2, col=1
        )
        
        # GPU utilization trend
        fig.add_trace(
            go.Scatter(
                x=timestamps,
                y=trends_data.get("gpu_utilization", []),
                mode='lines+markers',
                name='GPU Utilization',
                line=dict(color='red', width=2)
            ),
            row=2, col=2
        )
        
        fig.update_layout(height=500, showlegend=False)
        st.plotly_chart(fig, use_container_width=True)
    
    def render_service_health(self, services_data: dict):
        """🏥 Service health status"""
        
        st.markdown("## 🏥 Service Health")
        
        for service_name, service_data in services_data.items():
            status = service_data.get("status", "unknown")
            
            # Status icon and color
            if status == "healthy":
                icon = "✅"
                color = "green"
            elif status == "degraded":
                icon = "⚠️"
                color = "orange"
            elif status == "critical":
                icon = "🔴"
                color = "red"
            else:
                icon = "❓"
                color = "gray"
            
            # Service card
            with st.expander(f"{icon} {service_name.replace('_', ' ').title()}", expanded=True):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write(f"**Status:** {status}")
                    if "response_time" in service_data:
                        st.write(f"**Response Time:** {service_data['response_time']:.3f}s")
                
                with col2:
                    if "last_check" in service_data:
                        st.write(f"**Last Check:** {service_data['last_check']}")
                    if "error" in service_data:
                        st.error(f"Error: {service_data['error']}")
    
    def render_ai_insights(self, insights_data: dict):
        """🧠 AI insights section"""
        
        st.markdown("## 🧠 AI Insights")
        
        # Drift detection
        drift_data = insights_data.get("drift_detection", {})
        if drift_data.get("drift_detected"):
            st.markdown(f"""
            <div class="ai-insight">
                <h4>📊 Data Drift Detected</h4>
                <p>Magnitude: {drift_data.get('drift_magnitude', 0):.3f}</p>
            </div>
            """, unsafe_allow_html=True)
        
        # Anomaly detection
        anomaly_data = insights_data.get("anomaly_detection", {})
        if anomaly_data.get("anomalies_detected"):
            st.markdown(f"""
            <div class="ai-insight">
                <h4>🔍 Anomalies Detected</h4>
                <p>Count: {anomaly_data.get('anomaly_count', 0)}</p>
            </div>
            """, unsafe_allow_html=True)
        
        # Performance trends
        trends = insights_data.get("performance_trends", {})
        if trends.get("status") == "analyzed":
            st.markdown(f"""
            <div class="ai-insight">
                <h4>📈 Performance Trends</h4>
                <p>Accuracy: {trends.get('accuracy_trend', 'stable')}</p>
                <p>Performance: {trends.get('performance_trend', 'stable')}</p>
            </div>
            """, unsafe_allow_html=True)
    
    def render_recommendations(self, recommendations: list):
        """💡 Recommendations section"""
        
        st.markdown("## 💡 Smart Recommendations")
        
        if not recommendations:
            st.info("💡 No recommendations at this time. System is performing well!")
            return
        
        # Group recommendations by priority
        high_priority = [r for r in recommendations if r.get("priority") == "high"]
        medium_priority = [r for r in recommendations if r.get("priority") == "medium"]
        low_priority = [r for r in recommendations if r.get("priority") == "low"]
        
        # High priority recommendations
        if high_priority:
            st.markdown("### 🔴 High Priority")
            for rec in high_priority:
                st.markdown(f"""
                <div class="recommendation-card">
                    <h4>{rec.get('action', 'No action specified')}</h4>
                    <p><strong>Type:</strong> {rec.get('type', 'general')}</p>
                    <p><strong>Impact:</strong> {rec.get('estimated_impact', 'unknown')}</p>
                </div>
                """, unsafe_allow_html=True)
        
        # Medium priority recommendations
        if medium_priority:
            st.markdown("### 🟡 Medium Priority")
            for rec in medium_priority:
                st.markdown(f"""
                <div class="recommendation-card">
                    <h4>{rec.get('action', 'No action specified')}</h4>
                    <p><strong>Type:</strong> {rec.get('type', 'general')}</p>
                </div>
                """, unsafe_allow_html=True)
    
    def render_alerts_and_optimization(self, alerts: list, optimizations: list):
        """🚨 Alerts and optimization opportunities"""
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("## 🚨 Recent Alerts")
            
            if not alerts:
                st.success("🎉 No recent alerts!")
            else:
                for alert in alerts[-5:]:  # Show last 5 alerts
                    level = alert.get("level", "info")
                    message = alert.get("message", "No message")
                    timestamp = alert.get("timestamp", "")
                    
                    if level == "critical":
                        st.error(f"🔴 {message} ({timestamp})")
                    elif level == "warning":
                        st.warning(f"🟡 {message} ({timestamp})")
                    else:
                        st.info(f"🔵 {message} ({timestamp})")
        
        with col2:
            st.markdown("## 🎯 Optimization Opportunities")
            
            if not optimizations:
                st.success("🎉 System is well optimized!")
            else:
                for opt in optimizations:
                    impact = opt.get("impact", "unknown")
                    effort = opt.get("effort", "unknown")
                    description = opt.get("description", "No description")
                    
                    st.markdown(f"""
                    **{description}**  
                    Impact: {impact} | Effort: {effort}
                    """)

# 🚀 Main dashboard execution
async def main():
    """🎯 Main dashboard function"""
    
    dashboard = UltimateDashboard()
    await dashboard.render_dashboard()

# Run the dashboard
if __name__ == "__main__":
    asyncio.run(main())
