"""
🤖 Ollama API Client
Klient do komunikacji z Ollama API dla modelu Gemma3-4b
"""

import aiohttp
import json
import logging
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class OllamaClient:
    def __init__(self, base_url: str, model_name: str = "gemma3:4b"):
        self.base_url = base_url.rstrip('/')
        self.model_name = model_name
        
    async def test_connection(self) -> bool:
        """🔍 Test połączenia z Ollama"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/tags") as response:
                    if response.status == 200:
                        data = await response.json()
                        models = [model.get('name', 'unknown') for model in data.get('models', [])]
                        logger.info(f"✅ Ollama dostępne, modele: {models}")
                        
                        # Sprawdź czy nasz model jest dostępny
                        if any(self.model_name in model for model in models):
                            logger.info(f"✅ Model {self.model_name} jest dostępny")
                            return True
                        else:
                            logger.warning(f"⚠️ Model {self.model_name} nie jest dostępny. Dostępne: {models}")
                            return False
                    else:
                        logger.warning(f"⚠️ Ollama odpowiedział kodem: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"❌ Błąd połączenia z Ollama: {e}")
            return False
    
    async def generate(self, prompt: str, max_tokens: int = 2048, temperature: float = 0.3) -> Optional[str]:
        """🎯 Generowanie odpowiedzi przez Ollama"""
        try:
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "num_predict": max_tokens,
                    "temperature": temperature
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/api/generate",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=120)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('response', '')
                    else:
                        logger.error(f"❌ Błąd generowania: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"❌ Błąd podczas generowania: {e}")
            return None
    
    async def chat(self, messages: List[Dict], max_tokens: int = 2048, temperature: float = 0.3) -> Optional[str]:
        """💬 Chat API dla Ollama"""
        try:
            payload = {
                "model": self.model_name,
                "messages": messages,
                "stream": False,
                "options": {
                    "num_predict": max_tokens,
                    "temperature": temperature
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/api/chat",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=120)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('message', {}).get('content', '')
                    else:
                        logger.error(f"❌ Błąd chat: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"❌ Błąd podczas chat: {e}")
            return None