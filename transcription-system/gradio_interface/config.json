{"services": {"nvidia_stt": "http://localhost:8889", "python_mixer": "http://localhost:8080", "gemma_ai": "http://*************:1234", "database": "******************************************/hvac_crm", "file_storage": "http://**************:9000"}, "themes": {"default": "cosmic_dark", "available": ["cosmic_dark", "cosmic_light", "professional", "colorful"]}, "languages": {"default": "en", "available": ["en", "pl"]}, "features": {"audio_upload": true, "real_time_monitoring": true, "export_functionality": true, "hvac_glossary": true, "ai_insights": true, "multi_language": true, "dark_mode": true, "auto_refresh": true}, "interface": {"title": "🎆 Python_Mixer HVAC CRM - Cosmic Interface", "description": "Advanced Gradio interface for HVAC CRM with cosmic design", "version": "2.0.0", "port": 7860, "host": "0.0.0.0", "debug": true, "share": false}, "audio": {"supported_formats": [".wav", ".mp3", ".m4a", ".flac", ".ogg"], "max_file_size": "50MB", "sample_rate": 16000, "channels": 1, "confidence_threshold": 0.8}, "export": {"formats": ["PDF", "CSV", "JSON", "Excel"], "max_file_size": "100MB", "retention_days": 30, "auto_cleanup": true}, "monitoring": {"refresh_interval": 10, "metrics_retention": "24h", "alert_thresholds": {"cpu_usage": 80, "memory_usage": 85, "response_time": 1000, "error_rate": 1.0}}, "hvac": {"brands": ["LG", "<PERSON>kin", "Samsung", "Mitsubishi", "Fujitsu"], "equipment_types": ["Split System", "Multi Split", "VRF", "Heat Pump"], "keywords": ["klimatyzacja", "klimatyzator", "pompa cie<PERSON>ła", "<PERSON><PERSON><PERSON><PERSON>", "chłodzenie", "ogrzewanie", "ser<PERSON>s", "<PERSON><PERSON>a", "instalacja", "LG", "<PERSON>kin", "Samsung", "Mitsubishi", "Fujitsu", "split", "multi split", "VRF", "VRV", "kompresor", "freon"]}, "security": {"enable_auth": false, "session_timeout": 3600, "max_upload_size": "50MB", "allowed_file_types": [".wav", ".mp3", ".m4a", ".flac", ".ogg"]}, "performance": {"enable_caching": true, "cache_ttl": 300, "max_concurrent_users": 50, "enable_compression": true}, "logging": {"level": "INFO", "file": "logs/gradio_interface.log", "max_size": "10MB", "backup_count": 5}}