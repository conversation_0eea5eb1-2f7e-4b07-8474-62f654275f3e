#!/usr/bin/env python3
"""
🎨 Theme Components for Cosmic Gradio Interface
Golden ratio design with cosmic-level UX and divine quality standards
"""

import gradio as gr
import json
from typing import Dict, Any

class ThemeComponents:
    """🎨 Cosmic Theme Management with Golden Ratio Design"""
    
    def __init__(self):
        self.golden_ratio = 1.618
        self.current_theme = "cosmic_dark"
        self.themes = {
            "cosmic_dark": self.get_cosmic_dark_theme(),
            "cosmic_light": self.get_cosmic_light_theme(),
            "hvac_professional": self.get_hvac_professional_theme(),
            "fulmark_brand": self.get_fulmark_brand_theme()
        }
    
    def get_cosmic_dark_theme(self) -> Dict[str, Any]:
        """🌌 Cosmic Dark Theme with Golden Ratio"""
        return {
            "name": "🌌 Cosmic Dark",
            "css": """
            /* 🌌 Cosmic Dark Theme - Golden Ratio Design */
            :root {
                --golden-ratio: 1.618;
                --cosmic-primary: #667eea;
                --cosmic-secondary: #764ba2;
                --cosmic-accent: #00d4ff;
                --cosmic-success: #11998e;
                --cosmic-warning: #f093fb;
                --cosmic-error: #ff416c;
                --cosmic-bg-primary: #0c0c0c;
                --cosmic-bg-secondary: #1a1a2e;
                --cosmic-bg-tertiary: #16213e;
                --cosmic-text-primary: #ffffff;
                --cosmic-text-secondary: #b8b8b8;
                --cosmic-border: rgba(255,255,255,0.1);
                --cosmic-shadow: rgba(0,0,0,0.3);
                --cosmic-glow: rgba(0,212,255,0.5);
            }
            
            .gradio-container {
                background: linear-gradient(135deg, var(--cosmic-bg-primary) 0%, var(--cosmic-bg-secondary) 50%, var(--cosmic-bg-tertiary) 100%);
                color: var(--cosmic-text-primary);
                font-family: 'Segoe UI', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
                min-height: 100vh;
            }
            
            .cosmic-header {
                background: linear-gradient(90deg, var(--cosmic-accent), #ff6b6b, #4ecdc4);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                text-align: center;
                font-size: calc(2rem * var(--golden-ratio));
                font-weight: 800;
                margin: calc(1rem * var(--golden-ratio)) 0;
                text-shadow: 0 0 20px var(--cosmic-glow);
                letter-spacing: 0.05em;
                animation: cosmic-pulse 3s ease-in-out infinite;
            }
            
            @keyframes cosmic-pulse {
                0%, 100% { opacity: 1; transform: scale(1); }
                50% { opacity: 0.9; transform: scale(1.02); }
            }
            
            .cosmic-card {
                background: linear-gradient(135deg, rgba(102,126,234,0.15) 0%, rgba(118,75,162,0.15) 100%);
                border: 1px solid var(--cosmic-border);
                border-radius: calc(15px * var(--golden-ratio));
                padding: calc(1.5rem * var(--golden-ratio));
                margin: calc(1rem * var(--golden-ratio)) 0;
                backdrop-filter: blur(15px);
                box-shadow: 0 8px 32px var(--cosmic-shadow);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
            }
            
            .cosmic-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
                transition: left 0.6s;
            }
            
            .cosmic-card:hover {
                transform: translateY(-8px) scale(1.02);
                box-shadow: 0 16px 48px rgba(0,212,255,0.25);
                border-color: var(--cosmic-accent);
            }
            
            .cosmic-card:hover::before {
                left: 100%;
            }
            
            .cosmic-button {
                background: linear-gradient(135deg, var(--cosmic-primary) 0%, var(--cosmic-secondary) 100%);
                border: none;
                border-radius: calc(10px * var(--golden-ratio));
                color: white;
                padding: calc(0.8rem * var(--golden-ratio)) calc(1.5rem * var(--golden-ratio));
                font-weight: 600;
                font-size: 1rem;
                cursor: pointer;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: 0 4px 16px rgba(102,126,234,0.3);
                position: relative;
                overflow: hidden;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }
            
            .cosmic-button:hover {
                transform: translateY(-3px) scale(1.05);
                box-shadow: 0 8px 24px rgba(102,126,234,0.4);
                background: linear-gradient(135deg, var(--cosmic-secondary) 0%, var(--cosmic-primary) 100%);
            }
            
            .cosmic-button:active {
                transform: translateY(-1px) scale(1.02);
            }
            
            .status-healthy {
                background: linear-gradient(135deg, var(--cosmic-success) 0%, #38ef7d 100%);
                color: white;
                padding: calc(0.5rem * var(--golden-ratio)) calc(1rem * var(--golden-ratio));
                border-radius: calc(20px * var(--golden-ratio));
                font-weight: 600;
                display: inline-block;
                animation: status-glow 2s ease-in-out infinite;
            }
            
            @keyframes status-glow {
                0%, 100% { box-shadow: 0 0 10px rgba(17,153,142,0.5); }
                50% { box-shadow: 0 0 20px rgba(17,153,142,0.8); }
            }
            
            .status-warning {
                background: linear-gradient(135deg, var(--cosmic-warning) 0%, #f5576c 100%);
                color: white;
                padding: calc(0.5rem * var(--golden-ratio)) calc(1rem * var(--golden-ratio));
                border-radius: calc(20px * var(--golden-ratio));
                font-weight: 600;
                display: inline-block;
                animation: status-pulse 1.5s ease-in-out infinite;
            }
            
            @keyframes status-pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.7; }
            }
            
            .status-error {
                background: linear-gradient(135deg, var(--cosmic-error) 0%, #ff4b2b 100%);
                color: white;
                padding: calc(0.5rem * var(--golden-ratio)) calc(1rem * var(--golden-ratio));
                border-radius: calc(20px * var(--golden-ratio));
                font-weight: 600;
                display: inline-block;
                animation: status-shake 0.5s ease-in-out infinite;
            }
            
            @keyframes status-shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-2px); }
                75% { transform: translateX(2px); }
            }
            
            .metric-display {
                background: linear-gradient(135deg, rgba(0,212,255,0.1) 0%, rgba(255,107,107,0.1) 100%);
                border: 2px solid rgba(0,212,255,0.3);
                border-radius: calc(15px * var(--golden-ratio));
                padding: calc(1.5rem * var(--golden-ratio));
                text-align: center;
                margin: calc(0.5rem * var(--golden-ratio));
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }
            
            .metric-display:hover {
                transform: scale(1.05);
                border-color: var(--cosmic-accent);
                box-shadow: 0 0 30px rgba(0,212,255,0.3);
            }
            
            .metric-display h4 {
                color: var(--cosmic-accent);
                margin: 0 0 calc(0.5rem * var(--golden-ratio)) 0;
                font-size: 1rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.1em;
            }
            
            .metric-display h2 {
                color: var(--cosmic-text-primary);
                margin: 0;
                font-size: calc(2.5rem * var(--golden-ratio));
                font-weight: 800;
                text-shadow: 0 0 10px var(--cosmic-glow);
            }
            
            .waveform-container {
                background: linear-gradient(135deg, rgba(102,126,234,0.1) 0%, rgba(118,75,162,0.1) 100%);
                border: 1px solid var(--cosmic-border);
                border-radius: calc(15px * var(--golden-ratio));
                padding: calc(1.5rem * var(--golden-ratio));
                margin: calc(1rem * var(--golden-ratio)) 0;
                backdrop-filter: blur(10px);
                position: relative;
                overflow: hidden;
            }
            
            .upload-zone {
                border: 3px dashed var(--cosmic-primary);
                border-radius: calc(20px * var(--golden-ratio));
                padding: calc(3rem * var(--golden-ratio));
                text-align: center;
                background: linear-gradient(135deg, rgba(102,126,234,0.1) 0%, rgba(118,75,162,0.1) 100%);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                cursor: pointer;
                position: relative;
                overflow: hidden;
            }
            
            .upload-zone:hover {
                border-color: var(--cosmic-accent);
                background: linear-gradient(135deg, rgba(102,126,234,0.2) 0%, rgba(118,75,162,0.2) 100%);
                transform: scale(1.02);
                box-shadow: 0 0 30px rgba(0,212,255,0.2);
            }
            
            .glossary-term {
                background: linear-gradient(135deg, rgba(17,153,142,0.2) 0%, rgba(56,239,125,0.2) 100%);
                border: 1px solid rgba(17,153,142,0.3);
                border-radius: calc(12px * var(--golden-ratio));
                padding: calc(1.2rem * var(--golden-ratio));
                margin: calc(0.8rem * var(--golden-ratio)) 0;
                cursor: pointer;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
            }
            
            .glossary-term:hover {
                transform: translateX(8px) scale(1.02);
                box-shadow: 0 8px 24px rgba(17,153,142,0.3);
                border-color: var(--cosmic-success);
            }
            
            /* Scrollbar Styling */
            ::-webkit-scrollbar {
                width: 8px;
            }
            
            ::-webkit-scrollbar-track {
                background: var(--cosmic-bg-secondary);
            }
            
            ::-webkit-scrollbar-thumb {
                background: linear-gradient(135deg, var(--cosmic-primary), var(--cosmic-secondary));
                border-radius: 4px;
            }
            
            ::-webkit-scrollbar-thumb:hover {
                background: linear-gradient(135deg, var(--cosmic-secondary), var(--cosmic-primary));
            }
            
            /* Tab Styling */
            .tab-nav {
                background: rgba(102,126,234,0.1);
                border-radius: calc(15px * var(--golden-ratio));
                padding: calc(0.5rem * var(--golden-ratio));
                margin-bottom: calc(1rem * var(--golden-ratio));
            }
            
            .tab-nav button {
                background: transparent;
                border: none;
                color: var(--cosmic-text-secondary);
                padding: calc(0.8rem * var(--golden-ratio)) calc(1.5rem * var(--golden-ratio));
                border-radius: calc(10px * var(--golden-ratio));
                transition: all 0.3s ease;
                font-weight: 500;
            }
            
            .tab-nav button.selected {
                background: linear-gradient(135deg, var(--cosmic-primary), var(--cosmic-secondary));
                color: white;
                box-shadow: 0 4px 16px rgba(102,126,234,0.3);
            }
            
            /* Input Styling */
            input, textarea, select {
                background: rgba(255,255,255,0.05);
                border: 1px solid var(--cosmic-border);
                border-radius: calc(8px * var(--golden-ratio));
                color: var(--cosmic-text-primary);
                padding: calc(0.8rem * var(--golden-ratio));
                transition: all 0.3s ease;
            }
            
            input:focus, textarea:focus, select:focus {
                border-color: var(--cosmic-accent);
                box-shadow: 0 0 20px rgba(0,212,255,0.2);
                outline: none;
            }
            """,
            "colors": {
                "primary": "#667eea",
                "secondary": "#764ba2",
                "accent": "#00d4ff",
                "success": "#11998e",
                "warning": "#f093fb",
                "error": "#ff416c"
            }
        }
    
    def get_cosmic_light_theme(self) -> Dict[str, Any]:
        """☀️ Cosmic Light Theme"""
        return {
            "name": "☀️ Cosmic Light",
            "css": """
            /* ☀️ Cosmic Light Theme - Professional & Clean */
            :root {
                --golden-ratio: 1.618;
                --cosmic-primary: #1f77b4;
                --cosmic-secondary: #ff7f0e;
                --cosmic-accent: #2ca02c;
                --cosmic-success: #17a2b8;
                --cosmic-warning: #ffc107;
                --cosmic-error: #dc3545;
                --cosmic-bg-primary: #ffffff;
                --cosmic-bg-secondary: #f8f9fa;
                --cosmic-bg-tertiary: #e9ecef;
                --cosmic-text-primary: #333333;
                --cosmic-text-secondary: #666666;
                --cosmic-border: rgba(0,0,0,0.1);
                --cosmic-shadow: rgba(0,0,0,0.1);
            }
            
            .gradio-container {
                background: linear-gradient(135deg, var(--cosmic-bg-primary) 0%, var(--cosmic-bg-secondary) 50%, var(--cosmic-bg-tertiary) 100%);
                color: var(--cosmic-text-primary);
                font-family: 'Segoe UI', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
                min-height: 100vh;
            }
            
            .cosmic-header {
                background: linear-gradient(90deg, var(--cosmic-primary), var(--cosmic-secondary), var(--cosmic-accent));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                text-align: center;
                font-size: calc(2rem * var(--golden-ratio));
                font-weight: 800;
                margin: calc(1rem * var(--golden-ratio)) 0;
                letter-spacing: 0.05em;
            }
            
            .cosmic-card {
                background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.9) 100%);
                border: 1px solid var(--cosmic-border);
                border-radius: calc(15px * var(--golden-ratio));
                padding: calc(1.5rem * var(--golden-ratio));
                margin: calc(1rem * var(--golden-ratio)) 0;
                box-shadow: 0 4px 16px var(--cosmic-shadow);
                transition: all 0.3s ease;
            }
            
            .cosmic-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            }
            """,
            "colors": {
                "primary": "#1f77b4",
                "secondary": "#ff7f0e", 
                "accent": "#2ca02c",
                "success": "#17a2b8",
                "warning": "#ffc107",
                "error": "#dc3545"
            }
        }
    
    def get_hvac_professional_theme(self) -> Dict[str, Any]:
        """🔧 HVAC Professional Theme"""
        return {
            "name": "🔧 HVAC Professional",
            "css": """
            /* 🔧 HVAC Professional Theme */
            :root {
                --hvac-blue: #1e3a8a;
                --hvac-orange: #ea580c;
                --hvac-green: #059669;
                --hvac-red: #dc2626;
                --hvac-gray: #374151;
                --hvac-light: #f3f4f6;
            }
            
            .gradio-container {
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                color: var(--hvac-gray);
                font-family: 'Inter', 'Segoe UI', sans-serif;
            }
            
            .cosmic-header {
                background: linear-gradient(90deg, var(--hvac-blue), var(--hvac-orange));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                text-align: center;
                font-size: 2.5rem;
                font-weight: 700;
                margin: 2rem 0;
            }
            """,
            "colors": {
                "primary": "#1e3a8a",
                "secondary": "#ea580c",
                "accent": "#059669",
                "success": "#10b981",
                "warning": "#f59e0b",
                "error": "#ef4444"
            }
        }
    
    def get_fulmark_brand_theme(self) -> Dict[str, Any]:
        """🏢 Fulmark Brand Theme"""
        return {
            "name": "🏢 Fulmark Brand",
            "css": """
            /* 🏢 Fulmark Brand Theme */
            :root {
                --fulmark-primary: #0066cc;
                --fulmark-secondary: #ff6600;
                --fulmark-accent: #00cc66;
                --fulmark-dark: #003366;
                --fulmark-light: #f0f8ff;
            }
            
            .gradio-container {
                background: linear-gradient(135deg, var(--fulmark-light) 0%, #ffffff 100%);
                color: var(--fulmark-dark);
                font-family: 'Roboto', 'Segoe UI', sans-serif;
            }
            
            .cosmic-header {
                background: linear-gradient(90deg, var(--fulmark-primary), var(--fulmark-secondary));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                text-align: center;
                font-size: 2.8rem;
                font-weight: 800;
                margin: 2rem 0;
            }
            """,
            "colors": {
                "primary": "#0066cc",
                "secondary": "#ff6600",
                "accent": "#00cc66",
                "success": "#28a745",
                "warning": "#ffc107",
                "error": "#dc3545"
            }
        }
    
    def get_theme_css(self, theme_name: str = "cosmic_dark") -> str:
        """🎨 Get CSS for specified theme"""
        return self.themes.get(theme_name, self.themes["cosmic_dark"])["css"]
    
    def get_theme_colors(self, theme_name: str = "cosmic_dark") -> Dict[str, str]:
        """🎨 Get color palette for specified theme"""
        return self.themes.get(theme_name, self.themes["cosmic_dark"])["colors"]
    
    def create_theme_selector(self) -> gr.Dropdown:
        """🎨 Create theme selector dropdown"""
        theme_choices = [theme["name"] for theme in self.themes.values()]
        
        return gr.Dropdown(
            choices=theme_choices,
            value="🌌 Cosmic Dark",
            label="🎨 Interface Theme",
            interactive=True,
            info="Select your preferred cosmic theme"
        )
    
    def apply_golden_ratio_spacing(self, base_size: float = 1.0) -> Dict[str, str]:
        """📐 Apply golden ratio spacing"""
        return {
            "xs": f"{base_size / (self.golden_ratio ** 2)}rem",
            "sm": f"{base_size / self.golden_ratio}rem", 
            "md": f"{base_size}rem",
            "lg": f"{base_size * self.golden_ratio}rem",
            "xl": f"{base_size * (self.golden_ratio ** 2)}rem",
            "xxl": f"{base_size * (self.golden_ratio ** 3)}rem"
        }
    
    def create_cosmic_animations(self) -> str:
        """✨ Create cosmic animations CSS"""
        return """
        @keyframes cosmic-float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        @keyframes cosmic-glow {
            0%, 100% { box-shadow: 0 0 20px rgba(0,212,255,0.3); }
            50% { box-shadow: 0 0 40px rgba(0,212,255,0.6); }
        }
        
        @keyframes cosmic-rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        @keyframes cosmic-scale {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .cosmic-float { animation: cosmic-float 3s ease-in-out infinite; }
        .cosmic-glow { animation: cosmic-glow 2s ease-in-out infinite; }
        .cosmic-rotate { animation: cosmic-rotate 10s linear infinite; }
        .cosmic-scale { animation: cosmic-scale 2s ease-in-out infinite; }
        """
