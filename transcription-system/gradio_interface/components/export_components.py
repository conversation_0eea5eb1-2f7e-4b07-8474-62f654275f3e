#!/usr/bin/env python3
"""
📤 Export Components for Gradio Interface
PDF, CSV, JSON export functionality with advanced formatting
"""

import gradio as gr
import json
import pandas as pd
from datetime import datetime
import io
import base64
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
from reportlab.lib.units import inch

class ExportComponents:
    """📤 Export functionality components"""
    
    def __init__(self):
        self.export_history = []
        
    def create_export_interface(self):
        """📤 Create export interface"""
        
        gr.HTML('<div class="cosmic-card"><h2>📤 Export & Reports</h2></div>')
        
        with gr.Tabs():
            
            # Quick Export Tab
            with gr.Tab("⚡ Quick Export"):
                self.create_quick_export_tab()
            
            # Custom Reports Tab
            with gr.Tab("📊 Custom Reports"):
                self.create_custom_reports_tab()
            
            # Scheduled Reports Tab
            with gr.Tab("⏰ Scheduled Reports"):
                self.create_scheduled_reports_tab()
            
            # Export History Tab
            with gr.Tab("📜 Export History"):
                self.create_export_history_tab()
    
    def create_quick_export_tab(self):
        """⚡ Create quick export tab"""
        
        gr.HTML('<h3>⚡ Quick Export Options</h3>')
        
        with gr.Row():
            with gr.Column():
                # Export type selection
                export_type = gr.Radio(
                    choices=["📄 System Report", "📊 Performance Data", "🎤 Transcription History", "🔧 HVAC Database"],
                    value="📄 System Report",
                    label="📋 Export Type",
                    interactive=True
                )
                
                # Format selection
                export_format = gr.Radio(
                    choices=["📄 PDF", "📊 CSV", "🔧 JSON", "📈 Excel"],
                    value="📄 PDF",
                    label="📁 Format",
                    interactive=True
                )
                
                # Time range
                time_range = gr.Dropdown(
                    choices=["Last Hour", "Last 24 Hours", "Last 7 Days", "Last 30 Days", "All Time"],
                    value="Last 24 Hours",
                    label="📅 Time Range",
                    interactive=True
                )
                
                # Include options
                include_charts = gr.Checkbox(
                    value=True,
                    label="📈 Include Charts",
                    interactive=True
                )
                
                include_raw_data = gr.Checkbox(
                    value=False,
                    label="📊 Include Raw Data",
                    interactive=True
                )
                
                include_metadata = gr.Checkbox(
                    value=True,
                    label="ℹ️ Include Metadata",
                    interactive=True
                )
            
            with gr.Column():
                # Preview area
                gr.HTML('<h4>👁️ Export Preview</h4>')
                
                export_preview = gr.HTML(
                    value="<p>Select export options to see preview</p>"
                )
                
                # Export info
                export_info = gr.JSON(
                    label="📋 Export Information",
                    value={
                        "estimated_size": "N/A",
                        "estimated_time": "N/A",
                        "records_count": "N/A",
                        "format": "N/A"
                    }
                )
        
        # Export buttons
        gr.HTML('<h3>🚀 Generate Export</h3>')
        
        with gr.Row():
            generate_btn = gr.Button("🚀 Generate Export", variant="primary", size="lg")
            preview_btn = gr.Button("👁️ Preview", variant="secondary")
            schedule_btn = gr.Button("⏰ Schedule", variant="secondary")
        
        # Download area
        download_area = gr.File(
            label="📥 Download Generated File",
            visible=False
        )
        
        export_status = gr.HTML(
            value="<p>Ready to generate export</p>"
        )
        
        # Event handlers
        export_type.change(
            fn=self.update_export_preview,
            inputs=[export_type, export_format, time_range],
            outputs=[export_preview, export_info]
        )
        
        generate_btn.click(
            fn=self.generate_export,
            inputs=[export_type, export_format, time_range, include_charts, include_raw_data, include_metadata],
            outputs=[download_area, export_status]
        )
        
        preview_btn.click(
            fn=self.preview_export,
            inputs=[export_type, export_format],
            outputs=[export_preview]
        )
    
    def create_custom_reports_tab(self):
        """📊 Create custom reports tab"""
        
        gr.HTML('<h3>📊 Custom Report Builder</h3>')
        
        with gr.Row():
            with gr.Column(scale=1):
                # Report configuration
                gr.HTML('<h4>⚙️ Report Configuration</h4>')
                
                report_name = gr.Textbox(
                    label="📝 Report Name",
                    placeholder="Enter report name...",
                    interactive=True
                )
                
                report_description = gr.Textbox(
                    label="📄 Description",
                    placeholder="Describe the report purpose...",
                    lines=3,
                    interactive=True
                )
                
                # Data sources
                gr.HTML('<h4>📊 Data Sources</h4>')
                
                data_sources = gr.CheckboxGroup(
                    choices=[
                        "🎤 Transcription Data",
                        "📊 Performance Metrics", 
                        "🏥 System Health",
                        "🚨 Alert History",
                        "🔧 HVAC Equipment",
                        "👥 User Activity"
                    ],
                    value=["📊 Performance Metrics"],
                    label="Select Data Sources",
                    interactive=True
                )
                
                # Filters
                gr.HTML('<h4>🔽 Filters</h4>')
                
                date_from = gr.Textbox(
                    label="📅 From Date",
                    placeholder="YYYY-MM-DD",
                    interactive=True
                )
                
                date_to = gr.Textbox(
                    label="📅 To Date", 
                    placeholder="YYYY-MM-DD",
                    interactive=True
                )
                
                filter_criteria = gr.Textbox(
                    label="🔍 Additional Filters",
                    placeholder="e.g., confidence > 0.8",
                    interactive=True
                )
            
            with gr.Column(scale=2):
                # Report layout
                gr.HTML('<h4>📋 Report Layout</h4>')
                
                # Section selection
                report_sections = gr.CheckboxGroup(
                    choices=[
                        "📊 Executive Summary",
                        "📈 Performance Charts",
                        "📋 Data Tables",
                        "🎯 Key Metrics",
                        "🚨 Alerts Summary",
                        "💡 Recommendations",
                        "📄 Detailed Logs"
                    ],
                    value=["📊 Executive Summary", "📈 Performance Charts", "🎯 Key Metrics"],
                    label="Report Sections",
                    interactive=True
                )
                
                # Chart types
                chart_types = gr.CheckboxGroup(
                    choices=[
                        "📊 Bar Charts",
                        "📈 Line Charts", 
                        "🥧 Pie Charts",
                        "📊 Histograms",
                        "🎯 Gauges",
                        "🗺️ Heatmaps"
                    ],
                    value=["📊 Bar Charts", "📈 Line Charts"],
                    label="Chart Types",
                    interactive=True
                )
                
                # Styling options
                gr.HTML('<h4>🎨 Styling Options</h4>')
                
                with gr.Row():
                    color_scheme = gr.Dropdown(
                        choices=["Cosmic Dark", "Cosmic Light", "Professional", "Colorful"],
                        value="Cosmic Dark",
                        label="🎨 Color Scheme",
                        interactive=True
                    )
                    
                    page_orientation = gr.Radio(
                        choices=["Portrait", "Landscape"],
                        value="Portrait",
                        label="📄 Page Orientation",
                        interactive=True
                    )
        
        # Report actions
        with gr.Row():
            build_report_btn = gr.Button("🔨 Build Report", variant="primary")
            save_template_btn = gr.Button("💾 Save Template", variant="secondary")
            load_template_btn = gr.Button("📂 Load Template", variant="secondary")
        
        # Report preview
        report_preview = gr.HTML(
            value="<p>Configure report settings and click 'Build Report' to generate preview</p>"
        )
        
        # Event handlers
        build_report_btn.click(
            fn=self.build_custom_report,
            inputs=[
                report_name, report_description, data_sources, report_sections,
                chart_types, color_scheme, date_from, date_to
            ],
            outputs=[report_preview]
        )
    
    def create_scheduled_reports_tab(self):
        """⏰ Create scheduled reports tab"""
        
        gr.HTML('<h3>⏰ Scheduled Reports</h3>')
        
        with gr.Row():
            with gr.Column():
                # Schedule configuration
                gr.HTML('<h4>📅 Schedule Configuration</h4>')
                
                schedule_name = gr.Textbox(
                    label="📝 Schedule Name",
                    placeholder="e.g., Daily Performance Report",
                    interactive=True
                )
                
                report_template = gr.Dropdown(
                    choices=["System Performance", "Transcription Summary", "HVAC Equipment Status", "Custom Template"],
                    label="📊 Report Template",
                    interactive=True
                )
                
                frequency = gr.Radio(
                    choices=["📅 Daily", "📅 Weekly", "📅 Monthly", "📅 Custom"],
                    value="📅 Daily",
                    label="🔄 Frequency",
                    interactive=True
                )
                
                time_of_day = gr.Dropdown(
                    choices=["06:00", "09:00", "12:00", "15:00", "18:00", "21:00"],
                    value="09:00",
                    label="🕘 Time of Day",
                    interactive=True
                )
                
                recipients = gr.Textbox(
                    label="📧 Email Recipients",
                    placeholder="<EMAIL>, <EMAIL>",
                    interactive=True
                )
                
                # Advanced options
                gr.HTML('<h4>⚙️ Advanced Options</h4>')
                
                auto_cleanup = gr.Checkbox(
                    value=True,
                    label="🗑️ Auto-cleanup old reports",
                    interactive=True
                )
                
                retention_days = gr.Slider(
                    minimum=7,
                    maximum=365,
                    value=30,
                    label="📅 Retention Period (days)",
                    interactive=True
                )
            
            with gr.Column():
                # Active schedules
                gr.HTML('<h4>📋 Active Schedules</h4>')
                
                active_schedules = gr.Dataframe(
                    headers=["Name", "Template", "Frequency", "Next Run", "Status"],
                    datatype=["str", "str", "str", "str", "str"],
                    value=self.get_scheduled_reports(),
                    label="Scheduled Reports",
                    interactive=True
                )
                
                # Schedule actions
                with gr.Row():
                    create_schedule_btn = gr.Button("➕ Create Schedule", variant="primary")
                    edit_schedule_btn = gr.Button("✏️ Edit", variant="secondary")
                    delete_schedule_btn = gr.Button("🗑️ Delete", variant="secondary")
                    run_now_btn = gr.Button("▶️ Run Now", variant="secondary")
        
        # Event handlers
        create_schedule_btn.click(
            fn=self.create_schedule,
            inputs=[schedule_name, report_template, frequency, time_of_day, recipients],
            outputs=[active_schedules]
        )
    
    def create_export_history_tab(self):
        """📜 Create export history tab"""
        
        gr.HTML('<h3>📜 Export History</h3>')
        
        # Search and filters
        with gr.Row():
            search_exports = gr.Textbox(
                label="🔍 Search Exports",
                placeholder="Search by name, type, or date...",
                scale=2,
                interactive=True
            )
            
            filter_type = gr.Dropdown(
                choices=["All Types", "PDF", "CSV", "JSON", "Excel"],
                value="All Types",
                label="📁 Filter by Type",
                scale=1,
                interactive=True
            )
            
            filter_date = gr.Dropdown(
                choices=["All Time", "Today", "This Week", "This Month"],
                value="All Time",
                label="📅 Filter by Date",
                scale=1,
                interactive=True
            )
        
        # Export history table
        export_history_table = gr.Dataframe(
            headers=["Timestamp", "Name", "Type", "Format", "Size", "Status", "Actions"],
            datatype=["str", "str", "str", "str", "str", "str", "str"],
            value=self.get_export_history(),
            label="Export History",
            interactive=True
        )
        
        # Bulk actions
        with gr.Row():
            download_selected_btn = gr.Button("📥 Download Selected", variant="primary")
            delete_selected_btn = gr.Button("🗑️ Delete Selected", variant="secondary")
            cleanup_old_btn = gr.Button("🧹 Cleanup Old", variant="secondary")
        
        # Storage statistics
        gr.HTML('<h3>📊 Storage Statistics</h3>')
        
        with gr.Row():
            total_exports = gr.HTML(
                value='<div class="metric-display"><h4>📊 Total Exports</h4><h2>156</h2></div>'
            )
            
            storage_used = gr.HTML(
                value='<div class="metric-display"><h4>💾 Storage Used</h4><h2>2.3 GB</h2></div>'
            )
            
            avg_size = gr.HTML(
                value='<div class="metric-display"><h4>📏 Avg Size</h4><h2>15.2 MB</h2></div>'
            )
        
        # Event handlers
        search_exports.change(
            fn=self.filter_export_history,
            inputs=[search_exports, filter_type, filter_date],
            outputs=[export_history_table]
        )
    
    def update_export_preview(self, export_type, export_format, time_range):
        """👁️ Update export preview"""
        
        preview_html = f"""
        <div class="cosmic-card">
            <h4>👁️ Export Preview</h4>
            <p><strong>Type:</strong> {export_type}</p>
            <p><strong>Format:</strong> {export_format}</p>
            <p><strong>Time Range:</strong> {time_range}</p>
            <p><strong>Estimated Records:</strong> 1,234</p>
        </div>
        """
        
        export_info = {
            "estimated_size": "2.5 MB",
            "estimated_time": "15 seconds",
            "records_count": "1,234",
            "format": export_format.split()[1] if " " in export_format else export_format
        }
        
        return preview_html, export_info
    
    def generate_export(self, export_type, export_format, time_range, include_charts, include_raw_data, include_metadata):
        """🚀 Generate export file"""
        
        try:
            # Simulate export generation
            if "PDF" in export_format:
                file_content = self.generate_pdf_report(export_type, time_range, include_charts)
                filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            elif "CSV" in export_format:
                file_content = self.generate_csv_export(export_type, time_range)
                filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            elif "JSON" in export_format:
                file_content = self.generate_json_export(export_type, time_range, include_metadata)
                filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            else:
                file_content = "Sample export content"
                filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            
            # Save to temporary file
            temp_file = f"/tmp/{filename}"
            with open(temp_file, 'w' if isinstance(file_content, str) else 'wb') as f:
                f.write(file_content)
            
            # Add to history
            self.export_history.append({
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "name": filename,
                "type": export_type,
                "format": export_format,
                "size": "2.5 MB",
                "status": "✅ Completed"
            })
            
            status_html = f'<div class="status-healthy">✅ Export generated successfully: {filename}</div>'
            
            return temp_file, status_html
            
        except Exception as e:
            error_html = f'<div class="status-error">❌ Export failed: {str(e)}</div>'
            return None, error_html
    
    def generate_pdf_report(self, export_type, time_range, include_charts):
        """📄 Generate PDF report"""
        
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            textColor=colors.HexColor('#1f77b4')
        )
        
        title = Paragraph(f"🎆 Python_Mixer HVAC CRM Report", title_style)
        story.append(title)
        story.append(Spacer(1, 12))
        
        # Report info
        info_data = [
            ["Report Type:", export_type],
            ["Time Range:", time_range],
            ["Generated:", datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
            ["System:", "NVIDIA STT Polish HVAC Pipeline"]
        ]
        
        info_table = Table(info_data, colWidths=[2*inch, 4*inch])
        info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#f8f9fa')),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(info_table)
        story.append(Spacer(1, 20))
        
        # Content sections
        story.append(Paragraph("📊 Executive Summary", styles['Heading2']))
        story.append(Paragraph("System performance metrics and key indicators for the selected time period.", styles['Normal']))
        story.append(Spacer(1, 12))
        
        # Sample data table
        data = [
            ["Metric", "Current", "Average", "Target", "Status"],
            ["Word Accuracy", "94.2%", "92.8%", "95%", "🟡 Near Target"],
            ["Processing Time", "2.1s", "2.3s", "<3s", "✅ Good"],
            ["System Uptime", "99.9%", "99.7%", "99%", "✅ Excellent"],
            ["Error Rate", "0.1%", "0.2%", "<1%", "✅ Good"]
        ]
        
        table = Table(data, colWidths=[1.5*inch, 1*inch, 1*inch, 1*inch, 1.5*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#667eea')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()
    
    def generate_csv_export(self, export_type, time_range):
        """📊 Generate CSV export"""
        
        # Sample data
        data = {
            "Timestamp": [datetime.now().strftime("%Y-%m-%d %H:%M:%S")] * 5,
            "Metric": ["Word Accuracy", "Processing Time", "Confidence", "HVAC Keywords", "System Health"],
            "Value": [94.2, 2.1, 0.89, 15, 87.5],
            "Unit": ["%", "seconds", "score", "count", "score"],
            "Status": ["Good", "Good", "Good", "Good", "Good"]
        }
        
        df = pd.DataFrame(data)
        return df.to_csv(index=False)
    
    def generate_json_export(self, export_type, time_range, include_metadata):
        """🔧 Generate JSON export"""
        
        export_data = {
            "export_info": {
                "type": export_type,
                "time_range": time_range,
                "generated_at": datetime.now().isoformat(),
                "version": "2.0.0"
            },
            "data": {
                "metrics": [
                    {"name": "Word Accuracy", "value": 94.2, "unit": "%"},
                    {"name": "Processing Time", "value": 2.1, "unit": "seconds"},
                    {"name": "Confidence", "value": 0.89, "unit": "score"}
                ],
                "services": [
                    {"name": "NVIDIA STT", "status": "healthy", "uptime": "99.9%"},
                    {"name": "Python_Mixer", "status": "healthy", "uptime": "99.8%"}
                ]
            }
        }
        
        if include_metadata:
            export_data["metadata"] = {
                "system": "Python_Mixer HVAC CRM",
                "version": "2.0.0",
                "environment": "production"
            }
        
        return json.dumps(export_data, indent=2, ensure_ascii=False)
    
    def preview_export(self, export_type, export_format):
        """👁️ Preview export content"""
        
        preview_html = f"""
        <div class="cosmic-card">
            <h4>👁️ Export Preview - {export_type}</h4>
            <p><strong>Format:</strong> {export_format}</p>
            <hr>
            <h5>📊 Sample Content:</h5>
            <table style="width: 100%; border-collapse: collapse;">
                <tr style="background-color: #667eea; color: white;">
                    <th style="padding: 8px; border: 1px solid #ddd;">Metric</th>
                    <th style="padding: 8px; border: 1px solid #ddd;">Value</th>
                    <th style="padding: 8px; border: 1px solid #ddd;">Status</th>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd;">Word Accuracy</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">94.2%</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">✅ Good</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd;">Processing Time</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">2.1s</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">✅ Good</td>
                </tr>
            </table>
        </div>
        """
        
        return preview_html
    
    def build_custom_report(self, name, description, data_sources, sections, chart_types, color_scheme, date_from, date_to):
        """🔨 Build custom report"""
        
        report_html = f"""
        <div class="cosmic-card">
            <h3>📊 Custom Report: {name}</h3>
            <p><strong>Description:</strong> {description}</p>
            <p><strong>Data Sources:</strong> {', '.join(data_sources)}</p>
            <p><strong>Sections:</strong> {', '.join(sections)}</p>
            <p><strong>Date Range:</strong> {date_from} to {date_to}</p>
            <p><strong>Color Scheme:</strong> {color_scheme}</p>
            <hr>
            <p>✅ Report configuration saved. Click 'Generate Export' to create the report.</p>
        </div>
        """
        
        return report_html
    
    def get_scheduled_reports(self):
        """📅 Get scheduled reports data"""
        
        return [
            ["Daily Performance", "System Performance", "Daily", "2024-01-16 09:00", "✅ Active"],
            ["Weekly Summary", "Transcription Summary", "Weekly", "2024-01-21 09:00", "✅ Active"],
            ["Monthly Report", "HVAC Equipment Status", "Monthly", "2024-02-01 09:00", "✅ Active"]
        ]
    
    def create_schedule(self, name, template, frequency, time, recipients):
        """➕ Create new schedule"""
        
        # Add new schedule to the list
        new_schedule = [name, template, frequency, f"2024-01-16 {time}", "✅ Active"]
        current_schedules = self.get_scheduled_reports()
        current_schedules.append(new_schedule)
        
        return current_schedules
    
    def get_export_history(self):
        """📜 Get export history data"""
        
        return [
            ["2024-01-15 14:30", "System Report", "📄 System Report", "PDF", "2.5 MB", "✅ Completed", "📥 Download"],
            ["2024-01-15 13:45", "Performance Data", "📊 Performance Data", "CSV", "1.2 MB", "✅ Completed", "📥 Download"],
            ["2024-01-15 12:20", "HVAC Database", "🔧 HVAC Database", "JSON", "3.1 MB", "✅ Completed", "📥 Download"],
            ["2024-01-15 11:15", "Transcription History", "🎤 Transcription History", "Excel", "4.2 MB", "✅ Completed", "📥 Download"],
            ["2024-01-15 10:30", "Custom Report", "📊 Custom Report", "PDF", "1.8 MB", "✅ Completed", "📥 Download"]
        ]
    
    def filter_export_history(self, search_term, type_filter, date_filter):
        """🔍 Filter export history"""
        
        # Simple filtering logic
        history = self.get_export_history()
        
        if search_term:
            history = [row for row in history if search_term.lower() in row[1].lower()]
        
        if type_filter != "All Types":
            history = [row for row in history if type_filter in row[3]]
        
        return history
