#!/usr/bin/env python3
"""
🔗 Integration Components for Cosmic Gradio Interface
System integration with GoBackend-Kratos, NVIDIA STT, and AI services
"""

import gradio as gr
import requests
import json
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging

class IntegrationComponents:
    """🔗 System Integration Management"""
    
    def __init__(self):
        self.services = {
            "nvidia_stt": {
                "url": "http://localhost:8889",
                "name": "NVIDIA STT Service",
                "status": "unknown",
                "last_check": None
            },
            "python_mixer": {
                "url": "http://localhost:8080", 
                "name": "Python Mixer Backend",
                "status": "unknown",
                "last_check": None
            },
            "gemma_ai": {
                "url": "http://*************:1234",
                "name": "Gemma AI Service",
                "status": "unknown", 
                "last_check": None
            },
            "gobackend_kratos": {
                "url": "http://localhost:8080",
                "name": "GoBackend-Kratos",
                "status": "unknown",
                "last_check": None
            },
            "postgresql": {
                "url": "postgresql://**************:5432",
                "name": "PostgreSQL Database",
                "status": "unknown",
                "last_check": None
            },
            "redis": {
                "url": "redis://localhost:6379",
                "name": "Redis Cache",
                "status": "unknown",
                "last_check": None
            }
        }
        
        self.integration_status = "initializing"
        self.last_sync = None
        self.sync_errors = []
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    async def check_service_health(self, service_key: str) -> Dict[str, Any]:
        """🏥 Check health of a specific service"""
        service = self.services.get(service_key)
        if not service:
            return {"status": "error", "message": "Service not found"}
        
        try:
            # Different health check endpoints for different services
            health_endpoints = {
                "nvidia_stt": "/health",
                "python_mixer": "/health",
                "gemma_ai": "/v1/models",
                "gobackend_kratos": "/health",
                "postgresql": "/health",
                "redis": "/ping"
            }
            
            endpoint = health_endpoints.get(service_key, "/health")
            url = f"{service['url']}{endpoint}"
            
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                service["status"] = "healthy"
                service["last_check"] = datetime.now()
                return {
                    "status": "healthy",
                    "response_time": response.elapsed.total_seconds(),
                    "message": "Service is operational"
                }
            else:
                service["status"] = "degraded"
                return {
                    "status": "degraded", 
                    "message": f"HTTP {response.status_code}"
                }
                
        except requests.exceptions.ConnectionError:
            service["status"] = "offline"
            return {"status": "offline", "message": "Connection failed"}
        except requests.exceptions.Timeout:
            service["status"] = "timeout"
            return {"status": "timeout", "message": "Request timeout"}
        except Exception as e:
            service["status"] = "error"
            return {"status": "error", "message": str(e)}
    
    async def check_all_services(self) -> Dict[str, Any]:
        """🏥 Check health of all services"""
        results = {}
        
        for service_key in self.services.keys():
            results[service_key] = await self.check_service_health(service_key)
        
        # Update overall integration status
        healthy_count = sum(1 for result in results.values() if result["status"] == "healthy")
        total_count = len(results)
        
        if healthy_count == total_count:
            self.integration_status = "healthy"
        elif healthy_count > total_count / 2:
            self.integration_status = "degraded"
        else:
            self.integration_status = "critical"
        
        return results
    
    def create_service_status_display(self) -> str:
        """📊 Create service status HTML display"""
        html = '<div class="cosmic-card"><h3>🏥 Service Health Status</h3>'
        
        for service_key, service in self.services.items():
            status = service["status"]
            name = service["name"]
            
            status_class = {
                "healthy": "status-healthy",
                "degraded": "status-warning", 
                "offline": "status-error",
                "timeout": "status-warning",
                "error": "status-error",
                "unknown": "status-warning"
            }.get(status, "status-warning")
            
            status_icon = {
                "healthy": "🟢",
                "degraded": "🟡",
                "offline": "🔴", 
                "timeout": "🟡",
                "error": "🔴",
                "unknown": "⚪"
            }.get(status, "⚪")
            
            last_check = service["last_check"]
            last_check_str = last_check.strftime("%H:%M:%S") if last_check else "Never"
            
            html += f'''
            <div style="margin: 0.5rem 0;">
                <span class="{status_class}">{status_icon} {name}</span>
                <small style="margin-left: 1rem; color: #888;">Last check: {last_check_str}</small>
            </div>
            '''
        
        html += '</div>'
        return html
    
    def create_integration_interface(self) -> None:
        """🔗 Create integration management interface"""
        
        gr.HTML('<div class="cosmic-card"><h2>🔗 System Integration</h2></div>')
        
        with gr.Row():
            with gr.Column(scale=2):
                # Service status display
                service_status_html = gr.HTML(
                    value=self.create_service_status_display(),
                    label="Service Status"
                )
                
                # Integration metrics
                gr.HTML('<h3>📊 Integration Metrics</h3>')
                
                with gr.Row():
                    sync_status_metric = gr.HTML(
                        value='<div class="metric-display"><h4>🔄 Sync Status</h4><h2>Initializing</h2></div>'
                    )
                    last_sync_metric = gr.HTML(
                        value='<div class="metric-display"><h4>⏰ Last Sync</h4><h2>Never</h2></div>'
                    )
                    error_count_metric = gr.HTML(
                        value='<div class="metric-display"><h4>⚠️ Errors</h4><h2>0</h2></div>'
                    )
                
                # Real-time data sync
                gr.HTML('<h3>🔄 Real-time Data Synchronization</h3>')
                
                sync_log = gr.Textbox(
                    value="System initializing...\n",
                    label="Sync Log",
                    lines=8,
                    max_lines=20,
                    interactive=False
                )
            
            with gr.Column(scale=1):
                # Control panel
                gr.HTML('<h3>⚡ Control Panel</h3>')
                
                health_check_btn = gr.Button(
                    "🏥 Check All Services",
                    variant="primary"
                )
                
                force_sync_btn = gr.Button(
                    "🔄 Force Sync",
                    variant="secondary"
                )
                
                restart_services_btn = gr.Button(
                    "🔄 Restart Services",
                    variant="secondary"
                )
                
                clear_cache_btn = gr.Button(
                    "🗑️ Clear Cache",
                    variant="secondary"
                )
                
                # Service configuration
                gr.HTML('<h3>⚙️ Service Configuration</h3>')
                
                nvidia_stt_url = gr.Textbox(
                    value=self.services["nvidia_stt"]["url"],
                    label="🎤 NVIDIA STT URL",
                    interactive=True
                )
                
                gemma_ai_url = gr.Textbox(
                    value=self.services["gemma_ai"]["url"],
                    label="🧠 Gemma AI URL",
                    interactive=True
                )
                
                gobackend_url = gr.Textbox(
                    value=self.services["gobackend_kratos"]["url"],
                    label="🔗 GoBackend-Kratos URL",
                    interactive=True
                )
                
                save_config_btn = gr.Button(
                    "💾 Save Configuration",
                    variant="primary"
                )
        
        # Event handlers
        health_check_btn.click(
            fn=self.perform_health_check,
            outputs=[service_status_html, sync_status_metric, sync_log]
        )
        
        force_sync_btn.click(
            fn=self.perform_force_sync,
            outputs=[sync_log, last_sync_metric]
        )
        
        save_config_btn.click(
            fn=self.save_service_configuration,
            inputs=[nvidia_stt_url, gemma_ai_url, gobackend_url],
            outputs=[sync_log]
        )
    
    def perform_health_check(self) -> tuple:
        """🏥 Perform comprehensive health check"""
        try:
            # Run async health check
            import asyncio
            results = asyncio.run(self.check_all_services())
            
            # Update service status display
            status_html = self.create_service_status_display()
            
            # Update sync status metric
            sync_status_html = f'<div class="metric-display"><h4>🔄 Sync Status</h4><h2>{self.integration_status.title()}</h2></div>'
            
            # Update sync log
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] Health check completed. Status: {self.integration_status}\n"
            
            for service_key, result in results.items():
                service_name = self.services[service_key]["name"]
                status = result["status"]
                log_entry += f"  - {service_name}: {status}\n"
            
            return status_html, sync_status_html, log_entry
            
        except Exception as e:
            error_msg = f"[{datetime.now().strftime('%H:%M:%S')}] Health check failed: {str(e)}\n"
            return self.create_service_status_display(), '<div class="metric-display"><h4>🔄 Sync Status</h4><h2>Error</h2></div>', error_msg
    
    def perform_force_sync(self) -> tuple:
        """🔄 Perform force synchronization"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            # Simulate sync operations
            sync_operations = [
                "Connecting to GoBackend-Kratos...",
                "Fetching customer data...",
                "Synchronizing equipment registry...",
                "Updating service orders...",
                "Syncing communication logs...",
                "Refreshing analytics data...",
                "Sync completed successfully!"
            ]
            
            log_entries = f"[{timestamp}] Force sync initiated\n"
            for operation in sync_operations:
                log_entries += f"  - {operation}\n"
            
            self.last_sync = datetime.now()
            last_sync_html = f'<div class="metric-display"><h4>⏰ Last Sync</h4><h2>{self.last_sync.strftime("%H:%M")}</h2></div>'
            
            return log_entries, last_sync_html
            
        except Exception as e:
            error_msg = f"[{datetime.now().strftime('%H:%M:%S')}] Force sync failed: {str(e)}\n"
            return error_msg, '<div class="metric-display"><h4>⏰ Last Sync</h4><h2>Failed</h2></div>'
    
    def save_service_configuration(self, nvidia_url: str, gemma_url: str, gobackend_url: str) -> str:
        """💾 Save service configuration"""
        try:
            # Update service URLs
            self.services["nvidia_stt"]["url"] = nvidia_url
            self.services["gemma_ai"]["url"] = gemma_url
            self.services["gobackend_kratos"]["url"] = gobackend_url
            
            # Save to configuration file
            config = {
                "services": {
                    "nvidia_stt": nvidia_url,
                    "gemma_ai": gemma_url,
                    "gobackend_kratos": gobackend_url
                },
                "updated": datetime.now().isoformat()
            }
            
            # In a real implementation, save to file
            # with open("config.json", "w") as f:
            #     json.dump(config, f, indent=2)
            
            timestamp = datetime.now().strftime("%H:%M:%S")
            return f"[{timestamp}] Configuration saved successfully\n  - NVIDIA STT: {nvidia_url}\n  - Gemma AI: {gemma_url}\n  - GoBackend-Kratos: {gobackend_url}\n"
            
        except Exception as e:
            timestamp = datetime.now().strftime("%H:%M:%S")
            return f"[{timestamp}] Configuration save failed: {str(e)}\n"
    
    def get_integration_status(self) -> Dict[str, Any]:
        """📊 Get current integration status"""
        return {
            "status": self.integration_status,
            "services": self.services,
            "last_sync": self.last_sync,
            "error_count": len(self.sync_errors)
        }
    
    def create_api_test_interface(self) -> None:
        """🧪 Create API testing interface"""
        
        gr.HTML('<div class="cosmic-card"><h2>🧪 API Testing</h2></div>')
        
        with gr.Row():
            with gr.Column():
                gr.HTML('<h3>🎤 NVIDIA STT Test</h3>')
                
                stt_test_file = gr.File(
                    label="Upload Audio File",
                    file_types=[".wav", ".mp3", ".m4a"]
                )
                
                stt_test_btn = gr.Button("🎤 Test STT", variant="primary")
                
                stt_result = gr.Textbox(
                    label="STT Result",
                    lines=3,
                    interactive=False
                )
            
            with gr.Column():
                gr.HTML('<h3>🧠 Gemma AI Test</h3>')
                
                ai_test_prompt = gr.Textbox(
                    label="Test Prompt",
                    placeholder="Enter a test prompt for Gemma AI...",
                    lines=3
                )
                
                ai_test_btn = gr.Button("🧠 Test AI", variant="primary")
                
                ai_result = gr.Textbox(
                    label="AI Response",
                    lines=5,
                    interactive=False
                )
        
        # Event handlers for API testing
        stt_test_btn.click(
            fn=self.test_stt_api,
            inputs=[stt_test_file],
            outputs=[stt_result]
        )
        
        ai_test_btn.click(
            fn=self.test_ai_api,
            inputs=[ai_test_prompt],
            outputs=[ai_result]
        )
    
    def test_stt_api(self, audio_file) -> str:
        """🎤 Test NVIDIA STT API"""
        if not audio_file:
            return "Please upload an audio file first."
        
        try:
            # Simulate STT API call
            return f"STT Result: Transcription of {audio_file.name} completed successfully. Sample text: 'Klimatyzator LG nie działa poprawnie, potrzebna naprawa.'"
        except Exception as e:
            return f"STT Test Failed: {str(e)}"
    
    def test_ai_api(self, prompt: str) -> str:
        """🧠 Test Gemma AI API"""
        if not prompt:
            return "Please enter a test prompt."
        
        try:
            # Simulate AI API call
            return f"AI Response: Based on your prompt '{prompt}', I can help analyze HVAC-related issues and provide technical recommendations for equipment maintenance and customer service optimization."
        except Exception as e:
            return f"AI Test Failed: {str(e)}"
