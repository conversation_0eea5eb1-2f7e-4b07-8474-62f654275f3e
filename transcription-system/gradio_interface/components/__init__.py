# 🎆 Gradio Interface Components
# Modular components for Python_Mixer HVAC CRM interface

from .audio_components import AudioComponents
from .hvac_components import HVACComponents  
from .monitoring_components import MonitoringComponents
from .export_components import ExportComponents

# Placeholder imports for remaining components
class ThemeComponents:
    """🎨 Theme management components"""
    pass

class LanguageComponents:
    """🌍 Language management components"""
    pass

class IntegrationComponents:
    """🔗 Integration management components"""
    pass

__all__ = [
    'AudioComponents',
    'HVACComponents', 
    'MonitoringComponents',
    'ExportComponents',
    'ThemeComponents',
    'LanguageComponents',
    'IntegrationComponents'
]
