#!/usr/bin/env python3
"""
🎤 Audio Components for Gradio Interface
Advanced audio processing, waveform visualization, and transcription features
"""

import gradio as gr
import numpy as np
import librosa
import soundfile as sf
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime
import tempfile
import os
import json
import aiohttp
import asyncio

class AudioComponents:
    """🎤 Audio processing components for Gradio interface"""
    
    def __init__(self):
        self.transcription_history = []
        self.supported_formats = ['.wav', '.mp3', '.m4a', '.flac', '.ogg']
        self.stt_service_url = "http://localhost:8889"
        
        # HVAC keywords for enhanced processing
        self.hvac_keywords = [
            "klimatyzacja", "klimatyzator", "pompa ciepła", "wentylacja",
            "chłodzenie", "ogrzewanie", "serwis", "naprawa", "instalacja",
            "LG", "Daikin", "Samsung", "Mitsubishi", "Fujitsu",
            "split", "multi split", "VRF", "VRV", "kompresor", "freon"
        ]
    
    def create_audio_interface(self):
        """🎤 Create complete audio processing interface"""
        
        gr.HTML('<div class="cosmic-card"><h2>🎤 Audio Processing & Transcription</h2></div>')
        
        with gr.Row():
            with gr.Column(scale=2):
                # Audio upload section
                gr.HTML('<h3>📁 Audio Upload</h3>')
                
                audio_input = gr.Audio(
                    label="🎤 Upload Audio File",
                    type="filepath",
                    format="wav"
                )
                
                # Upload options
                with gr.Row():
                    language_select = gr.Dropdown(
                        choices=["🇵🇱 Polish", "🇺🇸 English"],
                        value="🇵🇱 Polish",
                        label="🌍 Language",
                        scale=1
                    )
                    
                    hvac_context = gr.Checkbox(
                        value=True,
                        label="🔧 HVAC Context",
                        scale=1
                    )
                    
                    confidence_threshold = gr.Slider(
                        minimum=0.5,
                        maximum=1.0,
                        value=0.8,
                        step=0.05,
                        label="🎯 Confidence Threshold",
                        scale=1
                    )
                
                # Transcription button
                transcribe_btn = gr.Button(
                    "🎤 Start Transcription",
                    variant="primary",
                    size="lg"
                )
                
                # Waveform visualization
                gr.HTML('<h3>🌊 Audio Waveform</h3>')
                waveform_plot = gr.Plot(
                    label="Audio Waveform Visualization",
                    value=self.create_sample_waveform()
                )
            
            with gr.Column(scale=1):
                # Real-time status
                gr.HTML('<h3>📊 Processing Status</h3>')
                
                status_display = gr.HTML(
                    value='<div class="status-healthy">🟢 Ready for Upload</div>'
                )
                
                # Audio info
                audio_info = gr.JSON(
                    label="📋 Audio Information",
                    value={
                        "duration": "0.0s",
                        "sample_rate": "N/A",
                        "channels": "N/A",
                        "format": "N/A",
                        "size": "N/A"
                    }
                )
                
                # Processing metrics
                processing_metrics = gr.JSON(
                    label="⚡ Processing Metrics",
                    value={
                        "processing_time": "N/A",
                        "confidence": "N/A",
                        "hvac_keywords_found": 0,
                        "model_used": "N/A"
                    }
                )
        
        # Transcription results section
        gr.HTML('<h3>📝 Transcription Results</h3>')
        
        with gr.Row():
            with gr.Column(scale=2):
                transcription_output = gr.Textbox(
                    label="📝 Transcribed Text",
                    placeholder="Transcription will appear here...",
                    lines=5,
                    max_lines=10,
                    interactive=False
                )
                
                # HVAC keywords found
                hvac_keywords_output = gr.Textbox(
                    label="🔧 HVAC Keywords Detected",
                    placeholder="Detected HVAC terms will appear here...",
                    lines=2,
                    interactive=False
                )
            
            with gr.Column(scale=1):
                # Confidence visualization
                confidence_gauge = gr.Plot(
                    label="🎯 Confidence Score",
                    value=self.create_confidence_gauge(0.0)
                )
                
                # Quick actions
                gr.HTML('<h4>⚡ Quick Actions</h4>')
                
                save_transcription_btn = gr.Button(
                    "💾 Save Transcription",
                    variant="secondary"
                )
                
                export_audio_btn = gr.Button(
                    "📤 Export Audio Data",
                    variant="secondary"
                )
                
                clear_results_btn = gr.Button(
                    "🗑️ Clear Results",
                    variant="secondary"
                )
        
        # Transcription history
        gr.HTML('<h3>📜 Transcription History</h3>')
        
        with gr.Row():
            history_search = gr.Textbox(
                label="🔍 Search History",
                placeholder="Search transcriptions...",
                scale=3
            )
            
            history_filter = gr.Dropdown(
                choices=["All", "High Confidence", "HVAC Related", "Recent"],
                value="All",
                label="🔽 Filter",
                scale=1
            )
        
        history_display = gr.Dataframe(
            headers=["Timestamp", "Filename", "Transcript", "Confidence", "HVAC Keywords"],
            datatype=["str", "str", "str", "number", "str"],
            label="📊 History Data",
            interactive=False
        )
        
        # Event handlers
        audio_input.change(
            fn=self.analyze_audio_file,
            inputs=[audio_input],
            outputs=[audio_info, waveform_plot, status_display]
        )
        
        transcribe_btn.click(
            fn=self.transcribe_audio,
            inputs=[audio_input, language_select, hvac_context, confidence_threshold],
            outputs=[
                transcription_output, 
                hvac_keywords_output, 
                confidence_gauge, 
                processing_metrics,
                status_display,
                history_display
            ]
        )
        
        save_transcription_btn.click(
            fn=self.save_transcription,
            inputs=[transcription_output, hvac_keywords_output],
            outputs=[status_display]
        )
        
        clear_results_btn.click(
            fn=self.clear_results,
            outputs=[
                transcription_output,
                hvac_keywords_output,
                confidence_gauge,
                processing_metrics,
                status_display
            ]
        )
        
        history_search.change(
            fn=self.filter_history,
            inputs=[history_search, history_filter],
            outputs=[history_display]
        )
    
    def create_sample_waveform(self):
        """🌊 Create sample waveform visualization"""
        
        # Generate sample audio data
        t = np.linspace(0, 2, 32000)  # 2 seconds at 16kHz
        audio_data = np.sin(2 * np.pi * 440 * t) * np.exp(-t)  # Decaying sine wave
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=t,
            y=audio_data,
            mode='lines',
            name='Waveform',
            line=dict(color='#00d4ff', width=1),
            fill='tonexty',
            fillcolor='rgba(0, 212, 255, 0.3)'
        ))
        
        fig.update_layout(
            title="🌊 Audio Waveform",
            xaxis_title="Time (seconds)",
            yaxis_title="Amplitude",
            height=300,
            showlegend=False,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white')
        )
        
        return fig
    
    def create_confidence_gauge(self, confidence_value):
        """🎯 Create confidence gauge visualization"""
        
        fig = go.Figure(go.Indicator(
            mode="gauge+number+delta",
            value=confidence_value * 100,
            domain={'x': [0, 1], 'y': [0, 1]},
            title={'text': "Confidence %"},
            delta={'reference': 80},
            gauge={
                'axis': {'range': [None, 100]},
                'bar': {'color': "#00d4ff"},
                'steps': [
                    {'range': [0, 50], 'color': "lightgray"},
                    {'range': [50, 80], 'color': "yellow"},
                    {'range': [80, 95], 'color': "orange"},
                    {'range': [95, 100], 'color': "green"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ))
        
        fig.update_layout(
            height=250,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white')
        )
        
        return fig
    
    def analyze_audio_file(self, audio_file_path):
        """📊 Analyze uploaded audio file"""
        
        if not audio_file_path:
            return {}, self.create_sample_waveform(), '<div class="status-warning">⚠️ No file uploaded</div>'
        
        try:
            # Load audio file
            audio_data, sample_rate = librosa.load(audio_file_path, sr=None)
            duration = len(audio_data) / sample_rate
            
            # Get file info
            file_size = os.path.getsize(audio_file_path)
            file_format = os.path.splitext(audio_file_path)[1]
            
            audio_info = {
                "duration": f"{duration:.2f}s",
                "sample_rate": f"{sample_rate} Hz",
                "channels": "1 (Mono)" if len(audio_data.shape) == 1 else f"{audio_data.shape[1]} (Stereo)",
                "format": file_format.upper(),
                "size": f"{file_size / 1024:.1f} KB"
            }
            
            # Create waveform visualization
            time_axis = np.linspace(0, duration, len(audio_data))
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=time_axis,
                y=audio_data,
                mode='lines',
                name='Waveform',
                line=dict(color='#00d4ff', width=1),
                fill='tonexty',
                fillcolor='rgba(0, 212, 255, 0.3)'
            ))
            
            fig.update_layout(
                title=f"🌊 Audio Waveform - {os.path.basename(audio_file_path)}",
                xaxis_title="Time (seconds)",
                yaxis_title="Amplitude",
                height=300,
                showlegend=False,
                plot_bgcolor='rgba(0,0,0,0)',
                paper_bgcolor='rgba(0,0,0,0)',
                font=dict(color='white')
            )
            
            status = '<div class="status-healthy">✅ Audio file analyzed successfully</div>'
            
            return audio_info, fig, status
            
        except Exception as e:
            error_info = {
                "error": str(e),
                "duration": "N/A",
                "sample_rate": "N/A",
                "channels": "N/A",
                "format": "N/A",
                "size": "N/A"
            }
            
            status = f'<div class="status-error">❌ Error analyzing audio: {str(e)}</div>'
            
            return error_info, self.create_sample_waveform(), status
    
    def transcribe_audio(self, audio_file_path, language, hvac_context, confidence_threshold):
        """🎤 Transcribe audio using NVIDIA STT service"""
        
        if not audio_file_path:
            return "", "", self.create_confidence_gauge(0.0), {}, '<div class="status-warning">⚠️ No audio file provided</div>', []
        
        try:
            # Mock transcription for demo (replace with actual STT service call)
            import time
            time.sleep(2)  # Simulate processing time
            
            # Sample transcription results
            sample_transcripts = [
                "Dzień dobry, mam problem z klimatyzacją LG. Urządzenie nie chłodzi prawidłowo i wydaje dziwne dźwięki.",
                "Potrzebuję serwisu pompy ciepła Daikin. System nie grzeje i pokazuje kod błędu E7.",
                "Klimatyzator Samsung przestał działać po burzy. Pilot nie reaguje i dioda miga na czerwono.",
                "Instalacja nowego systemu VRF w biurowcu. Kiedy możemy umówić termin?",
                "Wymiana filtrów w klimatyzacji. Jak często należy to robić?"
            ]
            
            # Select random transcript for demo
            import random
            transcript = random.choice(sample_transcripts)
            confidence = random.uniform(0.85, 0.98)
            
            # Find HVAC keywords
            found_keywords = []
            transcript_lower = transcript.lower()
            for keyword in self.hvac_keywords:
                if keyword.lower() in transcript_lower:
                    found_keywords.append(keyword)
            
            # Processing metrics
            processing_metrics = {
                "processing_time": f"{random.uniform(1.5, 3.5):.1f}s",
                "confidence": f"{confidence:.3f}",
                "hvac_keywords_found": len(found_keywords),
                "model_used": "NVIDIA NeMo Polish"
            }
            
            # Add to history
            history_entry = {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "filename": os.path.basename(audio_file_path),
                "transcript": transcript,
                "confidence": confidence,
                "hvac_keywords": ", ".join(found_keywords)
            }
            
            self.transcription_history.append(history_entry)
            
            # Create confidence gauge
            confidence_gauge = self.create_confidence_gauge(confidence)
            
            # Status update
            if confidence >= confidence_threshold:
                status = f'<div class="status-healthy">✅ Transcription completed (Confidence: {confidence:.1%})</div>'
            else:
                status = f'<div class="status-warning">⚠️ Low confidence transcription ({confidence:.1%})</div>'
            
            # Convert history to display format
            history_display = [[entry["timestamp"], entry["filename"], entry["transcript"][:50] + "...", 
                              entry["confidence"], entry["hvac_keywords"]] for entry in self.transcription_history[-10:]]
            
            return transcript, ", ".join(found_keywords), confidence_gauge, processing_metrics, status, history_display
            
        except Exception as e:
            error_status = f'<div class="status-error">❌ Transcription failed: {str(e)}</div>'
            return "", "", self.create_confidence_gauge(0.0), {}, error_status, []
    
    def save_transcription(self, transcript, keywords):
        """💾 Save transcription to file"""
        
        if not transcript:
            return '<div class="status-warning">⚠️ No transcription to save</div>'
        
        try:
            # Create transcriptions directory
            os.makedirs("transcriptions", exist_ok=True)
            
            # Save transcription
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"transcriptions/transcription_{timestamp}.json"
            
            data = {
                "timestamp": datetime.now().isoformat(),
                "transcript": transcript,
                "hvac_keywords": keywords,
                "saved_by": "gradio_interface"
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            return f'<div class="status-healthy">✅ Transcription saved to {filename}</div>'
            
        except Exception as e:
            return f'<div class="status-error">❌ Save failed: {str(e)}</div>'
    
    def clear_results(self):
        """🗑️ Clear all results"""
        
        return "", "", self.create_confidence_gauge(0.0), {}, '<div class="status-healthy">🗑️ Results cleared</div>'
    
    def filter_history(self, search_term, filter_type):
        """🔍 Filter transcription history"""
        
        filtered_history = self.transcription_history
        
        # Apply search filter
        if search_term:
            filtered_history = [
                entry for entry in filtered_history
                if search_term.lower() in entry["transcript"].lower() or 
                   search_term.lower() in entry["filename"].lower()
            ]
        
        # Apply type filter
        if filter_type == "High Confidence":
            filtered_history = [entry for entry in filtered_history if entry["confidence"] > 0.9]
        elif filter_type == "HVAC Related":
            filtered_history = [entry for entry in filtered_history if entry["hvac_keywords"]]
        elif filter_type == "Recent":
            filtered_history = filtered_history[-5:]  # Last 5 entries
        
        # Convert to display format
        return [[entry["timestamp"], entry["filename"], entry["transcript"][:50] + "...", 
                entry["confidence"], entry["hvac_keywords"]] for entry in filtered_history]
