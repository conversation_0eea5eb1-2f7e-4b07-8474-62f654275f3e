#!/usr/bin/env python3
"""
🌍 Language Components for Cosmic Gradio Interface
Multi-language support with HVAC terminology for Polish and English
"""

import gradio as gr
import json
from typing import Dict, Any, List
from pathlib import Path

class LanguageComponents:
    """🌍 Multi-language Support with HVAC Terminology"""
    
    def __init__(self):
        self.current_language = "en"
        self.supported_languages = ["en", "pl"]
        self.translations = self.load_translations()
        self.hvac_terminology = self.load_hvac_terminology()
    
    def load_translations(self) -> Dict[str, Dict[str, str]]:
        """📚 Load translation dictionaries"""
        return {
            "en": {
                # Interface Elements
                "dashboard": "Dashboard",
                "audio_processing": "Audio Processing",
                "hvac_glossary": "HVAC Glossary",
                "system_monitoring": "System Monitoring",
                "export_reports": "Export & Reports",
                "settings": "Settings",
                "customer_intelligence": "Customer Intelligence",
                "equipment_registry": "Equipment Registry",
                "financial_dashboard": "Financial Dashboard",
                "communication_hub": "Communication Hub",
                "service_pipeline": "Service Pipeline",
                "predictive_analytics": "Predictive Analytics",
                
                # Actions
                "upload_audio": "Upload Audio File",
                "start_recording": "Start Recording",
                "stop_recording": "Stop Recording",
                "process_audio": "Process Audio",
                "export_data": "Export Data",
                "refresh_data": "Refresh Data",
                "health_check": "Health Check",
                "save_settings": "Save Settings",
                "generate_report": "Generate Report",
                "analyze_customer": "Analyze Customer",
                "schedule_maintenance": "Schedule Maintenance",
                
                # Status Messages
                "system_online": "System Online",
                "system_offline": "System Offline",
                "processing": "Processing...",
                "completed": "Completed",
                "error_occurred": "Error Occurred",
                "success": "Success",
                "warning": "Warning",
                "healthy": "Healthy",
                "degraded": "Degraded",
                "failed": "Failed",
                
                # Metrics
                "accuracy": "Accuracy",
                "processing_time": "Processing Time",
                "confidence": "Confidence",
                "uptime": "Uptime",
                "response_time": "Response Time",
                "error_rate": "Error Rate",
                "throughput": "Throughput",
                "cpu_usage": "CPU Usage",
                "memory_usage": "Memory Usage",
                "disk_usage": "Disk Usage",
                
                # HVAC Specific
                "air_conditioning": "Air Conditioning",
                "heating": "Heating",
                "ventilation": "Ventilation",
                "installation": "Installation",
                "maintenance": "Maintenance",
                "repair": "Repair",
                "service_call": "Service Call",
                "equipment_health": "Equipment Health",
                "energy_efficiency": "Energy Efficiency",
                "temperature_control": "Temperature Control",
                
                # Customer Management
                "customer_profile": "Customer Profile",
                "service_history": "Service History",
                "equipment_list": "Equipment List",
                "maintenance_schedule": "Maintenance Schedule",
                "invoice_history": "Invoice History",
                "communication_log": "Communication Log",
                "satisfaction_score": "Satisfaction Score",
                "loyalty_status": "Loyalty Status",
                
                # Reports
                "daily_report": "Daily Report",
                "weekly_report": "Weekly Report",
                "monthly_report": "Monthly Report",
                "annual_report": "Annual Report",
                "performance_report": "Performance Report",
                "financial_report": "Financial Report",
                "maintenance_report": "Maintenance Report",
                "customer_report": "Customer Report"
            },
            "pl": {
                # Interface Elements
                "dashboard": "Panel Główny",
                "audio_processing": "Przetwarzanie Audio",
                "hvac_glossary": "Słownik HVAC",
                "system_monitoring": "Monitoring Systemu",
                "export_reports": "Eksport i Raporty",
                "settings": "Ustawienia",
                "customer_intelligence": "Analiza Klientów",
                "equipment_registry": "Rejestr Urządzeń",
                "financial_dashboard": "Panel Finansowy",
                "communication_hub": "Centrum Komunikacji",
                "service_pipeline": "Proces Serwisowy",
                "predictive_analytics": "Analiza Predykcyjna",
                
                # Actions
                "upload_audio": "Prześlij Plik Audio",
                "start_recording": "Rozpocznij Nagrywanie",
                "stop_recording": "Zatrzymaj Nagrywanie",
                "process_audio": "Przetwórz Audio",
                "export_data": "Eksportuj Dane",
                "refresh_data": "Odśwież Dane",
                "health_check": "Sprawdzenie Stanu",
                "save_settings": "Zapisz Ustawienia",
                "generate_report": "Generuj Raport",
                "analyze_customer": "Analizuj Klienta",
                "schedule_maintenance": "Zaplanuj Konserwację",
                
                # Status Messages
                "system_online": "System Online",
                "system_offline": "System Offline",
                "processing": "Przetwarzanie...",
                "completed": "Zakończone",
                "error_occurred": "Wystąpił Błąd",
                "success": "Sukces",
                "warning": "Ostrzeżenie",
                "healthy": "Sprawny",
                "degraded": "Pogorszony",
                "failed": "Nieudany",
                
                # Metrics
                "accuracy": "Dokładność",
                "processing_time": "Czas Przetwarzania",
                "confidence": "Pewność",
                "uptime": "Czas Działania",
                "response_time": "Czas Odpowiedzi",
                "error_rate": "Wskaźnik Błędów",
                "throughput": "Przepustowość",
                "cpu_usage": "Użycie CPU",
                "memory_usage": "Użycie Pamięci",
                "disk_usage": "Użycie Dysku",
                
                # HVAC Specific
                "air_conditioning": "Klimatyzacja",
                "heating": "Ogrzewanie",
                "ventilation": "Wentylacja",
                "installation": "Instalacja",
                "maintenance": "Konserwacja",
                "repair": "Naprawa",
                "service_call": "Wezwanie Serwisowe",
                "equipment_health": "Stan Urządzeń",
                "energy_efficiency": "Efektywność Energetyczna",
                "temperature_control": "Kontrola Temperatury",
                
                # Customer Management
                "customer_profile": "Profil Klienta",
                "service_history": "Historia Serwisu",
                "equipment_list": "Lista Urządzeń",
                "maintenance_schedule": "Harmonogram Konserwacji",
                "invoice_history": "Historia Faktur",
                "communication_log": "Log Komunikacji",
                "satisfaction_score": "Ocena Satysfakcji",
                "loyalty_status": "Status Lojalności",
                
                # Reports
                "daily_report": "Raport Dzienny",
                "weekly_report": "Raport Tygodniowy",
                "monthly_report": "Raport Miesięczny",
                "annual_report": "Raport Roczny",
                "performance_report": "Raport Wydajności",
                "financial_report": "Raport Finansowy",
                "maintenance_report": "Raport Konserwacji",
                "customer_report": "Raport Klientów"
            }
        }
    
    def load_hvac_terminology(self) -> Dict[str, Dict[str, Any]]:
        """🔧 Load HVAC terminology database"""
        return {
            "equipment": {
                "en": {
                    "air_conditioner": "Air Conditioner",
                    "heat_pump": "Heat Pump",
                    "furnace": "Furnace",
                    "boiler": "Boiler",
                    "ductwork": "Ductwork",
                    "thermostat": "Thermostat",
                    "compressor": "Compressor",
                    "evaporator": "Evaporator",
                    "condenser": "Condenser",
                    "filter": "Filter",
                    "fan": "Fan",
                    "coil": "Coil",
                    "refrigerant": "Refrigerant",
                    "valve": "Valve",
                    "sensor": "Sensor"
                },
                "pl": {
                    "air_conditioner": "Klimatyzator",
                    "heat_pump": "Pompa Ciepła",
                    "furnace": "Piec",
                    "boiler": "Kocioł",
                    "ductwork": "Kanały Wentylacyjne",
                    "thermostat": "Termostat",
                    "compressor": "Sprężarka",
                    "evaporator": "Parownik",
                    "condenser": "Skraplacz",
                    "filter": "Filtr",
                    "fan": "Wentylator",
                    "coil": "Wężownica",
                    "refrigerant": "Czynnik Chłodniczy",
                    "valve": "Zawór",
                    "sensor": "Czujnik"
                }
            },
            "brands": {
                "en": ["LG", "Daikin", "Samsung", "Mitsubishi", "Panasonic", "Carrier", "Trane", "Lennox"],
                "pl": ["LG", "Daikin", "Samsung", "Mitsubishi", "Panasonic", "Carrier", "Trane", "Lennox"]
            },
            "problems": {
                "en": {
                    "not_cooling": "Not Cooling",
                    "not_heating": "Not Heating",
                    "strange_noise": "Strange Noise",
                    "water_leak": "Water Leak",
                    "high_bills": "High Energy Bills",
                    "poor_airflow": "Poor Airflow",
                    "frozen_coils": "Frozen Coils",
                    "thermostat_issues": "Thermostat Issues",
                    "refrigerant_leak": "Refrigerant Leak",
                    "electrical_problems": "Electrical Problems"
                },
                "pl": {
                    "not_cooling": "Nie Chłodzi",
                    "not_heating": "Nie Grzeje",
                    "strange_noise": "Dziwne Dźwięki",
                    "water_leak": "Wyciek Wody",
                    "high_bills": "Wysokie Rachunki",
                    "poor_airflow": "Słaby Przepływ Powietrza",
                    "frozen_coils": "Zamrożone Wężownice",
                    "thermostat_issues": "Problemy z Termostatem",
                    "refrigerant_leak": "Wyciek Czynnika",
                    "electrical_problems": "Problemy Elektryczne"
                }
            },
            "services": {
                "en": {
                    "installation": "Installation",
                    "maintenance": "Maintenance",
                    "repair": "Repair",
                    "inspection": "Inspection",
                    "cleaning": "Cleaning",
                    "tune_up": "Tune-up",
                    "replacement": "Replacement",
                    "upgrade": "Upgrade",
                    "emergency": "Emergency Service",
                    "warranty": "Warranty Service"
                },
                "pl": {
                    "installation": "Instalacja",
                    "maintenance": "Konserwacja",
                    "repair": "Naprawa",
                    "inspection": "Przegląd",
                    "cleaning": "Czyszczenie",
                    "tune_up": "Regulacja",
                    "replacement": "Wymiana",
                    "upgrade": "Modernizacja",
                    "emergency": "Serwis Awaryjny",
                    "warranty": "Serwis Gwarancyjny"
                }
            }
        }
    
    def get_text(self, key: str, language: str = None) -> str:
        """📝 Get translated text"""
        lang = language or self.current_language
        return self.translations.get(lang, {}).get(key, key)
    
    def get_hvac_term(self, category: str, term: str, language: str = None) -> str:
        """🔧 Get HVAC terminology"""
        lang = language or self.current_language
        return self.hvac_terminology.get(category, {}).get(lang, {}).get(term, term)
    
    def get_hvac_brands(self, language: str = None) -> List[str]:
        """🏢 Get HVAC brands list"""
        lang = language or self.current_language
        return self.hvac_terminology.get("brands", {}).get(lang, [])
    
    def create_language_selector(self) -> gr.Dropdown:
        """🌍 Create language selector dropdown"""
        language_choices = [
            "🇺🇸 English",
            "🇵🇱 Polski"
        ]
        
        return gr.Dropdown(
            choices=language_choices,
            value="🇺🇸 English",
            label="🌍 Language / Język",
            interactive=True,
            info="Select your preferred language / Wybierz preferowany język"
        )
    
    def set_language(self, language_selection: str) -> str:
        """🌍 Set current language"""
        if "English" in language_selection:
            self.current_language = "en"
        elif "Polski" in language_selection:
            self.current_language = "pl"
        
        return f"Language set to: {language_selection}"
    
    def get_interface_labels(self, language: str = None) -> Dict[str, str]:
        """🏷️ Get all interface labels for current language"""
        lang = language or self.current_language
        return self.translations.get(lang, self.translations["en"])
    
    def create_hvac_glossary_interface(self) -> None:
        """📚 Create HVAC glossary interface"""
        labels = self.get_interface_labels()
        
        with gr.Row():
            with gr.Column(scale=1):
                gr.HTML(f'<h3>🔧 {labels["equipment_list"]}</h3>')
                
                equipment_terms = self.hvac_terminology["equipment"][self.current_language]
                equipment_html = ""
                for key, value in equipment_terms.items():
                    equipment_html += f'<div class="glossary-term"><strong>{value}</strong><br><small>{key}</small></div>'
                
                gr.HTML(equipment_html)
            
            with gr.Column(scale=1):
                gr.HTML(f'<h3>🏢 {labels.get("brands", "Brands")}</h3>')
                
                brands = self.get_hvac_brands()
                brands_html = ""
                for brand in brands:
                    brands_html += f'<div class="glossary-term">{brand}</div>'
                
                gr.HTML(brands_html)
            
            with gr.Column(scale=1):
                gr.HTML(f'<h3>⚠️ {labels.get("problems", "Common Problems")}</h3>')
                
                problems = self.hvac_terminology["problems"][self.current_language]
                problems_html = ""
                for key, value in problems.items():
                    problems_html += f'<div class="glossary-term"><strong>{value}</strong><br><small>{key}</small></div>'
                
                gr.HTML(problems_html)
    
    def translate_status(self, status: str, language: str = None) -> str:
        """📊 Translate status messages"""
        lang = language or self.current_language
        status_map = {
            "en": {
                "online": "🟢 Online",
                "offline": "🔴 Offline", 
                "warning": "🟡 Warning",
                "error": "🔴 Error",
                "healthy": "🟢 Healthy",
                "degraded": "🟡 Degraded",
                "failed": "🔴 Failed"
            },
            "pl": {
                "online": "🟢 Online",
                "offline": "🔴 Offline",
                "warning": "🟡 Ostrzeżenie", 
                "error": "🔴 Błąd",
                "healthy": "🟢 Sprawny",
                "degraded": "🟡 Pogorszony",
                "failed": "🔴 Nieudany"
            }
        }
        
        return status_map.get(lang, status_map["en"]).get(status.lower(), status)
    
    def get_localized_datetime_format(self, language: str = None) -> str:
        """📅 Get localized datetime format"""
        lang = language or self.current_language
        formats = {
            "en": "%Y-%m-%d %H:%M:%S",
            "pl": "%d.%m.%Y %H:%M:%S"
        }
        return formats.get(lang, formats["en"])
    
    def create_language_interface(self) -> None:
        """🌍 Create language management interface"""
        with gr.Row():
            with gr.Column():
                gr.HTML('<h3>🌍 Language Settings</h3>')
                
                language_selector = self.create_language_selector()
                
                current_lang_display = gr.HTML(
                    value=f"Current Language: {self.current_language.upper()}"
                )
                
                # Language test area
                gr.HTML('<h4>🧪 Language Test</h4>')
                
                test_terms = gr.HTML(
                    value=self.get_test_translation_html()
                )
                
                language_selector.change(
                    fn=self.update_language_display,
                    inputs=[language_selector],
                    outputs=[current_lang_display, test_terms]
                )
    
    def get_test_translation_html(self) -> str:
        """🧪 Get test translation HTML"""
        labels = self.get_interface_labels()
        
        html = f"""
        <div class="cosmic-card">
            <h4>📊 {labels['dashboard']}</h4>
            <p>🎤 {labels['audio_processing']}</p>
            <p>🔧 {labels['hvac_glossary']}</p>
            <p>📊 {labels['system_monitoring']}</p>
            <p>⚙️ {labels['settings']}</p>
        </div>
        """
        return html
    
    def update_language_display(self, language_selection: str) -> tuple:
        """🔄 Update language display"""
        self.set_language(language_selection)
        
        current_lang_html = f"Current Language: {self.current_language.upper()}"
        test_html = self.get_test_translation_html()
        
        return current_lang_html, test_html
