#!/usr/bin/env python3
"""
🎆 Main Gradio Interface for Python_Mixer HVAC CRM
Cosmic-level interface with advanced features and modular architecture
"""

import gradio as gr
import os
import json
import asyncio
from datetime import datetime
from pathlib import Path

# Import modular components
from components.audio_components import AudioComponents
from components.hvac_components import HVACComponents
from components.monitoring_components import MonitoringComponents
from components.export_components import ExportComponents
from components.theme_components import ThemeComponents
from components.language_components import LanguageComponents
from components.integration_components import IntegrationComponents

# Import utilities
from utils.audio_utils import AudioUtils
from utils.export_utils import ExportUtils
from utils.integration_utils import IntegrationUtils

class CosmicGradioInterface:
    """🎆 Cosmic Gradio Interface for Python_Mixer"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.config = self.load_config()
        
        # Initialize components
        self.audio_components = AudioComponents()
        self.hvac_components = HVACComponents()
        self.monitoring_components = MonitoringComponents()
        self.export_components = ExportComponents()
        self.theme_components = ThemeComponents()
        self.language_components = LanguageComponents()
        self.integration_components = IntegrationComponents()
        
        # Initialize utilities
        self.audio_utils = AudioUtils()
        self.export_utils = ExportUtils()
        self.integration_utils = IntegrationUtils()
        
        # State management
        self.current_theme = "cosmic_dark"
        self.current_language = "en"
        self.system_status = "initializing"
        
    def load_config(self):
        """📋 Load configuration"""
        config_path = self.base_dir / "config.json"
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        # Default configuration
        return {
            "services": {
                "nvidia_stt": "http://localhost:8889",
                "python_mixer": "http://localhost:8080",
                "gemma_ai": "http://*************:1234"
            },
            "themes": {
                "default": "cosmic_dark",
                "available": ["cosmic_dark", "cosmic_light"]
            },
            "languages": {
                "default": "en",
                "available": ["en", "pl"]
            },
            "features": {
                "audio_upload": True,
                "real_time_monitoring": True,
                "export_functionality": True,
                "hvac_glossary": True,
                "ai_insights": True
            }
        }
    
    def get_cosmic_css(self, theme="cosmic_dark"):
        """🎨 Get cosmic CSS styling"""
        
        if theme == "cosmic_dark":
            return """
            /* 🌌 Cosmic Dark Theme */
            .gradio-container {
                background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
                color: #ffffff;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            
            .cosmic-header {
                background: linear-gradient(90deg, #00d4ff, #ff6b6b, #4ecdc4);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                text-align: center;
                font-size: 3rem;
                font-weight: bold;
                margin: 2rem 0;
                text-shadow: 0 0 20px rgba(0,212,255,0.5);
            }
            
            .cosmic-card {
                background: linear-gradient(135deg, rgba(102,126,234,0.2) 0%, rgba(118,75,162,0.2) 100%);
                border: 1px solid rgba(255,255,255,0.1);
                border-radius: 15px;
                padding: 1.5rem;
                margin: 1rem 0;
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
            }
            
            .cosmic-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 12px 40px rgba(0,212,255,0.2);
            }
            
            .cosmic-button {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                border-radius: 10px;
                color: white;
                padding: 0.8rem 1.5rem;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 4px 16px rgba(102,126,234,0.3);
            }
            
            .cosmic-button:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(102,126,234,0.4);
            }
            
            .status-healthy {
                background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-weight: bold;
            }
            
            .status-warning {
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-weight: bold;
            }
            
            .status-error {
                background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-weight: bold;
            }
            
            .metric-display {
                background: linear-gradient(135deg, rgba(0,212,255,0.1) 0%, rgba(255,107,107,0.1) 100%);
                border: 1px solid rgba(0,212,255,0.3);
                border-radius: 10px;
                padding: 1rem;
                text-align: center;
                margin: 0.5rem;
            }
            
            .waveform-container {
                background: linear-gradient(135deg, rgba(102,126,234,0.1) 0%, rgba(118,75,162,0.1) 100%);
                border-radius: 10px;
                padding: 1rem;
                margin: 1rem 0;
            }
            
            .upload-zone {
                border: 2px dashed #667eea;
                border-radius: 15px;
                padding: 2rem;
                text-align: center;
                background: linear-gradient(135deg, rgba(102,126,234,0.1) 0%, rgba(118,75,162,0.1) 100%);
                transition: all 0.3s ease;
            }
            
            .upload-zone:hover {
                border-color: #764ba2;
                background: linear-gradient(135deg, rgba(102,126,234,0.2) 0%, rgba(118,75,162,0.2) 100%);
            }
            
            .glossary-term {
                background: linear-gradient(135deg, rgba(17,153,142,0.2) 0%, rgba(56,239,125,0.2) 100%);
                border: 1px solid rgba(17,153,142,0.3);
                border-radius: 8px;
                padding: 1rem;
                margin: 0.5rem 0;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            
            .glossary-term:hover {
                transform: translateX(5px);
                box-shadow: 0 4px 16px rgba(17,153,142,0.3);
            }
            """
        else:  # cosmic_light
            return """
            /* ☀️ Cosmic Light Theme */
            .gradio-container {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
                color: #333333;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            
            .cosmic-header {
                background: linear-gradient(90deg, #1f77b4, #ff7f0e, #2ca02c);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                text-align: center;
                font-size: 3rem;
                font-weight: bold;
                margin: 2rem 0;
            }
            
            .cosmic-card {
                background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,249,250,0.8) 100%);
                border: 1px solid rgba(0,0,0,0.1);
                border-radius: 15px;
                padding: 1.5rem;
                margin: 1rem 0;
                box-shadow: 0 4px 16px rgba(0,0,0,0.1);
                transition: all 0.3s ease;
            }
            
            .cosmic-card:hover {
                transform: translateY(-3px);
                box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            }
            
            .cosmic-button {
                background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                border: none;
                border-radius: 10px;
                color: white;
                padding: 0.8rem 1.5rem;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            """
    
    def create_main_interface(self):
        """🎆 Create main Gradio interface"""
        
        # Load cosmic CSS
        cosmic_css = self.get_cosmic_css(self.current_theme)
        
        with gr.Blocks(
            css=cosmic_css,
            title="🎆 Python_Mixer HVAC CRM - Cosmic Interface",
            theme=gr.themes.Soft()
        ) as interface:
            
            # Header
            gr.HTML("""
            <div class="cosmic-header">
                🎆 Python_Mixer HVAC CRM - Cosmic Interface 2024
            </div>
            """)
            
            # Control panel
            with gr.Row():
                with gr.Column(scale=1):
                    theme_selector = gr.Dropdown(
                        choices=["🌌 Cosmic Dark", "☀️ Cosmic Light"],
                        value="🌌 Cosmic Dark",
                        label="🎨 Theme",
                        interactive=True
                    )
                
                with gr.Column(scale=1):
                    language_selector = gr.Dropdown(
                        choices=["🇺🇸 English", "🇵🇱 Polski"],
                        value="🇺🇸 English",
                        label="🌍 Language",
                        interactive=True
                    )
                
                with gr.Column(scale=1):
                    system_status_display = gr.HTML(
                        value='<span class="status-healthy">🟢 System Online</span>',
                        label="System Status"
                    )
            
            # Main tabs
            with gr.Tabs():
                
                # 📊 Dashboard Tab
                with gr.Tab("📊 Dashboard"):
                    self.create_dashboard_tab()
                
                # 🎤 Audio Processing Tab
                with gr.Tab("🎤 Audio Processing"):
                    self.create_audio_tab()
                
                # 🔧 HVAC Glossary Tab
                with gr.Tab("🔧 HVAC Glossary"):
                    self.create_hvac_tab()
                
                # 📊 Monitoring Tab
                with gr.Tab("📊 System Monitoring"):
                    self.create_monitoring_tab()
                
                # 📤 Export Tab
                with gr.Tab("📤 Export & Reports"):
                    self.create_export_tab()
                
                # ⚙️ Settings Tab
                with gr.Tab("⚙️ Settings"):
                    self.create_settings_tab()
            
            # Footer
            gr.HTML("""
            <div style="text-align: center; margin-top: 2rem; padding: 1rem; 
                        background: linear-gradient(135deg, rgba(102,126,234,0.1) 0%, rgba(118,75,162,0.1) 100%);
                        border-radius: 10px;">
                <p>🎆 <strong>Python_Mixer HVAC CRM</strong> - Cosmic Interface 2024</p>
                <p>🚀 Powered by Gradio, NVIDIA STT, and Gemma AI</p>
                <p>🔧 Built for Fulmark HVAC Solutions</p>
            </div>
            """)
            
            # Event handlers
            theme_selector.change(
                fn=self.change_theme,
                inputs=[theme_selector],
                outputs=[system_status_display]
            )
            
            language_selector.change(
                fn=self.change_language,
                inputs=[language_selector],
                outputs=[system_status_display]
            )
        
        return interface
    
    def create_dashboard_tab(self):
        """📊 Create dashboard tab"""
        
        gr.HTML('<div class="cosmic-card"><h2>📊 System Dashboard</h2></div>')
        
        with gr.Row():
            with gr.Column(scale=2):
                # Real-time metrics
                gr.HTML('<h3>📈 Real-time Metrics</h3>')
                
                with gr.Row():
                    accuracy_metric = gr.HTML(
                        value='<div class="metric-display"><h4>📝 Accuracy</h4><h2>94.2%</h2></div>'
                    )
                    processing_metric = gr.HTML(
                        value='<div class="metric-display"><h4>⚡ Processing</h4><h2>2.1s</h2></div>'
                    )
                    confidence_metric = gr.HTML(
                        value='<div class="metric-display"><h4>🎯 Confidence</h4><h2>0.89</h2></div>'
                    )
                
                # Performance chart placeholder
                performance_chart = gr.Plot(
                    label="📈 Performance Trends",
                    value=self.monitoring_components.create_sample_chart()
                )
            
            with gr.Column(scale=1):
                # Service health
                gr.HTML('<h3>🏥 Service Health</h3>')
                
                service_status = gr.HTML(value="""
                <div class="cosmic-card">
                    <p><span class="status-healthy">🟢 NVIDIA STT</span></p>
                    <p><span class="status-healthy">🟢 Python_Mixer</span></p>
                    <p><span class="status-warning">🟡 Gemma AI</span></p>
                    <p><span class="status-healthy">🟢 Database</span></p>
                </div>
                """)
                
                # Quick actions
                gr.HTML('<h3>⚡ Quick Actions</h3>')
                
                with gr.Column():
                    refresh_btn = gr.Button("🔄 Refresh Data", variant="primary")
                    health_check_btn = gr.Button("🏥 Health Check", variant="secondary")
                    export_btn = gr.Button("📤 Quick Export", variant="secondary")
    
    def create_audio_tab(self):
        """🎤 Create audio processing tab"""
        return self.audio_components.create_audio_interface()
    
    def create_hvac_tab(self):
        """🔧 Create HVAC glossary tab"""
        return self.hvac_components.create_hvac_interface()
    
    def create_monitoring_tab(self):
        """📊 Create monitoring tab"""
        return self.monitoring_components.create_monitoring_interface()
    
    def create_export_tab(self):
        """📤 Create export tab"""
        return self.export_components.create_export_interface()
    
    def create_settings_tab(self):
        """⚙️ Create settings tab"""
        
        gr.HTML('<div class="cosmic-card"><h2>⚙️ System Settings</h2></div>')
        
        with gr.Row():
            with gr.Column():
                gr.HTML('<h3>🔧 Service Configuration</h3>')
                
                nvidia_stt_url = gr.Textbox(
                    value=self.config["services"]["nvidia_stt"],
                    label="🎤 NVIDIA STT URL",
                    interactive=True
                )
                
                python_mixer_url = gr.Textbox(
                    value=self.config["services"]["python_mixer"],
                    label="🔗 Python_Mixer URL",
                    interactive=True
                )
                
                gemma_ai_url = gr.Textbox(
                    value=self.config["services"]["gemma_ai"],
                    label="🧠 Gemma AI URL",
                    interactive=True
                )
            
            with gr.Column():
                gr.HTML('<h3>🎨 Interface Preferences</h3>')
                
                auto_refresh = gr.Checkbox(
                    value=True,
                    label="🔄 Auto Refresh",
                    interactive=True
                )
                
                refresh_interval = gr.Slider(
                    minimum=5,
                    maximum=300,
                    value=30,
                    step=5,
                    label="⏱️ Refresh Interval (seconds)",
                    interactive=True
                )
                
                enable_animations = gr.Checkbox(
                    value=True,
                    label="✨ Enable Animations",
                    interactive=True
                )
        
        # Save settings button
        save_settings_btn = gr.Button("💾 Save Settings", variant="primary")
        
        save_settings_btn.click(
            fn=self.save_settings,
            inputs=[nvidia_stt_url, python_mixer_url, gemma_ai_url, auto_refresh, refresh_interval],
            outputs=[gr.HTML(value="✅ Settings saved successfully!")]
        )
    
    def change_theme(self, theme_selection):
        """🎨 Change interface theme"""
        if "Dark" in theme_selection:
            self.current_theme = "cosmic_dark"
        else:
            self.current_theme = "cosmic_light"
        
        return f'<span class="status-healthy">🎨 Theme: {theme_selection}</span>'
    
    def change_language(self, language_selection):
        """🌍 Change interface language"""
        if "English" in language_selection:
            self.current_language = "en"
        else:
            self.current_language = "pl"
        
        return f'<span class="status-healthy">🌍 Language: {language_selection}</span>'
    
    def save_settings(self, nvidia_url, mixer_url, gemma_url, auto_refresh, refresh_interval):
        """💾 Save settings"""
        
        self.config["services"]["nvidia_stt"] = nvidia_url
        self.config["services"]["python_mixer"] = mixer_url
        self.config["services"]["gemma_ai"] = gemma_url
        
        # Save to file
        config_path = self.base_dir / "config.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
        
        return "✅ Settings saved successfully!"
    
    def launch_interface(self, share=False, debug=False):
        """🚀 Launch the Gradio interface"""
        
        interface = self.create_main_interface()
        
        print("🎆 COSMIC GRADIO INTERFACE LAUNCHING...")
        print("=" * 50)
        print("🎯 Python_Mixer HVAC CRM Interface")
        print("🎨 Cosmic Design with Advanced Features")
        print("🔗 Integrated with NVIDIA STT and Gemma AI")
        print("🌍 Multi-language Support (EN/PL)")
        print("📊 Real-time Monitoring and Analytics")
        print()
        
        interface.launch(
            share=share,
            debug=debug,
            server_name="0.0.0.0",
            server_port=7860
        )

# 🚀 Main execution
if __name__ == "__main__":
    cosmic_interface = CosmicGradioInterface()
    cosmic_interface.launch_interface(share=False, debug=True)
