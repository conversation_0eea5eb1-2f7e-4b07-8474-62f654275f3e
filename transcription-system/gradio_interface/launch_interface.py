#!/usr/bin/env python3
"""
🚀 Launch Script for Cosmic Gradio Interface
Quick launcher with health checks and configuration validation
"""

import sys
import os
import subprocess
import time
from pathlib import Path

# Add the gradio_interface directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_dependencies():
    """📦 Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'gradio',
        'pandas', 
        'numpy',
        'matplotlib',
        'requests',
        'aiohttp'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("📦 Install with: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies satisfied!")
    return True

def check_services():
    """🏥 Check if required services are running"""
    print("\n🏥 Checking service availability...")
    
    services = [
        ("NVIDIA STT", "http://localhost:8889/health"),
        ("Python Mixer", "http://localhost:8080/health"),
        ("Gemma AI", "http://*************:1234/v1/models"),
        ("GoBackend-Kratos", "http://localhost:8080/health")
    ]
    
    try:
        import requests
        
        for service_name, url in services:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    print(f"  🟢 {service_name} - Online")
                else:
                    print(f"  🟡 {service_name} - Responding but status {response.status_code}")
            except requests.exceptions.ConnectionError:
                print(f"  🔴 {service_name} - Offline")
            except requests.exceptions.Timeout:
                print(f"  🟡 {service_name} - Timeout")
            except Exception as e:
                print(f"  ⚪ {service_name} - Error: {str(e)}")
    
    except ImportError:
        print("  ⚠️  Cannot check services - requests not available")
    
    print("ℹ️  Services can be started independently - interface will work with available services")

def create_config_if_missing():
    """⚙️ Create default configuration if missing"""
    config_path = current_dir / "config.json"
    
    if not config_path.exists():
        print("📝 Creating default configuration...")
        
        default_config = {
            "services": {
                "nvidia_stt": "http://localhost:8889",
                "python_mixer": "http://localhost:8080",
                "gemma_ai": "http://*************:1234",
                "gobackend_kratos": "http://localhost:8080"
            },
            "themes": {
                "default": "cosmic_dark",
                "available": ["cosmic_dark", "cosmic_light", "hvac_professional", "fulmark_brand"]
            },
            "languages": {
                "default": "en",
                "available": ["en", "pl"]
            },
            "features": {
                "audio_upload": True,
                "real_time_monitoring": True,
                "export_functionality": True,
                "hvac_glossary": True,
                "ai_insights": True,
                "customer_intelligence": True,
                "equipment_registry": True,
                "financial_dashboard": True,
                "communication_hub": True,
                "service_pipeline": True,
                "predictive_analytics": True
            },
            "interface": {
                "port": 7860,
                "host": "0.0.0.0",
                "share": False,
                "debug": True,
                "auto_refresh": True,
                "refresh_interval": 30
            }
        }
        
        import json
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Configuration created: {config_path}")

def launch_interface():
    """🚀 Launch the Gradio interface"""
    print("\n🎆 LAUNCHING COSMIC GRADIO INTERFACE")
    print("=" * 50)
    
    try:
        from main_gradio_interface import CosmicGradioInterface
        
        print("🎯 Initializing Cosmic Interface...")
        cosmic_interface = CosmicGradioInterface()
        
        print("🎨 Loading cosmic themes and components...")
        print("🌍 Setting up multi-language support...")
        print("🔗 Configuring service integrations...")
        print("📊 Preparing monitoring dashboard...")
        print("🎤 Setting up audio processing...")
        print("📤 Initializing export capabilities...")
        
        print("\n🌟 COSMIC INTERFACE READY!")
        print("🔗 Interface will be available at: http://localhost:7860")
        print("🎨 Features: Cosmic themes, AI integration, HVAC glossary")
        print("🌍 Languages: English, Polski")
        print("📊 Monitoring: Real-time system health")
        print("🎤 Audio: STT processing with waveform visualization")
        print("📤 Export: PDF, Excel, CSV, JSON reports")
        print("\n🚀 Launching interface...")
        print("=" * 50)
        
        # Launch with configuration
        cosmic_interface.launch_interface(
            share=False,
            debug=True
        )
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        print("🔧 Make sure all components are properly installed")
        return False
    except Exception as e:
        print(f"❌ Launch error: {str(e)}")
        print("🔧 Check logs for detailed error information")
        return False
    
    return True

def main():
    """🎯 Main launcher function"""
    print("🎆 PYTHON_MIXER HVAC CRM - COSMIC GRADIO INTERFACE")
    print("🌌 Divine quality interface with cosmic-level UX")
    print("🔧 Built for HVAC excellence and customer success")
    print("=" * 60)
    
    # Pre-flight checks
    if not check_dependencies():
        print("\n❌ Dependency check failed!")
        print("📦 Please install required packages and try again")
        return 1
    
    check_services()
    create_config_if_missing()
    
    # Launch interface
    print("\n🎯 Starting interface launch sequence...")
    
    if launch_interface():
        print("\n✅ Interface launched successfully!")
        return 0
    else:
        print("\n❌ Interface launch failed!")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n🛑 Launch interrupted by user")
        print("👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        print("🔧 Please check the logs and try again")
        sys.exit(1)
