#!/usr/bin/env python3
"""
📤 Export Utilities for Cosmic Gradio Interface
PDF generation, data export, and reporting capabilities
"""

import pandas as pd
import json
import csv
import io
import base64
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
import tempfile
import zipfile

# PDF generation
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.graphics.shapes import Drawing
    from reportlab.graphics.charts.linecharts import HorizontalLineChart
    from reportlab.graphics.charts.barcharts import VerticalBarChart
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

class ExportUtils:
    """📤 Data Export and Report Generation Utilities"""
    
    def __init__(self):
        self.export_formats = ['csv', 'json', 'excel', 'pdf']
        self.report_types = ['daily', 'weekly', 'monthly', 'custom']
        self.temp_dir = Path(tempfile.gettempdir()) / "cosmic_exports"
        self.temp_dir.mkdir(exist_ok=True)
        
    def export_data(self, data: Union[Dict, List], format: str, filename: str = None) -> str:
        """📊 Export data in specified format"""
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"export_{timestamp}"
        
        try:
            if format.lower() == 'csv':
                return self.export_to_csv(data, filename)
            elif format.lower() == 'json':
                return self.export_to_json(data, filename)
            elif format.lower() == 'excel':
                return self.export_to_excel(data, filename)
            elif format.lower() == 'pdf':
                return self.export_to_pdf(data, filename)
            else:
                raise ValueError(f"Unsupported export format: {format}")
                
        except Exception as e:
            raise ValueError(f"Export failed: {str(e)}")
    
    def export_to_csv(self, data: Union[Dict, List], filename: str) -> str:
        """📄 Export data to CSV format"""
        
        output_path = self.temp_dir / f"{filename}.csv"
        
        if isinstance(data, dict):
            # Convert dict to DataFrame
            if all(isinstance(v, (list, tuple)) for v in data.values()):
                df = pd.DataFrame(data)
            else:
                df = pd.DataFrame([data])
        elif isinstance(data, list):
            if data and isinstance(data[0], dict):
                df = pd.DataFrame(data)
            else:
                df = pd.DataFrame({'data': data})
        else:
            df = pd.DataFrame({'data': [data]})
        
        df.to_csv(output_path, index=False, encoding='utf-8')
        return str(output_path)
    
    def export_to_json(self, data: Union[Dict, List], filename: str) -> str:
        """📄 Export data to JSON format"""
        
        output_path = self.temp_dir / f"{filename}.json"
        
        export_data = {
            "exported_at": datetime.now().isoformat(),
            "data": data,
            "metadata": {
                "format": "json",
                "version": "1.0",
                "source": "Python_Mixer HVAC CRM"
            }
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
        
        return str(output_path)
    
    def export_to_excel(self, data: Union[Dict, List], filename: str) -> str:
        """📊 Export data to Excel format"""
        
        output_path = self.temp_dir / f"{filename}.xlsx"
        
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            
            if isinstance(data, dict):
                # Multiple sheets for different data types
                for sheet_name, sheet_data in data.items():
                    if isinstance(sheet_data, (list, tuple)) and sheet_data:
                        if isinstance(sheet_data[0], dict):
                            df = pd.DataFrame(sheet_data)
                        else:
                            df = pd.DataFrame({sheet_name: sheet_data})
                    else:
                        df = pd.DataFrame({sheet_name: [sheet_data]})
                    
                    df.to_excel(writer, sheet_name=str(sheet_name)[:31], index=False)
            
            elif isinstance(data, list):
                if data and isinstance(data[0], dict):
                    df = pd.DataFrame(data)
                else:
                    df = pd.DataFrame({'data': data})
                
                df.to_excel(writer, sheet_name='Data', index=False)
            
            else:
                df = pd.DataFrame({'data': [data]})
                df.to_excel(writer, sheet_name='Data', index=False)
        
        return str(output_path)
    
    def export_to_pdf(self, data: Union[Dict, List], filename: str) -> str:
        """📄 Export data to PDF format"""
        
        if not PDF_AVAILABLE:
            raise ValueError("PDF export requires reportlab package")
        
        output_path = self.temp_dir / f"{filename}.pdf"
        
        # Create PDF document
        doc = SimpleDocTemplate(str(output_path), pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            textColor=colors.HexColor('#667eea'),
            alignment=1  # Center
        )
        
        story.append(Paragraph("🎆 Python_Mixer HVAC CRM Report", title_style))
        story.append(Spacer(1, 20))
        
        # Metadata
        meta_style = styles['Normal']
        story.append(Paragraph(f"<b>Generated:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", meta_style))
        story.append(Paragraph(f"<b>Format:</b> PDF Export", meta_style))
        story.append(Spacer(1, 20))
        
        # Data content
        if isinstance(data, dict):
            for key, value in data.items():
                story.append(Paragraph(f"<b>{key}:</b>", styles['Heading2']))
                
                if isinstance(value, (list, tuple)):
                    if value and isinstance(value[0], dict):
                        # Create table for list of dicts
                        df = pd.DataFrame(value)
                        table_data = [df.columns.tolist()] + df.values.tolist()
                        
                        table = Table(table_data)
                        table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#667eea')),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                            ('FONTSIZE', (0, 0), (-1, 0), 12),
                            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                            ('GRID', (0, 0), (-1, -1), 1, colors.black)
                        ]))
                        
                        story.append(table)
                    else:
                        # Simple list
                        for item in value:
                            story.append(Paragraph(f"• {item}", styles['Normal']))
                else:
                    story.append(Paragraph(str(value), styles['Normal']))
                
                story.append(Spacer(1, 12))
        
        elif isinstance(data, list):
            if data and isinstance(data[0], dict):
                # Create table
                df = pd.DataFrame(data)
                table_data = [df.columns.tolist()] + df.values.tolist()
                
                table = Table(table_data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#667eea')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(table)
            else:
                for item in data:
                    story.append(Paragraph(f"• {item}", styles['Normal']))
        
        else:
            story.append(Paragraph(str(data), styles['Normal']))
        
        # Build PDF
        doc.build(story)
        
        return str(output_path)
    
    def generate_hvac_report(self, report_type: str, data: Dict[str, Any]) -> str:
        """📊 Generate HVAC-specific report"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"hvac_report_{report_type}_{timestamp}"
        
        # Prepare report data
        report_data = {
            "report_info": {
                "type": report_type,
                "generated_at": datetime.now().isoformat(),
                "period": self.get_report_period(report_type)
            },
            "summary": data.get("summary", {}),
            "metrics": data.get("metrics", {}),
            "details": data.get("details", [])
        }
        
        if PDF_AVAILABLE:
            return self.create_hvac_pdf_report(report_data, filename)
        else:
            return self.export_to_json(report_data, filename)
    
    def create_hvac_pdf_report(self, data: Dict[str, Any], filename: str) -> str:
        """📄 Create HVAC-specific PDF report"""
        
        output_path = self.temp_dir / f"{filename}.pdf"
        doc = SimpleDocTemplate(str(output_path), pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Custom styles
        title_style = ParagraphStyle(
            'HVACTitle',
            parent=styles['Heading1'],
            fontSize=28,
            spaceAfter=30,
            textColor=colors.HexColor('#0066cc'),
            alignment=1
        )
        
        subtitle_style = ParagraphStyle(
            'HVACSubtitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            textColor=colors.HexColor('#667eea')
        )
        
        # Header
        story.append(Paragraph("🔧 HVAC CRM System Report", title_style))
        story.append(Spacer(1, 20))
        
        # Report info
        info = data.get("report_info", {})
        story.append(Paragraph(f"<b>Report Type:</b> {info.get('type', 'Unknown').title()}", styles['Normal']))
        story.append(Paragraph(f"<b>Generated:</b> {info.get('generated_at', 'Unknown')}", styles['Normal']))
        story.append(Paragraph(f"<b>Period:</b> {info.get('period', 'Unknown')}", styles['Normal']))
        story.append(Spacer(1, 30))
        
        # Summary section
        summary = data.get("summary", {})
        if summary:
            story.append(Paragraph("📊 Executive Summary", subtitle_style))
            
            for key, value in summary.items():
                story.append(Paragraph(f"<b>{key.replace('_', ' ').title()}:</b> {value}", styles['Normal']))
            
            story.append(Spacer(1, 20))
        
        # Metrics section
        metrics = data.get("metrics", {})
        if metrics:
            story.append(Paragraph("📈 Key Metrics", subtitle_style))
            
            # Create metrics table
            metrics_data = [["Metric", "Value", "Status"]]
            for key, value in metrics.items():
                status = "✅ Good" if isinstance(value, (int, float)) and value > 0 else "⚠️ Check"
                metrics_data.append([key.replace('_', ' ').title(), str(value), status])
            
            metrics_table = Table(metrics_data)
            metrics_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#667eea')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(metrics_table)
            story.append(Spacer(1, 20))
        
        # Details section
        details = data.get("details", [])
        if details:
            story.append(Paragraph("📋 Detailed Information", subtitle_style))
            
            if isinstance(details, list) and details and isinstance(details[0], dict):
                # Create details table
                df = pd.DataFrame(details)
                details_data = [df.columns.tolist()] + df.values.tolist()
                
                details_table = Table(details_data)
                details_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#667eea')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(details_table)
        
        # Footer
        story.append(Spacer(1, 50))
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=10,
            textColor=colors.grey,
            alignment=1
        )
        story.append(Paragraph("Generated by Python_Mixer HVAC CRM System", footer_style))
        story.append(Paragraph("🌟 Cosmic-level quality for HVAC excellence", footer_style))
        
        # Build PDF
        doc.build(story)
        
        return str(output_path)
    
    def get_report_period(self, report_type: str) -> str:
        """📅 Get report period description"""
        now = datetime.now()
        
        if report_type == "daily":
            return now.strftime("%Y-%m-%d")
        elif report_type == "weekly":
            start = now - timedelta(days=7)
            return f"{start.strftime('%Y-%m-%d')} to {now.strftime('%Y-%m-%d')}"
        elif report_type == "monthly":
            start = now.replace(day=1)
            return start.strftime("%B %Y")
        else:
            return "Custom Period"
    
    def create_export_archive(self, files: List[str], archive_name: str = None) -> str:
        """📦 Create ZIP archive of exported files"""
        
        if not archive_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            archive_name = f"hvac_export_{timestamp}"
        
        archive_path = self.temp_dir / f"{archive_name}.zip"
        
        with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in files:
                if Path(file_path).exists():
                    zipf.write(file_path, Path(file_path).name)
        
        return str(archive_path)
    
    def get_export_summary(self, file_path: str) -> Dict[str, Any]:
        """📊 Get export file summary"""
        
        path = Path(file_path)
        
        if not path.exists():
            return {"error": "File not found"}
        
        file_size = path.stat().st_size
        created_time = datetime.fromtimestamp(path.stat().st_ctime)
        
        summary = {
            "filename": path.name,
            "format": path.suffix.lower(),
            "size_bytes": file_size,
            "size_human": self.format_file_size(file_size),
            "created_at": created_time.isoformat(),
            "path": str(path)
        }
        
        # Format-specific information
        if path.suffix.lower() == '.csv':
            try:
                df = pd.read_csv(path)
                summary.update({
                    "rows": len(df),
                    "columns": len(df.columns),
                    "column_names": df.columns.tolist()
                })
            except:
                pass
        
        elif path.suffix.lower() == '.json':
            try:
                with open(path, 'r') as f:
                    data = json.load(f)
                summary.update({
                    "json_keys": list(data.keys()) if isinstance(data, dict) else ["array"],
                    "data_type": type(data).__name__
                })
            except:
                pass
        
        return summary
    
    def format_file_size(self, size_bytes: int) -> str:
        """📏 Format file size in human-readable format"""
        
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"
    
    def cleanup_temp_files(self, older_than_hours: int = 24) -> int:
        """🧹 Clean up temporary export files"""
        
        cutoff_time = datetime.now() - timedelta(hours=older_than_hours)
        cleaned_count = 0
        
        for file_path in self.temp_dir.glob("*"):
            if file_path.is_file():
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_time < cutoff_time:
                    try:
                        file_path.unlink()
                        cleaned_count += 1
                    except:
                        pass
        
        return cleaned_count
