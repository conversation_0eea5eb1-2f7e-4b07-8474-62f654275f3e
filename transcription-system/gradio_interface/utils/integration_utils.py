#!/usr/bin/env python3
"""
🔗 Integration Utilities for Cosmic Gradio Interface
API connections, data transformation, and service orchestration
"""

import requests
import json
import asyncio
import aiohttp
from typing import Dict, Any, List, Optional, Union, Callable
from datetime import datetime, timedelta
import logging
import time
from dataclasses import dataclass
from enum import Enum
import hashlib

class ServiceStatus(Enum):
    """📊 Service status enumeration"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    OFFLINE = "offline"
    ERROR = "error"
    UNKNOWN = "unknown"

@dataclass
class ServiceResponse:
    """📦 Service response data structure"""
    success: bool
    data: Any = None
    error: str = None
    status_code: int = None
    response_time: float = None
    timestamp: datetime = None

class IntegrationUtils:
    """🔗 Service Integration and API Management Utilities"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Python_Mixer_HVAC_CRM/1.0',
            'Content-Type': 'application/json'
        })
        
        # Service configurations
        self.services = {
            "nvidia_stt": {
                "base_url": "http://localhost:8889",
                "endpoints": {
                    "health": "/health",
                    "transcribe": "/transcribe",
                    "models": "/models"
                },
                "timeout": 30,
                "retry_count": 3
            },
            "python_mixer": {
                "base_url": "http://localhost:8080",
                "endpoints": {
                    "health": "/health",
                    "process": "/process",
                    "status": "/status"
                },
                "timeout": 15,
                "retry_count": 3
            },
            "gemma_ai": {
                "base_url": "http://*************:1234",
                "endpoints": {
                    "health": "/v1/models",
                    "chat": "/v1/chat/completions",
                    "completions": "/v1/completions"
                },
                "timeout": 60,
                "retry_count": 2
            },
            "gobackend_kratos": {
                "base_url": "http://localhost:8080",
                "endpoints": {
                    "health": "/health",
                    "customers": "/api/customers",
                    "equipment": "/api/equipment",
                    "service_orders": "/api/service-orders"
                },
                "timeout": 20,
                "retry_count": 3
            }
        }
        
        # Cache for responses
        self.response_cache = {}
        self.cache_ttl = 300  # 5 minutes
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    async def call_service(self, service_name: str, endpoint: str, method: str = "GET", 
                          data: Dict = None, params: Dict = None, 
                          use_cache: bool = True) -> ServiceResponse:
        """🔗 Make API call to service"""
        
        if service_name not in self.services:
            return ServiceResponse(
                success=False,
                error=f"Unknown service: {service_name}",
                timestamp=datetime.now()
            )
        
        service_config = self.services[service_name]
        base_url = service_config["base_url"]
        timeout = service_config["timeout"]
        retry_count = service_config["retry_count"]
        
        # Build URL
        if endpoint.startswith('/'):
            url = f"{base_url}{endpoint}"
        else:
            endpoint_path = service_config["endpoints"].get(endpoint, f"/{endpoint}")
            url = f"{base_url}{endpoint_path}"
        
        # Check cache
        if use_cache and method.upper() == "GET":
            cache_key = self.get_cache_key(url, params)
            cached_response = self.get_cached_response(cache_key)
            if cached_response:
                return cached_response
        
        # Make request with retries
        for attempt in range(retry_count + 1):
            try:
                start_time = time.time()
                
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
                    async with session.request(
                        method=method.upper(),
                        url=url,
                        json=data,
                        params=params
                    ) as response:
                        response_time = time.time() - start_time
                        response_data = await response.json() if response.content_type == 'application/json' else await response.text()
                        
                        service_response = ServiceResponse(
                            success=response.status < 400,
                            data=response_data,
                            status_code=response.status,
                            response_time=response_time,
                            timestamp=datetime.now()
                        )
                        
                        if not service_response.success:
                            service_response.error = f"HTTP {response.status}: {response_data}"
                        
                        # Cache successful GET requests
                        if use_cache and method.upper() == "GET" and service_response.success:
                            cache_key = self.get_cache_key(url, params)
                            self.cache_response(cache_key, service_response)
                        
                        return service_response
                        
            except asyncio.TimeoutError:
                error_msg = f"Timeout after {timeout}s (attempt {attempt + 1}/{retry_count + 1})"
                if attempt == retry_count:
                    return ServiceResponse(
                        success=False,
                        error=error_msg,
                        timestamp=datetime.now()
                    )
                self.logger.warning(f"Service {service_name} timeout, retrying...")
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
                
            except Exception as e:
                error_msg = f"Request failed: {str(e)} (attempt {attempt + 1}/{retry_count + 1})"
                if attempt == retry_count:
                    return ServiceResponse(
                        success=False,
                        error=error_msg,
                        timestamp=datetime.now()
                    )
                self.logger.warning(f"Service {service_name} error, retrying: {str(e)}")
                await asyncio.sleep(2 ** attempt)
        
        return ServiceResponse(
            success=False,
            error="Max retries exceeded",
            timestamp=datetime.now()
        )
    
    def get_cache_key(self, url: str, params: Dict = None) -> str:
        """🔑 Generate cache key"""
        key_data = f"{url}:{json.dumps(params, sort_keys=True) if params else ''}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def cache_response(self, key: str, response: ServiceResponse) -> None:
        """💾 Cache service response"""
        self.response_cache[key] = {
            "response": response,
            "cached_at": datetime.now()
        }
    
    def get_cached_response(self, key: str) -> Optional[ServiceResponse]:
        """📦 Get cached response if valid"""
        if key not in self.response_cache:
            return None
        
        cached_item = self.response_cache[key]
        cached_at = cached_item["cached_at"]
        
        if datetime.now() - cached_at > timedelta(seconds=self.cache_ttl):
            del self.response_cache[key]
            return None
        
        return cached_item["response"]
    
    async def check_service_health(self, service_name: str) -> ServiceStatus:
        """🏥 Check service health status"""
        
        response = await self.call_service(service_name, "health", use_cache=False)
        
        if response.success:
            if response.response_time and response.response_time < 5.0:
                return ServiceStatus.HEALTHY
            else:
                return ServiceStatus.DEGRADED
        else:
            if "timeout" in response.error.lower():
                return ServiceStatus.DEGRADED
            else:
                return ServiceStatus.OFFLINE
    
    async def check_all_services_health(self) -> Dict[str, ServiceStatus]:
        """🏥 Check health of all services"""
        
        health_checks = []
        for service_name in self.services.keys():
            health_checks.append(self.check_service_health(service_name))
        
        results = await asyncio.gather(*health_checks, return_exceptions=True)
        
        health_status = {}
        for i, service_name in enumerate(self.services.keys()):
            if isinstance(results[i], Exception):
                health_status[service_name] = ServiceStatus.ERROR
            else:
                health_status[service_name] = results[i]
        
        return health_status
    
    async def transcribe_audio(self, audio_file_path: str, language: str = "pl") -> ServiceResponse:
        """🎤 Transcribe audio using NVIDIA STT"""
        
        try:
            # Prepare file for upload
            with open(audio_file_path, 'rb') as f:
                files = {'audio': f}
                data = {'language': language}
                
                # Use requests for file upload (aiohttp file upload is more complex)
                service_config = self.services["nvidia_stt"]
                url = f"{service_config['base_url']}/transcribe"
                
                start_time = time.time()
                response = self.session.post(
                    url,
                    files=files,
                    data=data,
                    timeout=service_config["timeout"]
                )
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    return ServiceResponse(
                        success=True,
                        data=response.json(),
                        status_code=response.status_code,
                        response_time=response_time,
                        timestamp=datetime.now()
                    )
                else:
                    return ServiceResponse(
                        success=False,
                        error=f"HTTP {response.status_code}: {response.text}",
                        status_code=response.status_code,
                        response_time=response_time,
                        timestamp=datetime.now()
                    )
                    
        except Exception as e:
            return ServiceResponse(
                success=False,
                error=f"Transcription failed: {str(e)}",
                timestamp=datetime.now()
            )
    
    async def analyze_with_ai(self, text: str, analysis_type: str = "hvac_analysis") -> ServiceResponse:
        """🧠 Analyze text using Gemma AI"""
        
        prompts = {
            "hvac_analysis": f"""
            Analyze the following HVAC-related text and provide insights:
            
            Text: {text}
            
            Please provide:
            1. Equipment mentioned
            2. Problems identified
            3. Recommended actions
            4. Urgency level (1-5)
            5. Estimated cost category (Low/Medium/High)
            
            Respond in JSON format.
            """,
            "customer_sentiment": f"""
            Analyze the customer sentiment in this text:
            
            Text: {text}
            
            Provide sentiment score (-1 to 1) and explanation.
            """,
            "technical_summary": f"""
            Create a technical summary of this HVAC service text:
            
            Text: {text}
            
            Focus on technical details and actionable items.
            """
        }
        
        prompt = prompts.get(analysis_type, prompts["hvac_analysis"])
        
        data = {
            "model": "gemma-3-4b-it",
            "messages": [
                {"role": "system", "content": "You are an HVAC technical expert assistant."},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.3,
            "max_tokens": 1000
        }
        
        return await self.call_service("gemma_ai", "chat", method="POST", data=data)
    
    async def sync_customer_data(self, customer_id: str) -> ServiceResponse:
        """👥 Sync customer data from GoBackend-Kratos"""
        
        endpoint = f"/api/customers/{customer_id}"
        return await self.call_service("gobackend_kratos", endpoint)
    
    async def get_equipment_data(self, equipment_id: str = None) -> ServiceResponse:
        """🔧 Get equipment data"""
        
        if equipment_id:
            endpoint = f"/api/equipment/{equipment_id}"
        else:
            endpoint = "/api/equipment"
        
        return await self.call_service("gobackend_kratos", endpoint)
    
    async def create_service_order(self, order_data: Dict[str, Any]) -> ServiceResponse:
        """📋 Create new service order"""
        
        return await self.call_service(
            "gobackend_kratos", 
            "/api/service-orders", 
            method="POST", 
            data=order_data
        )
    
    def transform_stt_response(self, stt_response: Dict[str, Any]) -> Dict[str, Any]:
        """🔄 Transform STT response to standard format"""
        
        return {
            "transcription": stt_response.get("text", ""),
            "confidence": stt_response.get("confidence", 0.0),
            "language": stt_response.get("language", "unknown"),
            "duration": stt_response.get("duration", 0.0),
            "words": stt_response.get("words", []),
            "metadata": {
                "model": stt_response.get("model", "unknown"),
                "processing_time": stt_response.get("processing_time", 0.0)
            }
        }
    
    def transform_ai_response(self, ai_response: Dict[str, Any]) -> Dict[str, Any]:
        """🔄 Transform AI response to standard format"""
        
        choices = ai_response.get("choices", [])
        if choices:
            message = choices[0].get("message", {})
            content = message.get("content", "")
        else:
            content = ""
        
        return {
            "analysis": content,
            "model": ai_response.get("model", "unknown"),
            "usage": ai_response.get("usage", {}),
            "metadata": {
                "created": ai_response.get("created", 0),
                "id": ai_response.get("id", "")
            }
        }
    
    async def orchestrate_audio_processing(self, audio_file_path: str, language: str = "pl") -> Dict[str, Any]:
        """🎼 Orchestrate complete audio processing pipeline"""
        
        results = {
            "success": False,
            "transcription": None,
            "ai_analysis": None,
            "errors": [],
            "processing_time": 0
        }
        
        start_time = time.time()
        
        try:
            # Step 1: Transcribe audio
            self.logger.info("Starting audio transcription...")
            stt_response = await self.transcribe_audio(audio_file_path, language)
            
            if stt_response.success:
                results["transcription"] = self.transform_stt_response(stt_response.data)
                
                # Step 2: Analyze transcription with AI
                transcription_text = results["transcription"]["transcription"]
                if transcription_text:
                    self.logger.info("Starting AI analysis...")
                    ai_response = await self.analyze_with_ai(transcription_text, "hvac_analysis")
                    
                    if ai_response.success:
                        results["ai_analysis"] = self.transform_ai_response(ai_response.data)
                        results["success"] = True
                    else:
                        results["errors"].append(f"AI analysis failed: {ai_response.error}")
                else:
                    results["errors"].append("No transcription text to analyze")
            else:
                results["errors"].append(f"Transcription failed: {stt_response.error}")
        
        except Exception as e:
            results["errors"].append(f"Pipeline error: {str(e)}")
        
        results["processing_time"] = time.time() - start_time
        
        return results
    
    def get_service_metrics(self) -> Dict[str, Any]:
        """📊 Get integration metrics"""
        
        cache_size = len(self.response_cache)
        cache_hit_rate = 0.0  # Would need to track hits/misses in production
        
        return {
            "cache_size": cache_size,
            "cache_hit_rate": cache_hit_rate,
            "services_configured": len(self.services),
            "cache_ttl_seconds": self.cache_ttl,
            "last_updated": datetime.now().isoformat()
        }
    
    def clear_cache(self) -> int:
        """🗑️ Clear response cache"""
        cache_size = len(self.response_cache)
        self.response_cache.clear()
        return cache_size
    
    def update_service_config(self, service_name: str, config: Dict[str, Any]) -> bool:
        """⚙️ Update service configuration"""
        
        if service_name not in self.services:
            return False
        
        self.services[service_name].update(config)
        
        # Clear cache for this service
        keys_to_remove = []
        for key in self.response_cache.keys():
            if service_name in key:
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self.response_cache[key]
        
        return True
    
    async def test_integration_pipeline(self) -> Dict[str, Any]:
        """🧪 Test complete integration pipeline"""
        
        test_results = {
            "overall_success": False,
            "service_health": {},
            "api_tests": {},
            "performance_metrics": {},
            "timestamp": datetime.now().isoformat()
        }
        
        # Test service health
        health_status = await self.check_all_services_health()
        test_results["service_health"] = {k: v.value for k, v in health_status.items()}
        
        # Test basic API calls
        api_tests = {}
        
        # Test NVIDIA STT health
        stt_health = await self.call_service("nvidia_stt", "health")
        api_tests["nvidia_stt_health"] = stt_health.success
        
        # Test Gemma AI models
        ai_models = await self.call_service("gemma_ai", "health")
        api_tests["gemma_ai_models"] = ai_models.success
        
        # Test GoBackend health
        backend_health = await self.call_service("gobackend_kratos", "health")
        api_tests["gobackend_health"] = backend_health.success
        
        test_results["api_tests"] = api_tests
        
        # Performance metrics
        test_results["performance_metrics"] = self.get_service_metrics()
        
        # Overall success
        healthy_services = sum(1 for status in health_status.values() if status == ServiceStatus.HEALTHY)
        successful_apis = sum(1 for success in api_tests.values() if success)
        
        test_results["overall_success"] = (
            healthy_services >= len(self.services) * 0.7 and  # 70% services healthy
            successful_apis >= len(api_tests) * 0.7  # 70% API tests successful
        )
        
        return test_results
