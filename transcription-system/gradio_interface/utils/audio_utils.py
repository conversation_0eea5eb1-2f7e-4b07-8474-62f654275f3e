#!/usr/bin/env python3
"""
🎤 Audio Utilities for Cosmic Gradio Interface
Audio processing, waveform visualization, and STT integration
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.animation import FuncAnimation
import librosa
import soundfile as sf
import io
import base64
from typing import Tuple, Optional, Dict, Any, List
from pathlib import Path
import tempfile
import requests
import json

class AudioUtils:
    """🎤 Audio Processing and Visualization Utilities"""
    
    def __init__(self):
        self.sample_rate = 16000
        self.supported_formats = ['.wav', '.mp3', '.m4a', '.flac', '.ogg']
        self.max_duration = 300  # 5 minutes max
        self.chunk_size = 1024
        
    def load_audio(self, file_path: str) -> Tuple[np.ndarray, int]:
        """📂 Load audio file"""
        try:
            audio, sr = librosa.load(file_path, sr=self.sample_rate)
            
            # Limit duration
            max_samples = self.max_duration * sr
            if len(audio) > max_samples:
                audio = audio[:max_samples]
            
            return audio, sr
        except Exception as e:
            raise ValueError(f"Failed to load audio: {str(e)}")
    
    def create_waveform_plot(self, audio: np.ndarray, sr: int, title: str = "Audio Waveform") -> str:
        """📊 Create waveform visualization"""
        try:
            # Create figure with cosmic styling
            plt.style.use('dark_background')
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
            fig.patch.set_facecolor('#0c0c0c')
            
            # Time axis
            time = np.linspace(0, len(audio) / sr, len(audio))
            
            # Waveform plot
            ax1.plot(time, audio, color='#00d4ff', linewidth=0.8, alpha=0.8)
            ax1.fill_between(time, audio, alpha=0.3, color='#667eea')
            ax1.set_title(title, color='#ffffff', fontsize=16, fontweight='bold')
            ax1.set_xlabel('Time (seconds)', color='#ffffff')
            ax1.set_ylabel('Amplitude', color='#ffffff')
            ax1.grid(True, alpha=0.3, color='#ffffff')
            ax1.set_facecolor('#1a1a2e')
            
            # Spectrogram
            D = librosa.amplitude_to_db(np.abs(librosa.stft(audio)), ref=np.max)
            img = librosa.display.specshow(D, y_axis='hz', x_axis='time', sr=sr, ax=ax2, cmap='plasma')
            ax2.set_title('Spectrogram', color='#ffffff', fontsize=14, fontweight='bold')
            ax2.set_xlabel('Time (seconds)', color='#ffffff')
            ax2.set_ylabel('Frequency (Hz)', color='#ffffff')
            ax2.set_facecolor('#1a1a2e')
            
            # Add colorbar
            cbar = plt.colorbar(img, ax=ax2, format='%+2.0f dB')
            cbar.ax.yaxis.set_tick_params(color='#ffffff')
            cbar.ax.yaxis.set_ticklabels(cbar.ax.yaxis.get_ticklabels(), color='#ffffff')
            
            # Style adjustments
            for ax in [ax1, ax2]:
                ax.tick_params(colors='#ffffff')
                for spine in ax.spines.values():
                    spine.set_color('#ffffff')
            
            plt.tight_layout()
            
            # Convert to base64 string
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', facecolor='#0c0c0c', dpi=100, bbox_inches='tight')
            buffer.seek(0)
            plot_data = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return f"data:image/png;base64,{plot_data}"
            
        except Exception as e:
            return f"Error creating waveform: {str(e)}"
    
    def create_real_time_waveform(self, audio_chunks: List[np.ndarray]) -> str:
        """📈 Create real-time waveform visualization"""
        try:
            # Combine audio chunks
            if not audio_chunks:
                return self.create_empty_waveform()
            
            audio = np.concatenate(audio_chunks)
            return self.create_waveform_plot(audio, self.sample_rate, "Real-time Audio")
            
        except Exception as e:
            return f"Error creating real-time waveform: {str(e)}"
    
    def create_empty_waveform(self) -> str:
        """📊 Create empty waveform placeholder"""
        plt.style.use('dark_background')
        fig, ax = plt.subplots(figsize=(12, 4))
        fig.patch.set_facecolor('#0c0c0c')
        
        ax.set_xlim(0, 10)
        ax.set_ylim(-1, 1)
        ax.set_title('🎤 Ready for Audio Input', color='#00d4ff', fontsize=16, fontweight='bold')
        ax.set_xlabel('Time (seconds)', color='#ffffff')
        ax.set_ylabel('Amplitude', color='#ffffff')
        ax.grid(True, alpha=0.3, color='#ffffff')
        ax.set_facecolor('#1a1a2e')
        
        # Add cosmic effects
        ax.axhline(y=0, color='#667eea', linewidth=2, alpha=0.5)
        ax.text(5, 0, '🌌 Cosmic Audio Processor Ready', 
                ha='center', va='center', color='#00d4ff', fontsize=14, fontweight='bold')
        
        # Style
        ax.tick_params(colors='#ffffff')
        for spine in ax.spines.values():
            spine.set_color('#ffffff')
        
        plt.tight_layout()
        
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', facecolor='#0c0c0c', dpi=100, bbox_inches='tight')
        buffer.seek(0)
        plot_data = base64.b64encode(buffer.getvalue()).decode()
        plt.close()
        
        return f"data:image/png;base64,{plot_data}"
    
    def analyze_audio_quality(self, audio: np.ndarray, sr: int) -> Dict[str, Any]:
        """🔍 Analyze audio quality metrics"""
        try:
            # Basic metrics
            duration = len(audio) / sr
            rms_energy = np.sqrt(np.mean(audio**2))
            peak_amplitude = np.max(np.abs(audio))
            
            # Signal-to-noise ratio estimation
            # Simple approach: compare energy in speech vs silence regions
            frame_length = int(0.025 * sr)  # 25ms frames
            hop_length = int(0.01 * sr)     # 10ms hop
            
            frames = librosa.util.frame(audio, frame_length=frame_length, hop_length=hop_length)
            frame_energy = np.sum(frames**2, axis=0)
            
            # Estimate SNR
            energy_threshold = np.percentile(frame_energy, 30)  # Bottom 30% as noise
            noise_frames = frame_energy[frame_energy <= energy_threshold]
            signal_frames = frame_energy[frame_energy > energy_threshold]
            
            if len(noise_frames) > 0 and len(signal_frames) > 0:
                snr_db = 10 * np.log10(np.mean(signal_frames) / np.mean(noise_frames))
            else:
                snr_db = 0
            
            # Spectral features
            spectral_centroid = librosa.feature.spectral_centroid(y=audio, sr=sr)[0]
            spectral_rolloff = librosa.feature.spectral_rolloff(y=audio, sr=sr)[0]
            zero_crossing_rate = librosa.feature.zero_crossing_rate(audio)[0]
            
            # Quality assessment
            quality_score = self.calculate_quality_score(
                snr_db, peak_amplitude, rms_energy, np.mean(spectral_centroid)
            )
            
            return {
                "duration": round(duration, 2),
                "sample_rate": sr,
                "rms_energy": round(rms_energy, 4),
                "peak_amplitude": round(peak_amplitude, 4),
                "snr_db": round(snr_db, 2),
                "spectral_centroid_mean": round(np.mean(spectral_centroid), 2),
                "spectral_rolloff_mean": round(np.mean(spectral_rolloff), 2),
                "zero_crossing_rate_mean": round(np.mean(zero_crossing_rate), 4),
                "quality_score": quality_score,
                "quality_grade": self.get_quality_grade(quality_score)
            }
            
        except Exception as e:
            return {"error": f"Audio analysis failed: {str(e)}"}
    
    def calculate_quality_score(self, snr_db: float, peak_amp: float, rms_energy: float, spectral_centroid: float) -> float:
        """📊 Calculate overall audio quality score (0-100)"""
        score = 0
        
        # SNR contribution (40%)
        if snr_db >= 20:
            score += 40
        elif snr_db >= 10:
            score += 30
        elif snr_db >= 5:
            score += 20
        else:
            score += 10
        
        # Peak amplitude contribution (20%)
        if 0.3 <= peak_amp <= 0.9:
            score += 20
        elif 0.1 <= peak_amp <= 1.0:
            score += 15
        else:
            score += 5
        
        # RMS energy contribution (20%)
        if 0.05 <= rms_energy <= 0.3:
            score += 20
        elif 0.01 <= rms_energy <= 0.5:
            score += 15
        else:
            score += 5
        
        # Spectral centroid contribution (20%)
        if 1000 <= spectral_centroid <= 4000:  # Good for speech
            score += 20
        elif 500 <= spectral_centroid <= 6000:
            score += 15
        else:
            score += 5
        
        return min(100, max(0, score))
    
    def get_quality_grade(self, score: float) -> str:
        """🏆 Get quality grade based on score"""
        if score >= 90:
            return "A+ Excellent"
        elif score >= 80:
            return "A Good"
        elif score >= 70:
            return "B Fair"
        elif score >= 60:
            return "C Poor"
        else:
            return "D Very Poor"
    
    def convert_audio_format(self, input_path: str, output_format: str = 'wav') -> str:
        """🔄 Convert audio to different format"""
        try:
            audio, sr = self.load_audio(input_path)
            
            # Create temporary output file
            with tempfile.NamedTemporaryFile(suffix=f'.{output_format}', delete=False) as tmp_file:
                output_path = tmp_file.name
            
            # Save in new format
            sf.write(output_path, audio, sr)
            
            return output_path
            
        except Exception as e:
            raise ValueError(f"Audio conversion failed: {str(e)}")
    
    def enhance_audio_for_stt(self, audio: np.ndarray, sr: int) -> np.ndarray:
        """🎯 Enhance audio for better STT accuracy"""
        try:
            # Normalize audio
            audio = librosa.util.normalize(audio)
            
            # Apply pre-emphasis filter
            pre_emphasis = 0.97
            audio = np.append(audio[0], audio[1:] - pre_emphasis * audio[:-1])
            
            # Noise reduction (simple spectral subtraction)
            # This is a basic implementation - in production, use more sophisticated methods
            stft = librosa.stft(audio)
            magnitude = np.abs(stft)
            phase = np.angle(stft)
            
            # Estimate noise from first 0.5 seconds
            noise_frames = int(0.5 * sr / 512)  # Assuming hop_length=512
            noise_spectrum = np.mean(magnitude[:, :noise_frames], axis=1, keepdims=True)
            
            # Spectral subtraction
            alpha = 2.0  # Over-subtraction factor
            enhanced_magnitude = magnitude - alpha * noise_spectrum
            enhanced_magnitude = np.maximum(enhanced_magnitude, 0.1 * magnitude)
            
            # Reconstruct audio
            enhanced_stft = enhanced_magnitude * np.exp(1j * phase)
            enhanced_audio = librosa.istft(enhanced_stft)
            
            return enhanced_audio
            
        except Exception as e:
            # If enhancement fails, return original audio
            return audio
    
    def create_audio_metrics_display(self, metrics: Dict[str, Any]) -> str:
        """📊 Create HTML display for audio metrics"""
        if "error" in metrics:
            return f'<div class="cosmic-card"><h3>❌ Audio Analysis Error</h3><p>{metrics["error"]}</p></div>'
        
        quality_color = {
            "A+ Excellent": "#11998e",
            "A Good": "#38ef7d", 
            "B Fair": "#f093fb",
            "C Poor": "#ff6b6b",
            "D Very Poor": "#ff416c"
        }.get(metrics["quality_grade"], "#666666")
        
        html = f'''
        <div class="cosmic-card">
            <h3>📊 Audio Quality Analysis</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 1rem 0;">
                
                <div class="metric-display">
                    <h4>⏱️ Duration</h4>
                    <h2>{metrics["duration"]}s</h2>
                </div>
                
                <div class="metric-display">
                    <h4>📊 Sample Rate</h4>
                    <h2>{metrics["sample_rate"]} Hz</h2>
                </div>
                
                <div class="metric-display">
                    <h4>🔊 RMS Energy</h4>
                    <h2>{metrics["rms_energy"]}</h2>
                </div>
                
                <div class="metric-display">
                    <h4>📈 Peak Amplitude</h4>
                    <h2>{metrics["peak_amplitude"]}</h2>
                </div>
                
                <div class="metric-display">
                    <h4>📡 SNR</h4>
                    <h2>{metrics["snr_db"]} dB</h2>
                </div>
                
                <div class="metric-display" style="border-color: {quality_color};">
                    <h4>🏆 Quality</h4>
                    <h2 style="color: {quality_color};">{metrics["quality_grade"]}</h2>
                    <p style="color: {quality_color};">{metrics["quality_score"]}/100</p>
                </div>
                
            </div>
            
            <div style="margin-top: 1rem;">
                <h4>🎵 Spectral Features</h4>
                <p><strong>Spectral Centroid:</strong> {metrics["spectral_centroid_mean"]} Hz</p>
                <p><strong>Spectral Rolloff:</strong> {metrics["spectral_rolloff_mean"]} Hz</p>
                <p><strong>Zero Crossing Rate:</strong> {metrics["zero_crossing_rate_mean"]}</p>
            </div>
        </div>
        '''
        
        return html
    
    def get_supported_formats(self) -> List[str]:
        """📋 Get list of supported audio formats"""
        return self.supported_formats
    
    def validate_audio_file(self, file_path: str) -> Dict[str, Any]:
        """✅ Validate audio file"""
        try:
            path = Path(file_path)
            
            # Check if file exists
            if not path.exists():
                return {"valid": False, "error": "File does not exist"}
            
            # Check file extension
            if path.suffix.lower() not in self.supported_formats:
                return {"valid": False, "error": f"Unsupported format. Supported: {', '.join(self.supported_formats)}"}
            
            # Check file size (max 100MB)
            file_size = path.stat().st_size
            max_size = 100 * 1024 * 1024  # 100MB
            if file_size > max_size:
                return {"valid": False, "error": f"File too large. Max size: {max_size // (1024*1024)}MB"}
            
            # Try to load audio
            try:
                audio, sr = self.load_audio(file_path)
                duration = len(audio) / sr
                
                if duration > self.max_duration:
                    return {"valid": False, "error": f"Audio too long. Max duration: {self.max_duration}s"}
                
                return {
                    "valid": True,
                    "duration": duration,
                    "sample_rate": sr,
                    "file_size": file_size,
                    "format": path.suffix.lower()
                }
                
            except Exception as e:
                return {"valid": False, "error": f"Cannot load audio: {str(e)}"}
                
        except Exception as e:
            return {"valid": False, "error": f"Validation error: {str(e)}"}
