# 🎆 Gradio Interface Requirements
# Dependencies for Python_Mixer HVAC CRM Cosmic Interface

# 🎨 Core Interface
gradio==4.12.0
streamlit==1.29.0

# 📊 Data Processing & Visualization
pandas==2.0.3
numpy==1.24.3
plotly==5.17.0
matplotlib==3.7.2
seaborn==0.12.2

# 🎤 Audio Processing
librosa==0.10.1
soundfile==0.12.1
pydub==0.25.1

# 📄 Document Generation
reportlab==4.0.7
fpdf2==2.7.6
openpyxl==3.1.2

# 🌐 Web & API
aiohttp==3.9.1
aiofiles==23.2.1
requests==2.31.0
fastapi==0.104.1
uvicorn==0.24.0

# 🔧 Data Validation & Models
pydantic==2.5.0

# 🎨 UI Enhancements
pillow==10.1.0
opencv-python==********

# 📊 Advanced Analytics
scipy==1.11.4
scikit-learn==1.3.2
statsmodels==0.14.0

# 🔍 Text Processing
textdistance==4.6.0

# 🧪 Testing
pytest==7.4.3
pytest-asyncio==0.21.1

# 🔧 Utilities
python-dotenv==1.0.0
click==8.1.7
tqdm==4.66.1
colorama==0.4.6
psutil==5.9.6

# 🎯 Performance Monitoring
memory-profiler==0.61.0

# 🌍 Internationalization
babel==2.13.1

# 📝 Configuration
pyyaml==6.0.1
toml==0.10.2

# 🔐 Security
cryptography==41.0.7

# 📊 Database
sqlalchemy==2.0.23
psycopg2-binary==2.9.9

# 🎵 Audio Format Support
mutagen==1.47.0

# 📈 Time Series
arrow==1.3.0

# 🎨 Color Management
colorcet==3.0.1

# 🔄 Task Queue
celery==5.3.4

# 📧 Email Support
email-validator==2.1.0

# 🌐 HTTP Client
httpx==0.25.2
