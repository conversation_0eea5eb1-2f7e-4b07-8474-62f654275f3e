#!/bin/bash
# 🚀 Launch Script for Cosmic Gradio Interface
# Python_Mixer HVAC CRM - Advanced Interface Launcher

set -e

echo "🎆 COSMIC GRADIO INTERFACE LAUNCHER"
echo "=================================="
echo "🚀 Python_Mixer HVAC CRM Interface"
echo "🎨 Cosmic Design with Advanced Features"
echo "🔗 Integrated with NVIDIA STT and Gemma AI"
echo ""

# 🔧 Configuration
INTERFACE_DIR=$(dirname "$0")
VENV_NAME="gradio_cosmic_env"
PYTHON_VERSION="python3"
PORT=7860
HOST="0.0.0.0"

cd "$INTERFACE_DIR"

# 🎯 Check Python version
echo "🐍 Checking Python version..."
if ! command -v $PYTHON_VERSION &> /dev/null; then
    echo "❌ Python 3 not found. Please install Python 3.8 or higher."
    exit 1
fi

PYTHON_VER=$($PYTHON_VERSION --version 2>&1 | awk '{print $2}')
echo "✅ Python version: $PYTHON_VER"

# 🌐 Create virtual environment
echo ""
echo "🌐 Setting up virtual environment..."
if [ ! -d "$VENV_NAME" ]; then
    echo "🔧 Creating virtual environment: $VENV_NAME"
    $PYTHON_VERSION -m venv $VENV_NAME
    echo "✅ Virtual environment created"
else
    echo "✅ Virtual environment already exists"
fi

# 🔧 Activate virtual environment
echo "🔧 Activating virtual environment..."
source $VENV_NAME/bin/activate

# 📦 Install dependencies
echo ""
echo "📦 Installing dependencies..."
echo "⏳ This may take a few minutes..."

pip install --upgrade pip
pip install -r requirements.txt

echo "✅ Dependencies installed successfully"

# 📁 Create necessary directories
echo ""
echo "📁 Setting up directory structure..."

mkdir -p logs
mkdir -p transcriptions
mkdir -p exports
mkdir -p temp
mkdir -p data

echo "✅ Directory structure ready"

# 🔍 System health check
echo ""
echo "🔍 Performing system health check..."

# Check if ports are available
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️ Port $PORT is already in use"
    echo "💡 You can change the port in config.json"
    read -p "Continue anyway? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
else
    echo "✅ Port $PORT is available"
fi

# Check service connectivity
echo "🔗 Checking service connectivity..."

# NVIDIA STT Service
if curl -f http://localhost:8889/health > /dev/null 2>&1; then
    echo "✅ NVIDIA STT service: Online"
else
    echo "⚠️ NVIDIA STT service: Offline"
    echo "💡 Make sure NVIDIA STT service is running on port 8889"
fi

# Python_Mixer Backend
if curl -f http://localhost:8080/health > /dev/null 2>&1; then
    echo "✅ Python_Mixer backend: Online"
else
    echo "⚠️ Python_Mixer backend: Offline"
    echo "💡 Make sure Python_Mixer backend is running on port 8080"
fi

# Gemma AI Service
if curl -f http://*************:1234/v1/models > /dev/null 2>&1; then
    echo "✅ Gemma AI service: Online"
else
    echo "⚠️ Gemma AI service: Offline"
    echo "💡 Make sure LM Studio with Gemma is running on *************:1234"
fi

# 🎨 Interface configuration
echo ""
echo "🎨 Interface Configuration"
echo "========================="
echo "🌐 Host: $HOST"
echo "🔌 Port: $PORT"
echo "🎯 URL: http://localhost:$PORT"
echo "🎨 Theme: Cosmic Dark (default)"
echo "🌍 Language: English (default)"
echo "🔄 Auto-refresh: Enabled"
echo ""

# 🚀 Launch options
echo "🚀 LAUNCH OPTIONS"
echo "================="
echo "1. 🎆 Launch Cosmic Interface (Default)"
echo "2. 🔧 Launch with Debug Mode"
echo "3. 🌐 Launch with Public Sharing"
echo "4. ⚙️ Configure Settings"
echo "5. 📊 View System Status"
echo "6. 🧪 Run Tests"
echo ""

read -p "Select option (1-6) [1]: " choice
choice=${choice:-1}

case $choice in
    1)
        echo ""
        echo "🎆 Launching Cosmic Interface..."
        echo "🌐 Interface will be available at: http://localhost:$PORT"
        echo "🎨 Cosmic design with advanced features enabled"
        echo "⏰ Starting in 3 seconds..."
        sleep 3
        $PYTHON_VERSION main_gradio_interface.py
        ;;
    2)
        echo ""
        echo "🔧 Launching with Debug Mode..."
        echo "🐛 Debug information will be displayed"
        DEBUG=true $PYTHON_VERSION main_gradio_interface.py
        ;;
    3)
        echo ""
        echo "🌐 Launching with Public Sharing..."
        echo "⚠️ This will create a public URL accessible from anywhere"
        read -p "Are you sure? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            SHARE=true $PYTHON_VERSION main_gradio_interface.py
        else
            echo "❌ Launch cancelled"
        fi
        ;;
    4)
        echo ""
        echo "⚙️ Configuration Options"
        echo "========================"
        echo "📝 Edit config.json to modify settings:"
        echo "  - Service endpoints"
        echo "  - Theme preferences"
        echo "  - Language settings"
        echo "  - Feature toggles"
        echo "  - Performance options"
        echo ""
        echo "📁 Config file: $(pwd)/config.json"
        
        if command -v nano &> /dev/null; then
            read -p "Open config in nano? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                nano config.json
            fi
        fi
        ;;
    5)
        echo ""
        echo "📊 System Status"
        echo "==============="
        echo "🐍 Python: $PYTHON_VER"
        echo "📦 Virtual Environment: $VENV_NAME"
        echo "📁 Working Directory: $(pwd)"
        echo "💾 Disk Space: $(df -h . | awk 'NR==2{print $4}') available"
        echo "🧠 Memory: $(free -h | awk 'NR==2{print $7}') available"
        echo ""
        echo "📋 Installed Packages:"
        pip list | grep -E "(gradio|plotly|pandas|librosa)"
        ;;
    6)
        echo ""
        echo "🧪 Running Tests..."
        if [ -f "tests/test_interface.py" ]; then
            $PYTHON_VERSION -m pytest tests/ -v
        else
            echo "⚠️ No tests found. Creating basic test structure..."
            mkdir -p tests
            echo "# Basic interface tests" > tests/test_interface.py
            echo "✅ Test structure created"
        fi
        ;;
    *)
        echo "❌ Invalid option. Please run the script again."
        exit 1
        ;;
esac

# 🎆 Success message
echo ""
echo "🎆 COSMIC INTERFACE READY!"
echo "========================="
echo "🌟 Interface launched successfully"
echo "🎨 Cosmic design activated"
echo "🔗 All integrations configured"
echo "📊 Real-time monitoring enabled"
echo ""
echo "🚀 Access your interface at: http://localhost:$PORT"
echo "💡 Press Ctrl+C to stop the interface"
echo ""
echo "🎯 Features Available:"
echo "  🎤 Audio Upload & Transcription"
echo "  🔧 HVAC Glossary & Equipment Database"
echo "  📊 Real-time System Monitoring"
echo "  📤 Advanced Export Functionality"
echo "  🎨 Cosmic Dark/Light Themes"
echo "  🌍 Multi-language Support (EN/PL)"
echo ""
echo "🎆 Enjoy your Cosmic HVAC CRM Experience!"

# 🔧 Cleanup on exit
trap 'echo ""; echo "🔄 Cleaning up..."; deactivate; echo "✅ Cleanup completed"; echo "👋 Goodbye!"' EXIT
