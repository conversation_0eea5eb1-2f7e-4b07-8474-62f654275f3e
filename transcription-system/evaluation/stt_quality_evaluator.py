#!/usr/bin/env python3
"""
🧪 NVIDIA STT Quality Evaluator
Comprehensive evaluation framework for Polish HVAC transcription accuracy
"""

import os
import asyncio
import logging
import json
import time
import statistics
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from pathlib import Path
import difflib

import aiofiles
import aiohttp
from pydantic import BaseModel
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# 🔧 Konfiguracja logowania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EvaluationResult(BaseModel):
    """📊 Wynik ewaluacji transkrypcji"""
    audio_file: str
    expected_transcript: str
    actual_transcript: str
    word_accuracy: float
    character_accuracy: float
    hvac_terms_accuracy: float
    processing_time: float
    confidence_score: float
    model_used: str
    timestamp: datetime

class HVACSTTEvaluator:
    """🧪 Ewaluator jakości NVIDIA STT dla HVAC"""
    
    def __init__(self, stt_url: str = "http://localhost:8889"):
        self.stt_url = stt_url
        self.results: List[EvaluationResult] = []
        
        # 🎯 HVAC test cases - expected transcriptions
        self.test_cases = {
            "klimatyzacja_lg_problem.wav": "Dzień dobry, mam problem z klimatyzacją LG. Urządzenie nie chłodzi prawidłowo i wydaje dziwne dźwięki.",
            "pompa_ciepla_daikin.wav": "Potrzebuję serwisu pompy ciepła Daikin. System nie grzeje i pokazuje kod błędu E7.",
            "instalacja_vrf_biurowiec.wav": "Planujemy instalację systemu VRF w nowym biurowcu. Potrzebujemy wyceny na 20 jednostek wewnętrznych.",
            "serwis_samsung_awaria.wav": "Klimatyzator Samsung przestał działać po burzy. Pilot nie reaguje i dioda miga na czerwono.",
            "wymiana_filtrow_konserwacja.wav": "Kiedy należy wymieniać filtry w klimatyzacji? Czy można to robić samodzielnie?",
            "naprawa_kompresora_mitsubishi.wav": "Kompresor w klimatyzatorze Mitsubishi wydaje głośne dźwięki. Czy to poważna awaria?",
            "montaz_split_mieszkanie.wav": "Chcemy zamontować klimatyzację split w mieszkaniu. Jakie są koszty instalacji?",
            "czyszczenie_parownika_serwis.wav": "Czy parownik wymaga regularnego czyszczenia? Jak często powinien być serwis?"
        }
        
        # 🔍 HVAC terminology for specialized accuracy testing
        self.hvac_terms = [
            "klimatyzacja", "klimatyzator", "pompa ciepła", "VRF", "VRV",
            "split", "multi split", "kompresor", "parownik", "skraplacz",
            "LG", "Daikin", "Samsung", "Mitsubishi", "Fujitsu",
            "serwis", "naprawa", "instalacja", "montaż", "wymiana",
            "filtr", "freon", "pilot", "kod błędu", "awaria"
        ]
    
    async def evaluate_transcription_quality(self, audio_file: str, expected: str) -> EvaluationResult:
        """🎯 Ewaluacja jakości pojedynczej transkrypcji"""
        
        start_time = time.time()
        
        try:
            # 🎤 Wykonanie transkrypcji
            actual_result = await self._transcribe_audio(audio_file)
            processing_time = time.time() - start_time
            
            actual_transcript = actual_result.get("transcript", "")
            confidence = actual_result.get("confidence", 0.0)
            model_used = actual_result.get("model_used", "unknown")
            
            # 📊 Obliczenie metryk accuracy
            word_accuracy = self._calculate_word_accuracy(expected, actual_transcript)
            char_accuracy = self._calculate_character_accuracy(expected, actual_transcript)
            hvac_accuracy = self._calculate_hvac_terms_accuracy(expected, actual_transcript)
            
            result = EvaluationResult(
                audio_file=audio_file,
                expected_transcript=expected,
                actual_transcript=actual_transcript,
                word_accuracy=word_accuracy,
                character_accuracy=char_accuracy,
                hvac_terms_accuracy=hvac_accuracy,
                processing_time=processing_time,
                confidence_score=confidence,
                model_used=model_used,
                timestamp=datetime.now()
            )
            
            self.results.append(result)
            
            logger.info(f"✅ Evaluated {audio_file}: Word={word_accuracy:.2f}%, HVAC={hvac_accuracy:.2f}%")
            return result
            
        except Exception as e:
            logger.error(f"❌ Evaluation failed for {audio_file}: {e}")
            raise
    
    async def _transcribe_audio(self, audio_file: str) -> Dict:
        """🎤 Wykonanie transkrypcji przez NVIDIA STT"""
        
        async with aiohttp.ClientSession() as session:
            with open(audio_file, 'rb') as f:
                data = aiohttp.FormData()
                data.add_field('audio_file', f, filename=os.path.basename(audio_file))
                data.add_field('language', 'pl')
                data.add_field('hvac_context', 'true')
                
                async with session.post(f"{self.stt_url}/transcribe", data=data) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        raise Exception(f"STT request failed: {response.status}")
    
    def _calculate_word_accuracy(self, expected: str, actual: str) -> float:
        """📝 Obliczenie accuracy na poziomie słów"""
        expected_words = expected.lower().split()
        actual_words = actual.lower().split()
        
        # Użycie SequenceMatcher dla lepszej accuracy
        matcher = difflib.SequenceMatcher(None, expected_words, actual_words)
        return matcher.ratio() * 100
    
    def _calculate_character_accuracy(self, expected: str, actual: str) -> float:
        """🔤 Obliczenie accuracy na poziomie znaków"""
        expected_chars = expected.lower().replace(" ", "")
        actual_chars = actual.lower().replace(" ", "")
        
        matcher = difflib.SequenceMatcher(None, expected_chars, actual_chars)
        return matcher.ratio() * 100
    
    def _calculate_hvac_terms_accuracy(self, expected: str, actual: str) -> float:
        """🔧 Obliczenie accuracy dla terminów HVAC"""
        expected_lower = expected.lower()
        actual_lower = actual.lower()
        
        expected_hvac = [term for term in self.hvac_terms if term.lower() in expected_lower]
        if not expected_hvac:
            return 100.0  # Brak terminów HVAC = 100% accuracy
        
        correct_hvac = [term for term in expected_hvac if term.lower() in actual_lower]
        return (len(correct_hvac) / len(expected_hvac)) * 100
    
    async def run_comprehensive_evaluation(self, test_audio_dir: str = "./test_audio") -> Dict:
        """🧪 Uruchomienie kompletnej ewaluacji"""
        
        logger.info("🚀 Starting comprehensive NVIDIA STT evaluation...")
        
        # 📁 Sprawdzenie dostępności plików testowych
        available_tests = []
        for audio_file, expected in self.test_cases.items():
            file_path = os.path.join(test_audio_dir, audio_file)
            if os.path.exists(file_path):
                available_tests.append((file_path, expected))
            else:
                logger.warning(f"⚠️ Test file not found: {file_path}")
        
        if not available_tests:
            logger.error("❌ No test audio files found!")
            return {}
        
        # 🎯 Wykonanie testów
        for file_path, expected in available_tests:
            try:
                await self.evaluate_transcription_quality(file_path, expected)
                await asyncio.sleep(1)  # Krótka przerwa między testami
            except Exception as e:
                logger.error(f"❌ Test failed for {file_path}: {e}")
        
        # 📊 Generowanie raportu
        return await self.generate_evaluation_report()
    
    async def generate_evaluation_report(self) -> Dict:
        """📊 Generowanie raportu ewaluacji"""
        
        if not self.results:
            return {"error": "No evaluation results available"}
        
        # 📈 Obliczenie statystyk
        word_accuracies = [r.word_accuracy for r in self.results]
        char_accuracies = [r.character_accuracy for r in self.results]
        hvac_accuracies = [r.hvac_terms_accuracy for r in self.results]
        processing_times = [r.processing_time for r in self.results]
        confidence_scores = [r.confidence_score for r in self.results]
        
        report = {
            "evaluation_summary": {
                "total_tests": len(self.results),
                "timestamp": datetime.now().isoformat(),
                "test_duration": sum(processing_times)
            },
            "accuracy_metrics": {
                "word_accuracy": {
                    "mean": statistics.mean(word_accuracies),
                    "median": statistics.median(word_accuracies),
                    "min": min(word_accuracies),
                    "max": max(word_accuracies),
                    "std": statistics.stdev(word_accuracies) if len(word_accuracies) > 1 else 0
                },
                "character_accuracy": {
                    "mean": statistics.mean(char_accuracies),
                    "median": statistics.median(char_accuracies),
                    "min": min(char_accuracies),
                    "max": max(char_accuracies)
                },
                "hvac_terms_accuracy": {
                    "mean": statistics.mean(hvac_accuracies),
                    "median": statistics.median(hvac_accuracies),
                    "min": min(hvac_accuracies),
                    "max": max(hvac_accuracies)
                }
            },
            "performance_metrics": {
                "average_processing_time": statistics.mean(processing_times),
                "average_confidence": statistics.mean(confidence_scores),
                "fastest_transcription": min(processing_times),
                "slowest_transcription": max(processing_times)
            },
            "detailed_results": [
                {
                    "file": r.audio_file,
                    "word_accuracy": r.word_accuracy,
                    "hvac_accuracy": r.hvac_terms_accuracy,
                    "processing_time": r.processing_time,
                    "confidence": r.confidence_score,
                    "model": r.model_used
                }
                for r in self.results
            ]
        }
        
        # 💾 Zapisanie raportu
        report_file = f"evaluation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        async with aiofiles.open(f"./evaluation_results/{report_file}", 'w', encoding='utf-8') as f:
            await f.write(json.dumps(report, indent=2, ensure_ascii=False))
        
        logger.info(f"📊 Evaluation report saved: {report_file}")
        return report
    
    def create_evaluation_visualizations(self, report: Dict):
        """📈 Tworzenie wizualizacji wyników ewaluacji"""
        
        if not self.results:
            return
        
        # 📊 Przygotowanie danych
        df = pd.DataFrame([
            {
                "File": os.path.basename(r.audio_file),
                "Word Accuracy": r.word_accuracy,
                "Character Accuracy": r.character_accuracy,
                "HVAC Accuracy": r.hvac_terms_accuracy,
                "Processing Time": r.processing_time,
                "Confidence": r.confidence_score
            }
            for r in self.results
        ])
        
        # 🎨 Tworzenie wykresów
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('NVIDIA STT Polish HVAC - Evaluation Results', fontsize=16)
        
        # Wykres 1: Accuracy comparison
        df[["Word Accuracy", "Character Accuracy", "HVAC Accuracy"]].plot(
            kind='bar', ax=axes[0,0], title='Accuracy Metrics by Test'
        )
        axes[0,0].set_ylabel('Accuracy (%)')
        axes[0,0].legend()
        
        # Wykres 2: Processing time
        df["Processing Time"].plot(kind='bar', ax=axes[0,1], title='Processing Time by Test', color='orange')
        axes[0,1].set_ylabel('Time (seconds)')
        
        # Wykres 3: Confidence vs Accuracy
        axes[1,0].scatter(df["Confidence"], df["Word Accuracy"], alpha=0.7)
        axes[1,0].set_xlabel('Confidence Score')
        axes[1,0].set_ylabel('Word Accuracy (%)')
        axes[1,0].set_title('Confidence vs Word Accuracy')
        
        # Wykres 4: HVAC terms accuracy
        df["HVAC Accuracy"].plot(kind='bar', ax=axes[1,1], title='HVAC Terms Accuracy', color='green')
        axes[1,1].set_ylabel('HVAC Accuracy (%)')
        
        plt.tight_layout()
        
        # 💾 Zapisanie wykresów
        chart_file = f"evaluation_charts_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        plt.savefig(f"./evaluation_results/{chart_file}", dpi=300, bbox_inches='tight')
        logger.info(f"📈 Evaluation charts saved: {chart_file}")
        
        plt.show()

# 🚀 Main execution
async def main():
    """🎯 Główna funkcja ewaluacji"""
    
    # 📁 Tworzenie katalogów
    os.makedirs("./evaluation_results", exist_ok=True)
    os.makedirs("./test_audio", exist_ok=True)
    
    evaluator = HVACSTTEvaluator()
    
    # 🧪 Uruchomienie ewaluacji
    report = await evaluator.run_comprehensive_evaluation()
    
    if report:
        print("\n🎯 NVIDIA STT EVALUATION COMPLETED!")
        print("=" * 50)
        print(f"📊 Total tests: {report['evaluation_summary']['total_tests']}")
        print(f"📈 Average word accuracy: {report['accuracy_metrics']['word_accuracy']['mean']:.2f}%")
        print(f"🔧 Average HVAC accuracy: {report['accuracy_metrics']['hvac_terms_accuracy']['mean']:.2f}%")
        print(f"⚡ Average processing time: {report['performance_metrics']['average_processing_time']:.2f}s")
        
        # 📈 Tworzenie wizualizacji
        evaluator.create_evaluation_visualizations(report)

if __name__ == "__main__":
    asyncio.run(main())
