#!/usr/bin/env python3
"""
📊 Quality Dashboard for NVIDIA STT Polish HVAC System
Real-time evaluation and monitoring dashboard
"""

import os
import asyncio
import logging
import json
from typing import Dict, List
from datetime import datetime, timedelta
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import aiohttp

# 🔧 Konfiguracja logowania
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QualityDashboard:
    """📊 Dashboard jakości systemu NVIDIA STT"""
    
    def __init__(self):
        self.stt_url = "http://localhost:8889"
        self.orchestrator_url = "http://localhost:9000"
        self.gemma_url = "http://*************:1234"
        
    def setup_dashboard(self):
        """🎨 Konfiguracja Streamlit dashboard"""
        
        st.set_page_config(
            page_title="🎤 NVIDIA STT Quality Dashboard",
            page_icon="🎤",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # 🎨 Custom CSS
        st.markdown("""
        <style>
        .main-header {
            font-size: 2.5rem;
            color: #1f77b4;
            text-align: center;
            margin-bottom: 2rem;
        }
        .metric-card {
            background-color: #f0f2f6;
            padding: 1rem;
            border-radius: 0.5rem;
            border-left: 4px solid #1f77b4;
        }
        .success-metric {
            border-left-color: #28a745;
        }
        .warning-metric {
            border-left-color: #ffc107;
        }
        .danger-metric {
            border-left-color: #dc3545;
        }
        </style>
        """, unsafe_allow_html=True)
    
    async def get_system_health(self) -> Dict:
        """🏥 Sprawdzenie stanu systemu"""
        
        health_status = {
            "nvidia_stt": {"status": "unknown", "details": {}},
            "orchestrator": {"status": "unknown", "details": {}},
            "gemma": {"status": "unknown", "details": {}}
        }
        
        # NVIDIA STT health
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.stt_url}/health", timeout=5) as response:
                    if response.status == 200:
                        health_status["nvidia_stt"]["status"] = "healthy"
                        health_status["nvidia_stt"]["details"] = await response.json()
                    else:
                        health_status["nvidia_stt"]["status"] = "unhealthy"
        except:
            health_status["nvidia_stt"]["status"] = "offline"
        
        # Orchestrator health
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.orchestrator_url}/health", timeout=5) as response:
                    if response.status == 200:
                        health_status["orchestrator"]["status"] = "healthy"
                        health_status["orchestrator"]["details"] = await response.json()
                    else:
                        health_status["orchestrator"]["status"] = "unhealthy"
        except:
            health_status["orchestrator"]["status"] = "offline"
        
        # Gemma health
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.gemma_url}/v1/models", timeout=5) as response:
                    if response.status == 200:
                        health_status["gemma"]["status"] = "healthy"
                        health_status["gemma"]["details"] = await response.json()
                    else:
                        health_status["gemma"]["status"] = "unhealthy"
        except:
            health_status["gemma"]["status"] = "offline"
        
        return health_status
    
    def load_evaluation_results(self) -> Dict:
        """📊 Wczytanie wyników ewaluacji"""
        
        results_dir = "./evaluation_results"
        if not os.path.exists(results_dir):
            return {}
        
        # Znajdź najnowszy raport ewaluacji
        evaluation_files = [f for f in os.listdir(results_dir) if f.startswith("evaluation_report_")]
        if not evaluation_files:
            return {}
        
        latest_file = sorted(evaluation_files)[-1]
        
        try:
            with open(os.path.join(results_dir, latest_file), 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {}
    
    def load_gemma_insights(self) -> Dict:
        """🧠 Wczytanie insights z Gemmy"""
        
        results_dir = "./evaluation_results"
        if not os.path.exists(results_dir):
            return {}
        
        # Znajdź najnowszy raport insights
        insights_files = [f for f in os.listdir(results_dir) if f.startswith("gemma_insights_report_")]
        if not insights_files:
            return {}
        
        latest_file = sorted(insights_files)[-1]
        
        try:
            with open(os.path.join(results_dir, latest_file), 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {}
    
    def render_system_health_section(self, health_status: Dict):
        """🏥 Sekcja stanu systemu"""
        
        st.markdown("## 🏥 System Health Status")
        
        col1, col2, col3 = st.columns(3)
        
        # NVIDIA STT Status
        with col1:
            status = health_status["nvidia_stt"]["status"]
            if status == "healthy":
                st.success("🎤 NVIDIA STT: Healthy")
                if health_status["nvidia_stt"]["details"]:
                    details = health_status["nvidia_stt"]["details"]
                    st.write(f"GPU Available: {details.get('gpu_available', 'Unknown')}")
                    st.write(f"Models: {details.get('models_loaded', [])}")
            elif status == "unhealthy":
                st.warning("🎤 NVIDIA STT: Unhealthy")
            else:
                st.error("🎤 NVIDIA STT: Offline")
        
        # Orchestrator Status
        with col2:
            status = health_status["orchestrator"]["status"]
            if status == "healthy":
                st.success("🎯 Orchestrator: Healthy")
            elif status == "unhealthy":
                st.warning("🎯 Orchestrator: Unhealthy")
            else:
                st.error("🎯 Orchestrator: Offline")
        
        # Gemma Status
        with col3:
            status = health_status["gemma"]["status"]
            if status == "healthy":
                st.success("🧠 Gemma: Healthy")
                if health_status["gemma"]["details"]:
                    models = health_status["gemma"]["details"].get("data", [])
                    st.write(f"Available Models: {len(models)}")
            elif status == "unhealthy":
                st.warning("🧠 Gemma: Unhealthy")
            else:
                st.error("🧠 Gemma: Offline")
    
    def render_evaluation_metrics(self, evaluation_data: Dict):
        """📊 Sekcja metryk ewaluacji"""
        
        if not evaluation_data:
            st.warning("📊 No evaluation data available. Run evaluation first.")
            return
        
        st.markdown("## 📊 Evaluation Metrics")
        
        # Key metrics
        col1, col2, col3, col4 = st.columns(4)
        
        accuracy_metrics = evaluation_data.get("accuracy_metrics", {})
        performance_metrics = evaluation_data.get("performance_metrics", {})
        
        with col1:
            word_acc = accuracy_metrics.get("word_accuracy", {}).get("mean", 0)
            st.metric("Word Accuracy", f"{word_acc:.1f}%", 
                     delta=f"{word_acc - 85:.1f}%" if word_acc > 0 else None)
        
        with col2:
            hvac_acc = accuracy_metrics.get("hvac_terms_accuracy", {}).get("mean", 0)
            st.metric("HVAC Terms Accuracy", f"{hvac_acc:.1f}%",
                     delta=f"{hvac_acc - 90:.1f}%" if hvac_acc > 0 else None)
        
        with col3:
            proc_time = performance_metrics.get("average_processing_time", 0)
            st.metric("Avg Processing Time", f"{proc_time:.2f}s",
                     delta=f"{30 - proc_time:.1f}s" if proc_time > 0 else None)
        
        with col4:
            confidence = performance_metrics.get("average_confidence", 0)
            st.metric("Avg Confidence", f"{confidence:.2f}",
                     delta=f"{confidence - 0.8:.2f}" if confidence > 0 else None)
        
        # Detailed results chart
        if "detailed_results" in evaluation_data:
            st.markdown("### 📈 Detailed Results")
            
            results_df = pd.DataFrame(evaluation_data["detailed_results"])
            
            # Accuracy comparison chart
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=("Word Accuracy", "HVAC Accuracy", "Processing Time", "Confidence"),
                specs=[[{"secondary_y": False}, {"secondary_y": False}],
                       [{"secondary_y": False}, {"secondary_y": False}]]
            )
            
            # Word accuracy
            fig.add_trace(
                go.Bar(x=results_df.index, y=results_df["word_accuracy"], name="Word Accuracy"),
                row=1, col=1
            )
            
            # HVAC accuracy
            fig.add_trace(
                go.Bar(x=results_df.index, y=results_df["hvac_accuracy"], name="HVAC Accuracy"),
                row=1, col=2
            )
            
            # Processing time
            fig.add_trace(
                go.Scatter(x=results_df.index, y=results_df["processing_time"], 
                          mode="lines+markers", name="Processing Time"),
                row=2, col=1
            )
            
            # Confidence
            fig.add_trace(
                go.Scatter(x=results_df.index, y=results_df["confidence"], 
                          mode="lines+markers", name="Confidence"),
                row=2, col=2
            )
            
            fig.update_layout(height=600, showlegend=False)
            st.plotly_chart(fig, use_container_width=True)
    
    def render_gemma_insights(self, insights_data: Dict):
        """🧠 Sekcja insights z Gemmy"""
        
        if not insights_data:
            st.warning("🧠 No Gemma insights available. Run deep analysis first.")
            return
        
        st.markdown("## 🧠 Gemma Deep Insights")
        
        # Summary metrics
        if "comprehensive_insights" in insights_data:
            insights = insights_data["comprehensive_insights"]
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("Total Analyses", insights.get("total_analyses", 0))
            
            with col2:
                avg_conf = insights.get("average_confidence", 0)
                st.metric("Avg Confidence", f"{avg_conf:.2f}")
            
            with col3:
                proc_time = insights.get("total_processing_time", 0)
                st.metric("Total Processing Time", f"{proc_time:.1f}s")
        
        # Recommendations
        if "consolidated_recommendations" in insights_data:
            st.markdown("### 📝 Key Recommendations")
            
            recommendations = insights_data["consolidated_recommendations"][:10]  # Top 10
            
            for i, rec in enumerate(recommendations, 1):
                st.markdown(f"**{i}.** {rec}")
        
        # Analysis summary
        if "analysis_summary" in insights_data:
            st.markdown("### 📊 Analysis Summary")
            
            summary_df = pd.DataFrame.from_dict(
                insights_data["analysis_summary"], 
                orient="index"
            )
            
            if not summary_df.empty:
                st.dataframe(summary_df, use_container_width=True)
    
    def render_action_center(self):
        """🎯 Centrum akcji"""
        
        st.markdown("## 🎯 Action Center")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🧪 Run STT Evaluation", type="primary"):
                st.info("Starting STT evaluation... (This would trigger the evaluation script)")
                # TODO: Integrate with evaluation script
        
        with col2:
            if st.button("🧠 Run Gemma Analysis", type="primary"):
                st.info("Starting Gemma deep analysis... (This would trigger the analysis)")
                # TODO: Integrate with Gemma analyzer
        
        with col3:
            if st.button("📊 Generate Report", type="primary"):
                st.info("Generating comprehensive report... (This would create a PDF report)")
                # TODO: Integrate with report generator
    
    async def run_dashboard(self):
        """🚀 Uruchomienie dashboard"""
        
        self.setup_dashboard()
        
        # Header
        st.markdown('<h1 class="main-header">🎤 NVIDIA STT Quality Dashboard</h1>', 
                   unsafe_allow_html=True)
        
        # Sidebar
        st.sidebar.markdown("## 🎛️ Dashboard Controls")
        auto_refresh = st.sidebar.checkbox("Auto Refresh", value=True)
        refresh_interval = st.sidebar.slider("Refresh Interval (seconds)", 10, 300, 60)
        
        if st.sidebar.button("🔄 Refresh Now"):
            st.experimental_rerun()
        
        # Main content
        try:
            # System health
            health_status = await self.get_system_health()
            self.render_system_health_section(health_status)
            
            st.markdown("---")
            
            # Evaluation metrics
            evaluation_data = self.load_evaluation_results()
            self.render_evaluation_metrics(evaluation_data)
            
            st.markdown("---")
            
            # Gemma insights
            insights_data = self.load_gemma_insights()
            self.render_gemma_insights(insights_data)
            
            st.markdown("---")
            
            # Action center
            self.render_action_center()
            
            # Auto refresh
            if auto_refresh:
                time.sleep(refresh_interval)
                st.experimental_rerun()
                
        except Exception as e:
            st.error(f"Dashboard error: {e}")

# 🚀 Main execution
def main():
    """📊 Główna funkcja dashboard"""
    
    dashboard = QualityDashboard()
    
    # Run async dashboard
    asyncio.run(dashboard.run_dashboard())

if __name__ == "__main__":
    main()
