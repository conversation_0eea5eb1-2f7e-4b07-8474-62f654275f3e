#!/bin/bash

echo "🚀 Uruchamianie Ollama server..."
# Uruchomienie serwera w tle
ollama serve &
SERVER_PID=$!

echo "⏳ Oczekiwanie na uruchomienie serwera..."
# Czekanie aż serwer będzie gotowy
for i in {1..30}; do
    if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
        echo "✅ Serwer Ollama gotowy!"
        break
    fi
    echo "Próba $i/30 - czekam na serwer..."
    sleep 2
done

echo "📥 Pobieranie modelu Gemma3 4B..."
ollama pull gemma3:4b

echo "✅ Model Gemma3 4B gotowy!"
echo "🌐 Serwer dostępny na porcie 11434"

# Utrzymanie kontenera - czekanie na proces serwera
wait $SERVER_PID