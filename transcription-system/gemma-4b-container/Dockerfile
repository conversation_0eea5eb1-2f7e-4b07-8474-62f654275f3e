FROM ollama/ollama:latest

# 🏠 Ustawienie katalogu roboczego
WORKDIR /app

# 📁 Utworzenie katalogów dla modeli
RUN mkdir -p /root/.ollama

# 🔧 Instalacja curl dla health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# 📋 Skrypt startowy
COPY start_gemma.sh /app/start_gemma.sh
RUN chmod +x /app/start_gemma.sh

# 🌐 Expose port
EXPOSE 11434

# 🚀 Uruchomienie - nadpisanie ENTRYPOINT
ENTRYPOINT []
CMD ["/bin/bash", "/app/start_gemma.sh"]