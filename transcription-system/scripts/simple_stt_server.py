#!/usr/bin/env python3
"""
🎤 Simple STT Server for Testing
Simplified transcription server for testing the pipeline without NVIDIA NeMo
"""

import os
import asyncio
import logging
import json
import time
from typing import Dict, List, Optional
from datetime import datetime
from pathlib import Path

from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn
import aiofiles

# 🔧 Konfiguracja logowania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/workspace/logs/stt_server.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 🎯 Modele danych
class TranscriptionResponse(BaseModel):
    transcript: str
    confidence: float
    processing_time: float
    language_detected: str
    hvac_keywords: Optional[List[str]] = None

class SimpleSTTServer:
    """🎤 Uproszczony serwer STT dla testów"""
    
    def __init__(self):
        self.app = FastAPI(
            title="🎤 Simple HVAC STT Server",
            description="Uproszczony serwer transkrypcji dla testów - HVAC CRM",
            version="1.0.0"
        )
        
        # 🔧 Konfiguracja CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 🧠 HVAC keywords dla testów
        self.hvac_keywords = [
            "klimatyzacja", "klimatyzator", "pompa ciepła", "wentylacja",
            "chłodzenie", "ogrzewanie", "serwis", "naprawa", "instalacja",
            "LG", "Daikin", "Samsung", "Mitsubishi", "Fujitsu"
        ]
        
        self._setup_routes()
        
    def _setup_routes(self):
        """🛣️ Konfiguracja endpointów API"""
        
        @self.app.get("/health")
        async def health_check():
            """🏥 Sprawdzenie stanu serwera"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "models_loaded": ["simple_mock"],
                "gpu_available": False,
                "gpu_count": 0,
                "mode": "testing"
            }
        
        @self.app.post("/transcribe", response_model=TranscriptionResponse)
        async def transcribe_audio(
            background_tasks: BackgroundTasks,
            audio_file: UploadFile = File(...),
            language: str = "pl",
            model_type: str = "fastconformer",
            hvac_context: bool = True
        ):
            """🎤 Główny endpoint transkrypcji (mock)"""
            return await self._mock_transcribe_audio_file(
                audio_file, language, hvac_context, background_tasks
            )
        
        @self.app.get("/models")
        async def get_available_models():
            """📋 Lista dostępnych modeli"""
            return {
                "available_models": ["simple_mock"],
                "default_model": "simple_mock",
                "supported_languages": ["pl", "en"],
                "hvac_keywords_count": len(self.hvac_keywords),
                "mode": "testing"
            }
    
    async def _mock_transcribe_audio_file(
        self, 
        audio_file: UploadFile, 
        language: str, 
        hvac_context: bool,
        background_tasks: BackgroundTasks
    ) -> TranscriptionResponse:
        """🎤 Mock transkrypcja dla testów"""
        
        start_time = time.time()
        
        try:
            # 💾 Zapisanie pliku tymczasowego
            temp_path = f"/workspace/audio_input/{audio_file.filename}"
            async with aiofiles.open(temp_path, 'wb') as f:
                content = await audio_file.read()
                await f.write(content)
            
            # 🎯 Mock transkrypcja
            mock_transcripts = [
                "Dzień dobry, mam problem z klimatyzacją LG. Urządzenie nie chłodzi prawidłowo.",
                "Potrzebuję serwisu pompy ciepła Daikin. Wydaje dziwne dźwięki.",
                "Klimatyzator Samsung przestał działać po burzy. Proszę o wizytę serwisową.",
                "Instalacja nowego systemu VRF w biurowcu. Kiedy możemy umówić termin?",
                "Wymiana filtrów w klimatyzacji. Jak często należy to robić?"
            ]
            
            # Wybór losowej transkrypcji na podstawie nazwy pliku
            transcript_index = hash(audio_file.filename) % len(mock_transcripts)
            transcript = mock_transcripts[transcript_index]
            
            # 🔍 Analiza HVAC keywords
            hvac_keywords_found = []
            if hvac_context:
                hvac_keywords_found = self._extract_hvac_keywords(transcript)
            
            # 📊 Mock confidence
            confidence = 0.85 + (hash(audio_file.filename) % 10) / 100  # 0.85-0.94
            
            processing_time = time.time() - start_time
            
            # 🗑️ Czyszczenie pliku tymczasowego w tle
            background_tasks.add_task(self._cleanup_temp_file, temp_path)
            
            logger.info(f"✅ Mock transkrypcja zakończona: {audio_file.filename} ({processing_time:.2f}s)")
            
            return TranscriptionResponse(
                transcript=transcript,
                confidence=round(confidence, 3),
                processing_time=processing_time,
                language_detected=language,
                hvac_keywords=hvac_keywords_found if hvac_context else None
            )
            
        except Exception as e:
            logger.error(f"❌ Błąd mock transkrypcji {audio_file.filename}: {e}")
            raise HTTPException(status_code=500, detail=f"Błąd transkrypcji: {str(e)}")
    
    def _extract_hvac_keywords(self, transcript: str) -> List[str]:
        """🔍 Wyodrębnienie słów kluczowych HVAC"""
        found_keywords = []
        transcript_lower = transcript.lower()
        
        for keyword in self.hvac_keywords:
            if keyword.lower() in transcript_lower:
                found_keywords.append(keyword)
        
        return found_keywords
    
    async def _cleanup_temp_file(self, file_path: str):
        """🗑️ Usunięcie pliku tymczasowego"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.debug(f"🗑️ Usunięto plik tymczasowy: {file_path}")
        except Exception as e:
            logger.warning(f"⚠️ Nie udało się usunąć pliku tymczasowego: {e}")

# 🚀 Uruchomienie serwera
if __name__ == "__main__":
    server = SimpleSTTServer()
    
    uvicorn.run(
        server.app,
        host="0.0.0.0",
        port=8889,
        log_level="info",
        access_log=True
    )
