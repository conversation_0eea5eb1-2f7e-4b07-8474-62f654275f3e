#!/bin/bash

# 🧪 Test systemu transkrypcji NVIDIA NeMo + Gemma3-4b

echo "🧪 TESTOWANIE SYSTEMU TRANSKRYPCJI"
echo "=================================="

# Test 1: Health check orchestratora
echo "1️⃣ Test Transcription Orchestrator..."
ORCHESTRATOR_HEALTH=$(curl -s http://localhost:9000/health | jq -r '.status' 2>/dev/null)
if [ "$ORCHESTRATOR_HEALTH" = "healthy" ]; then
    echo "✅ Orchestrator działa poprawnie"
else
    echo "❌ Orchestrator nie odpowiada"
fi

# Test 2: Health check NVIDIA STT
echo "2️⃣ Test NVIDIA STT..."
STT_HEALTH=$(curl -s http://localhost:8889/health | jq -r '.status' 2>/dev/null)
if [ "$STT_HEALTH" = "healthy" ]; then
    echo "✅ NVIDIA STT działa poprawnie"
else
    echo "❌ NVIDIA STT nie odpowiada"
fi

# Test 3: Test modelu Gemma3-4b
echo "3️⃣ Test Gemma3-4b..."
GEMMA_MODELS=$(curl -s http://localhost:11434/api/tags | jq -r '.models[0].name' 2>/dev/null)
if [ "$GEMMA_MODELS" = "gemma3:4b" ]; then
    echo "✅ Gemma3-4b model dostępny"
else
    echo "❌ Gemma3-4b model niedostępny"
fi

# Test 4: Test generowania przez Gemma
echo "4️⃣ Test generowania Gemma..."
GEMMA_RESPONSE=$(curl -s -X POST http://localhost:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{"model": "gemma3:4b", "prompt": "Odpowiedz krótko: Jak się masz?", "stream": false}' \
  | jq -r '.response' 2>/dev/null)

if [ ! -z "$GEMMA_RESPONSE" ] && [ "$GEMMA_RESPONSE" != "null" ]; then
    echo "✅ Gemma generuje odpowiedzi: ${GEMMA_RESPONSE:0:50}..."
else
    echo "❌ Gemma nie generuje odpowiedzi"
fi

# Test 5: Test Redis
echo "5️⃣ Test Redis..."
REDIS_PING=$(docker exec fulmark-redis redis-cli ping 2>/dev/null)
if [ "$REDIS_PING" = "PONG" ]; then
    echo "✅ Redis działa poprawnie"
else
    echo "❌ Redis nie odpowiada"
fi

# Test 6: Test MongoDB
echo "6️⃣ Test MongoDB..."
MONGO_STATUS=$(docker exec fulmark-mongodb mongosh --quiet --eval "db.runCommand('ping').ok" 2>/dev/null)
if [ "$MONGO_STATUS" = "1" ]; then
    echo "✅ MongoDB działa poprawnie"
else
    echo "❌ MongoDB nie odpowiada"
fi

# Test 7: Test Weaviate
echo "7️⃣ Test Weaviate..."
WEAVIATE_READY=$(curl -s http://localhost:8082/v1/.well-known/ready | jq -r '.ready' 2>/dev/null)
if [ "$WEAVIATE_READY" = "true" ]; then
    echo "✅ Weaviate działa poprawnie"
else
    echo "❌ Weaviate nie jest gotowy"
fi

echo ""
echo "📊 PODSUMOWANIE TESTÓW"
echo "======================"
echo "🎯 System transkrypcji NVIDIA NeMo + Gemma3-4b"
echo "🐳 Wszystkie kontenery: docker-compose ps"
echo "📜 Logi: docker-compose logs [service-name]"
echo "🔧 Restart: docker-compose restart [service-name]"