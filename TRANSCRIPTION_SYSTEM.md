# 🎯 SYSTEM TRANSKRYPCJI NVIDIA NEMO + GEMMA3-4B

## 🏗️ Architektura Systemu

System transkrypcji składa się z następujących komponentów:

### 🤖 Komponenty AI
- **NVIDIA NeMo STT** - Transkrypcja mowy na tekst (język polski)
- **Gemma3-4b** - <PERSON><PERSON>za semantyczna i generowanie insights
- **Weaviate** - <PERSON>za wektorowa dla embeddings
- **Transformers** - Generowanie embeddings

### 🔧 Serwisy
- **Transcription Orchestrator** - Orkiestrator całego pipeline
- **Audio Converter** - Konwersja formatów audio
- **Email Processor** - Przetwarzanie załączników email
- **Gemma Integration** - Integracja z modelem Gemma3-4b

### 🗄️ Infrastruktura
- **Redis** - Cache i kolejki zadań
- **MongoDB** - Przechowywanie metadanych
- **MinIO** - Przechowywanie plików audio

## 🚀 Uruchomienie

```bash
# Uruchomienie całego systemu
./start-fulmark-system.sh

# Lub ręcznie
docker-compose up -d
```

## 📊 Endpointy API

### Transcription Orchestrator (Port 9000)
- `GET /health` - Status systemu
- `POST /transcribe` - Transkrypcja pliku audio
- `GET /jobs/{job_id}` - Status zadania
- `GET /results/{job_id}` - Wyniki transkrypcji

### NVIDIA STT (Port 8889)
- `GET /health` - Status serwisu STT
- `POST /transcribe` - Bezpośrednia transkrypcja

### Gemma3-4b (Port 11434)
- `GET /api/tags` - Lista modeli
- `POST /api/generate` - Generowanie tekstu
- `POST /api/chat` - Chat API

## 🔄 Pipeline Transkrypcji

1. **Email → Załączniki M4A** (Email Processor)
2. **M4A → WAV** (Audio Converter)
3. **WAV → Tekst** (NVIDIA NeMo STT)
4. **Tekst → Analiza** (Gemma3-4b)
5. **Wyniki → CRM** (GoSpine Backend)

## 🧪 Testowanie

```bash
# Test całego pipeline
curl -X POST http://localhost:9000/transcribe \
  -F "audio=@test.m4a" \
  -F "language=pl"

# Test modelu Gemma
curl -X POST http://localhost:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{"model": "gemma3:4b", "prompt": "Przeanalizuj: Dzień dobry, chciałbym umówić serwis klimatyzacji.", "stream": false}'
```

## 📈 Monitoring

```bash
# Status wszystkich kontenerów
docker-compose ps

# Logi konkretnego serwisu
docker-compose logs gemma-integration

# Metryki Redis
docker exec fulmark-redis redis-cli info
```

## 🔧 Konfiguracja

Główne zmienne środowiskowe w `docker-compose.yml`:

- `MODEL_NAME=gemma3:4b` - Model Gemma
- `LANGUAGE=pl` - Język transkrypcji
- `MAX_TOKENS=2048` - Maksymalna długość odpowiedzi
- `TEMPERATURE=0.3` - Temperatura generowania

## 🚨 Rozwiązywanie Problemów

### Gemma3-4b nie startuje
```bash
# Sprawdź logi
docker logs fulmark-gemma-4b

# Restart kontenera
docker-compose restart gemma-4b
```

### NVIDIA STT w trybie testowym
- Brak GPU - działa w trybie mock
- Dla produkcji potrzebny NVIDIA GPU z CUDA

### Redis connection failed
```bash
# Sprawdź sieć
docker network inspect fulmark-erp-crm_fulmark-network

# Restart Redis
docker-compose restart redis
```

## 📋 Status Implementacji

✅ **UKOŃCZONE:**
- [x] Kontener Gemma3-4b z Ollama
- [x] Integracja NVIDIA NeMo STT
- [x] Pipeline transkrypcji
- [x] Redis connectivity
- [x] Docker Compose konfiguracja
- [x] Health checks
- [x] Dokumentacja

🔄 **W TRAKCIE:**
- [ ] Integracja z GoSpine backend
- [ ] Testy end-to-end
- [ ] Monitoring i metryki

🎯 **PLANOWANE:**
- [ ] GPU support dla NVIDIA STT
- [ ] Batch processing
- [ ] API rate limiting
- [ ] Backup i recovery