#!/usr/bin/env python3
"""
🔧 FULMARK HVAC CRM - SETUP ŚRODOWISKA WLEWU DANYCH
Skrypt do przygotowania środowiska dla wlewu danych

Wielki inżynierze! Ten skrypt:
- Instaluje wymagane pakiety Python
- Sprawdza połączenia z bazami danych
- Testuje LM Studio
- Przygotowuje środowisko
"""

import sys
import subprocess
import os
import requests
import json
from pathlib import Path

def print_header():
    """Wyświetla nagłówek"""
    print("🔧 FULMARK HVAC CRM - SETUP ŚRODOWISKA WLEWU DANYCH")
    print("=" * 60)

def check_python_version():
    """Sprawdza wersję Python"""
    print("🐍 Sprawdzanie wersji Python...")
    
    if sys.version_info < (3, 8):
        print(f"❌ Python {sys.version_info.major}.{sys.version_info.minor} - wymagany 3.8+")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_requirements():
    """Instaluje wymagane pakiety"""
    print("📦 Instalowanie wymaganych pakietów...")
    
    requirements = [
        'pandas>=1.5.0',
        'pymongo>=4.0.0',
        'requests>=2.28.0',
        'python-dotenv>=0.19.0',
        'asyncio',
        'pathlib'
    ]
    
    for package in requirements:
        try:
            print(f"   📥 Instalowanie {package}...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', package
            ], capture_output=True, text=True, check=True)
            print(f"   ✅ {package} zainstalowany")
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Błąd instalacji {package}: {e}")
            return False
    
    print("✅ Wszystkie pakiety zainstalowane!")
    return True

def test_mongodb_connection():
    """Testuje połączenie z MongoDB"""
    print("🗄️ Testowanie połączenia z MongoDB...")
    
    try:
        from pymongo import MongoClient
        
        # URL z konfiguracji
        mongodb_url = "mongodb+srv://xbow123:<EMAIL>/?retryWrites=true&w=majority&appName=hvac-db"
        
        client = MongoClient(mongodb_url, serverSelectionTimeoutMS=5000)
        
        # Test połączenia
        client.admin.command('ping')
        
        # Sprawdź bazy danych
        db_names = client.list_database_names()
        print(f"   ✅ Połączenie OK! Dostępne bazy: {', '.join(db_names)}")
        
        # Sprawdź kolekcje w hvac_crm
        if 'hvac_crm' in db_names:
            db = client['hvac_crm']
            collections = db.list_collection_names()
            print(f"   📋 Kolekcje w hvac_crm: {', '.join(collections) if collections else 'brak'}")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Błąd połączenia z MongoDB: {e}")
        return False

def test_lm_studio():
    """Testuje połączenie z LM Studio"""
    print("🤖 Testowanie połączenia z LM Studio...")
    
    lm_studio_url = "http://192.168.0.179:1234"
    
    try:
        # Test podstawowego połączenia
        response = requests.get(f"{lm_studio_url}/v1/models", timeout=5)
        
        if response.status_code == 200:
            models = response.json()
            if models.get('data'):
                model_names = [model.get('id', 'unknown') for model in models['data']]
                print(f"   ✅ LM Studio OK! Dostępne modele: {', '.join(model_names)}")
                return True
            else:
                print("   ⚠️ LM Studio odpowiada, ale brak załadowanych modeli")
                return False
        else:
            print(f"   ❌ LM Studio błąd HTTP: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Nie można połączyć z LM Studio")
        print("   💡 Sprawdź czy LM Studio działa na http://192.168.0.179:1234")
        return False
    except Exception as e:
        print(f"   ❌ Błąd LM Studio: {e}")
        return False

def check_data_files():
    """Sprawdza dostępność plików CSV"""
    print("📁 Sprawdzanie plików CSV...")
    
    data_folder = "/home/<USER>/HVAC/unifikacja/Data_to_ingest"
    
    if not os.path.exists(data_folder):
        print(f"   ❌ Folder nie istnieje: {data_folder}")
        return False
    
    required_files = [
        'Kartoteka kontrahentów_extracted.csv',
        'clients_export.csv', 
        'calendar_archive.csv'
    ]
    
    found_files = 0
    total_size = 0
    
    for filename in required_files:
        filepath = os.path.join(data_folder, filename)
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            total_size += size
            found_files += 1
            print(f"   ✅ {filename} ({size:,} bytes)")
        else:
            print(f"   ❌ {filename} - nie znaleziono")
    
    print(f"   📊 Znaleziono {found_files}/{len(required_files)} plików ({total_size:,} bytes)")
    
    return found_files == len(required_files)

def create_test_script():
    """Tworzy skrypt testowy"""
    print("🧪 Tworzenie skryptu testowego...")
    
    test_script = """#!/usr/bin/env python3
# Test podstawowych funkcji wlewu danych

import asyncio
from data_ingestion_master import DataIngestionMaster, IngestionConfig

async def test_basic_functionality():
    print("🧪 Test podstawowych funkcji...")
    
    config = IngestionConfig()
    config.dry_run = True  # Tylko test, bez zapisywania
    config.enable_ai_enhancement = False  # Szybszy test
    
    master = DataIngestionMaster(config)
    
    try:
        await master.initialize()
        print("✅ Inicjalizacja OK")
        
        await master.analyze_csv_files()
        print("✅ Analiza CSV OK")
        
        print("🎯 Test zakończony sukcesem!")
        return True
        
    except Exception as e:
        print(f"❌ Test nieudany: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_basic_functionality())
    exit(0 if success else 1)
"""
    
    with open('test_ingestion.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("   ✅ Utworzono test_ingestion.py")

def print_usage_instructions():
    """Wyświetla instrukcje użycia"""
    print("\n🚀 INSTRUKCJE UŻYCIA:")
    print("=" * 40)
    print()
    print("1️⃣ Test środowiska:")
    print("   python test_ingestion.py")
    print()
    print("2️⃣ Analiza plików CSV:")
    print("   python run_data_ingestion.py --analyze-only")
    print()
    print("3️⃣ Test wlewu (bez zapisywania):")
    print("   python run_data_ingestion.py --dry-run")
    print()
    print("4️⃣ Szybki wlew (bez AI):")
    print("   python run_data_ingestion.py --no-ai")
    print()
    print("5️⃣ Pełny wlew z AI:")
    print("   python run_data_ingestion.py --full")
    print()
    print("🎯 Cel: 2137 punktów na skali jakości!")

def main():
    """Główna funkcja setup"""
    print_header()
    
    success_count = 0
    total_checks = 5
    
    # Sprawdzenia
    if check_python_version():
        success_count += 1
    
    if install_requirements():
        success_count += 1
    
    if test_mongodb_connection():
        success_count += 1
    
    if test_lm_studio():
        success_count += 1
    else:
        print("   💡 LM Studio jest opcjonalne - można uruchomić z --no-ai")
    
    if check_data_files():
        success_count += 1
    
    # Utwórz skrypt testowy
    create_test_script()
    
    # Podsumowanie
    print("\n📊 PODSUMOWANIE SETUP:")
    print("=" * 30)
    print(f"✅ Udane sprawdzenia: {success_count}/{total_checks}")
    
    if success_count >= 4:  # LM Studio jest opcjonalne
        print("🎉 ŚRODOWISKO GOTOWE DO WLEWU DANYCH!")
        print_usage_instructions()
    else:
        print("⚠️ Niektóre sprawdzenia nieudane - sprawdź błędy powyżej")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
