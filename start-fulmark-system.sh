#!/bin/bash

# 🚀 FULMARK ERP-CRM - Skrypt startowy
# System transkrypcji NVIDIA NeMo + Gemma3-4b + Email Intelligence

echo "🏗️ FULMARK ERP-CRM - Uruchamianie systemu..."
echo "================================================"

# Sprawdzenie czy Docker działa
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker nie jest uruchomiony!"
    exit 1
fi

# Sprawdzenie czy docker-compose jest dostępny
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose nie jest zainstalowany!"
    exit 1
fi

echo "✅ Docker i docker-compose są dostępne"

# Tworzenie katalogów jeśli nie istnieją
echo "📁 Tworzenie katalogów..."
mkdir -p transcription-system/{logs,transcriptions,analysis_results,audio_input,audio_converted,email_attachments}
mkdir -p email_intelligence/{logs,temp,attachments}

echo "🔧 Budowanie kontenerów..."
docker-compose build

echo "🚀 Uruchamianie systemu..."
docker-compose up -d

echo "⏳ Oczekiwanie na uruchomienie serwisów..."
sleep 30

echo "🔍 Sprawdzanie statusu serwisów..."
docker-compose ps

echo ""
echo "🎯 SYSTEM GOTOWY!"
echo "================================================"
echo "📊 Transcription Orchestrator: http://localhost:9000"
echo "📧 Email Intelligence:         http://localhost:8001"
echo "🤖 Gemma3-4b Model:           http://localhost:11434"
echo "🎤 NVIDIA STT:                http://localhost:8889"
echo "🗄️ Weaviate:                  http://localhost:8082"
echo "🔴 Redis:                     localhost:6379"
echo "🍃 MongoDB:                   localhost:27017"
echo ""
echo "📋 Sprawdź status: docker-compose ps"
echo "📜 Logi: docker-compose logs [service-name]"
echo "🛑 Stop: docker-compose down"
echo "================================================"