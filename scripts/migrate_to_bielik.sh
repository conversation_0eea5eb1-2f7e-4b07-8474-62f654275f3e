#!/bin/bash

# 🚀 COMPLETE BIELIK V3 4.5B MIGRATION SCRIPT
# Automated migration from Gemma to Bielik V3 4.5B for HVAC CRM

set -e

# Make script executable
chmod +x "$0"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
OLLAMA_HOST="*************"
OLLAMA_PORT="11434"
BIELIK_MODEL="SpeakLeash/bielik-4.5b-v3.0-instruct:Q8_0"
BACKEND_DIR="../backend"
FRONTEND_DIR="../frontend"

echo -e "${PURPLE}🇵🇱 BIELIK V3 4.5B MIGRATION - HVAC CRM POLISH AI REVOLUTION${NC}"
echo "=================================================================="
echo ""

# Function to print colored status
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Sprawdzanie wymagań..."
    
    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        print_error "jq nie jest zainstalowane. Instaluję..."
        sudo apt-get update && sudo apt-get install -y jq
    fi
    
    # Check if curl is installed
    if ! command -v curl &> /dev/null; then
        print_error "curl nie jest zainstalowane. Instaluję..."
        sudo apt-get update && sudo apt-get install -y curl
    fi
    
    # Check SSH access to Ollama host
    if ! ssh -o ConnectTimeout=5 -o BatchMode=yes koldbringer@$OLLAMA_HOST exit 2>/dev/null; then
        print_warning "Brak dostępu SSH do $OLLAMA_HOST. Sprawdź konfigurację SSH."
        return 1
    fi
    
    print_success "Wszystkie wymagania spełnione"
    return 0
}

# Function to backup current configuration
backup_config() {
    print_status "Tworzenie kopii zapasowej konfiguracji..."
    
    local backup_dir="./backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Backup .env file
    if [ -f "$BACKEND_DIR/.env" ]; then
        cp "$BACKEND_DIR/.env" "$backup_dir/.env.backup"
        print_success "Kopia zapasowa .env utworzona"
    fi
    
    # Backup existing AI services
    if [ -d "$BACKEND_DIR/services" ]; then
        cp -r "$BACKEND_DIR/services" "$backup_dir/services_backup"
        print_success "Kopia zapasowa serwisów AI utworzona"
    fi
    
    echo "$backup_dir" > .last_backup_path
    print_success "Kopia zapasowa utworzona w: $backup_dir"
}

# Function to install Bielik on remote server
install_bielik_remote() {
    print_status "Instalowanie Bielik V3 4.5B na serwerze $OLLAMA_HOST..."
    
    ssh koldbringer@$OLLAMA_HOST << EOF
        echo "🔄 Sprawdzanie Ollama..."
        if ! command -v ollama &> /dev/null; then
            echo "❌ Ollama nie jest zainstalowane!"
            exit 1
        fi
        
        echo "📥 Pobieranie modelu Bielik V3 4.5B..."
        ollama pull $BIELIK_MODEL
        
        echo "✅ Model Bielik zainstalowany pomyślnie!"
        
        echo "🧪 Test podstawowy..."
        echo "Testuj klimatyzację" | ollama run $BIELIK_MODEL --verbose
        
        echo "📊 Lista zainstalowanych modeli:"
        ollama list
EOF
    
    if [ $? -eq 0 ]; then
        print_success "Bielik V3 4.5B zainstalowany pomyślnie"
    else
        print_error "Instalacja Bielik nie powiodła się"
        return 1
    fi
}

# Function to test Ollama connectivity
test_ollama_connection() {
    print_status "Testowanie połączenia z Ollama..."
    
    local response=$(curl -s -w "%{http_code}" -o /dev/null "http://$OLLAMA_HOST:$OLLAMA_PORT/api/tags")
    
    if [ "$response" = "200" ]; then
        print_success "Połączenie z Ollama działa"
        return 0
    else
        print_error "Brak połączenia z Ollama ($OLLAMA_HOST:$OLLAMA_PORT)"
        return 1
    fi
}

# Function to test Bielik model
test_bielik_model() {
    print_status "Testowanie modelu Bielik V3 4.5B..."
    
    local test_prompt="Przeanalizuj: klimatyzacja nie chłodzi"
    local response=$(curl -s -X POST "http://$OLLAMA_HOST:$OLLAMA_PORT/api/generate" \
        -H "Content-Type: application/json" \
        -d "{
            \"model\": \"$BIELIK_MODEL\",
            \"prompt\": \"$test_prompt\",
            \"stream\": false,
            \"options\": {
                \"temperature\": 0.7,
                \"max_tokens\": 200
            }
        }")
    
    if echo "$response" | jq -e '.response' > /dev/null 2>&1; then
        print_success "Test Bielik zakończony sukcesem"
        echo "Przykładowa odpowiedź:"
        echo "$response" | jq -r '.response' | head -3
        return 0
    else
        print_error "Test Bielik nie powiódł się"
        echo "Odpowiedź: $response"
        return 1
    fi
}

# Function to update backend configuration
update_backend_config() {
    print_status "Aktualizowanie konfiguracji backend..."
    
    # Create necessary directories
    mkdir -p "$BACKEND_DIR/config"
    mkdir -p "$BACKEND_DIR/services"
    mkdir -p "$BACKEND_DIR/routes"
    
    # Copy new files
    if [ -f "./install_bielik.sh" ]; then
        chmod +x ./install_bielik.sh
        print_success "Skrypt instalacyjny Bielik przygotowany"
    fi
    
    print_success "Konfiguracja backend zaktualizowana"
}

# Function to install backend dependencies
install_backend_dependencies() {
    print_status "Instalowanie zależności backend..."
    
    cd "$BACKEND_DIR"
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        print_warning "package.json nie znaleziony w $BACKEND_DIR"
        return 1
    fi
    
    # Install dependencies
    npm install axios
    
    print_success "Zależności backend zainstalowane"
    cd - > /dev/null
}

# Function to restart backend service
restart_backend() {
    print_status "Restartowanie serwisu backend..."
    
    # Kill existing backend process
    pkill -f "node.*server" || true
    sleep 2
    
    # Start backend in background
    cd "$BACKEND_DIR"
    nohup npm start > backend.log 2>&1 &
    local backend_pid=$!
    
    # Wait a moment and check if it's running
    sleep 3
    if kill -0 $backend_pid 2>/dev/null; then
        print_success "Backend uruchomiony (PID: $backend_pid)"
        echo $backend_pid > .backend_pid
    else
        print_error "Nie udało się uruchomić backend"
        return 1
    fi
    
    cd - > /dev/null
}

# Function to test backend AI endpoints
test_backend_endpoints() {
    print_status "Testowanie endpointów AI..."
    
    # Wait for backend to be ready
    sleep 5
    
    # Test health endpoint
    local health_response=$(curl -s "http://localhost:5000/api/ai/health")
    if echo "$health_response" | jq -e '.success' > /dev/null 2>&1; then
        print_success "Endpoint /api/ai/health działa"
    else
        print_warning "Endpoint /api/ai/health nie odpowiada poprawnie"
    fi
    
    # Test Bielik analysis
    local analysis_response=$(curl -s -X POST "http://localhost:5000/api/ai/analyze" \
        -H "Content-Type: application/json" \
        -d '{"text": "Klimatyzacja hałasuje i nie chłodzi", "type": "issue_classification"}')
    
    if echo "$analysis_response" | jq -e '.success' > /dev/null 2>&1; then
        print_success "Endpoint analizy AI działa"
        echo "Model użyty: $(echo "$analysis_response" | jq -r '.data.model_used')"
    else
        print_warning "Endpoint analizy AI nie działa poprawnie"
    fi
}

# Function to create monitoring dashboard
setup_monitoring() {
    print_status "Konfigurowanie monitoringu..."
    
    # Create monitoring script
    cat > monitor_bielik.sh << 'EOF'
#!/bin/bash
# Bielik V3 4.5B Monitoring Script

echo "🇵🇱 BIELIK V3 4.5B MONITORING"
echo "=============================="

# Check Ollama service
echo "📡 Ollama Status:"
curl -s http://*************:11434/api/tags | jq '.models[] | select(.name | contains("bielik")) | {name, size, modified_at}'

# Check backend AI health
echo ""
echo "🧠 AI Health:"
curl -s http://localhost:5000/api/ai/health | jq '.data.overall_status, .data.models'

# Check AI statistics
echo ""
echo "📊 AI Statistics:"
curl -s http://localhost:5000/api/ai/stats | jq '.data | {requests, successes, failures, avg_response_time}'

echo ""
echo "✅ Monitoring completed at $(date)"
EOF
    
    chmod +x monitor_bielik.sh
    print_success "Skrypt monitoringu utworzony: monitor_bielik.sh"
}

# Function to rollback if something goes wrong
rollback() {
    print_warning "Wykonywanie rollback..."
    
    if [ -f ".last_backup_path" ]; then
        local backup_path=$(cat .last_backup_path)
        if [ -d "$backup_path" ]; then
            # Restore .env
            if [ -f "$backup_path/.env.backup" ]; then
                cp "$backup_path/.env.backup" "$BACKEND_DIR/.env"
                print_success "Przywrócono .env"
            fi
            
            # Restore services
            if [ -d "$backup_path/services_backup" ]; then
                rm -rf "$BACKEND_DIR/services"
                cp -r "$backup_path/services_backup" "$BACKEND_DIR/services"
                print_success "Przywrócono serwisy"
            fi
        fi
    fi
    
    print_success "Rollback zakończony"
}

# Main migration function
main() {
    echo -e "${BLUE}🚀 Rozpoczynanie migracji na Bielik V3 4.5B...${NC}"
    echo ""
    
    # Phase 1: Prerequisites and backup
    echo -e "${PURPLE}📋 FAZA 1: Przygotowanie${NC}"
    if ! check_prerequisites; then
        print_error "Sprawdzenie wymagań nie powiodło się"
        exit 1
    fi
    
    backup_config
    echo ""
    
    # Phase 2: Install Bielik
    echo -e "${PURPLE}📥 FAZA 2: Instalacja Bielik${NC}"
    if ! install_bielik_remote; then
        print_error "Instalacja Bielik nie powiodła się"
        rollback
        exit 1
    fi
    
    if ! test_ollama_connection; then
        print_error "Test połączenia Ollama nie powiódł się"
        rollback
        exit 1
    fi
    
    if ! test_bielik_model; then
        print_error "Test modelu Bielik nie powiódł się"
        rollback
        exit 1
    fi
    echo ""
    
    # Phase 3: Backend integration
    echo -e "${PURPLE}🔧 FAZA 3: Integracja Backend${NC}"
    update_backend_config
    install_backend_dependencies
    
    if ! restart_backend; then
        print_error "Restart backend nie powiódł się"
        rollback
        exit 1
    fi
    echo ""
    
    # Phase 4: Testing and monitoring
    echo -e "${PURPLE}🧪 FAZA 4: Testowanie i Monitoring${NC}"
    test_backend_endpoints
    setup_monitoring
    echo ""
    
    # Success summary
    echo -e "${GREEN}🎆 MIGRACJA ZAKOŃCZONA SUKCESEM!${NC}"
    echo "=================================="
    echo ""
    echo -e "${GREEN}✅ Bielik V3 4.5B jest gotowy do pracy!${NC}"
    echo ""
    echo "📊 Dostępne narzędzia:"
    echo "   - ./monitor_bielik.sh - monitoring systemu"
    echo "   - http://localhost:3000/bielik-dashboard - dashboard React"
    echo "   - http://localhost:5000/api/ai/health - status API"
    echo ""
    echo "🔧 Konfiguracja:"
    echo "   - Model: $BIELIK_MODEL"
    echo "   - URL: http://$OLLAMA_HOST:$OLLAMA_PORT"
    echo "   - Priorytet: Bielik → OpenAI → LM Studio"
    echo ""
    echo -e "${BLUE}🇵🇱 Polski AI dla HVAC jest gotowy do rewolucji!${NC}"
}

# Handle script interruption
trap 'print_error "Migracja przerwana"; rollback; exit 1' INT TERM

# Run main function
main "$@"
