#!/bin/bash

# 🐳 DOCKER BIELIK V3 4.5B SETUP
# Local Docker-based installation for HVAC CRM Polish AI

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${PURPLE}🐳 DOCKER BIELIK V3 4.5B SETUP - LOKALNA INSTALACJA${NC}"
echo "=================================================================="
echo ""

# Configuration
OLLAMA_PORT="11434"
BIELIK_MODEL="SpeakLeash/bielik-4.5b-v3.0-instruct:Q8_0"
CONTAINER_NAME="ollama-bielik"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check Docker
check_docker() {
    print_status "Sprawdzanie Docker..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker nie jest zainstalowany!"
        return 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker nie jest uruchomiony lub brak uprawnień!"
        print_status "Spróbuj: sudo systemctl start docker"
        return 1
    fi
    
    print_success "Docker jest gotowy"
    return 0
}

# Function to setup Ollama container
setup_ollama_container() {
    print_status "Konfigurowanie kontenera Ollama..."
    
    # Stop existing container if running
    if docker ps -q -f name=$CONTAINER_NAME | grep -q .; then
        print_status "Zatrzymywanie istniejącego kontenera..."
        docker stop $CONTAINER_NAME
    fi
    
    # Remove existing container
    if docker ps -aq -f name=$CONTAINER_NAME | grep -q .; then
        print_status "Usuwanie istniejącego kontenera..."
        docker rm $CONTAINER_NAME
    fi
    
    # Create data volume
    print_status "Tworzenie wolumenu danych..."
    docker volume create ollama-data || true
    
    # Run Ollama container
    print_status "Uruchamianie kontenera Ollama..."
    docker run -d \
        --name $CONTAINER_NAME \
        -p $OLLAMA_PORT:11434 \
        -v ollama-data:/root/.ollama \
        --restart unless-stopped \
        ollama/ollama
    
    # Wait for container to be ready
    print_status "Oczekiwanie na gotowość kontenera..."
    sleep 10
    
    # Check if container is running
    if ! docker ps -q -f name=$CONTAINER_NAME | grep -q .; then
        print_error "Kontener Ollama nie uruchomił się poprawnie"
        docker logs $CONTAINER_NAME
        return 1
    fi
    
    print_success "Kontener Ollama uruchomiony pomyślnie"
    return 0
}

# Function to install Bielik model
install_bielik_model() {
    print_status "Instalowanie modelu Bielik V3 4.5B..."
    print_warning "To może potrwać 5-10 minut (pobieranie ~4.5GB)..."
    
    # Pull Bielik model
    docker exec $CONTAINER_NAME ollama pull $BIELIK_MODEL
    
    if [ $? -eq 0 ]; then
        print_success "Model Bielik zainstalowany pomyślnie!"
    else
        print_error "Instalacja modelu Bielik nie powiodła się"
        return 1
    fi
    
    # List installed models
    print_status "Lista zainstalowanych modeli:"
    docker exec $CONTAINER_NAME ollama list
    
    return 0
}

# Function to test Bielik model
test_bielik_model() {
    print_status "Testowanie modelu Bielik V3 4.5B..."
    
    local test_prompt="Przeanalizuj następujące zgłoszenie HVAC: klimatyzacja nie chłodzi i hałasuje"
    
    # Test via API
    local response=$(curl -s -X POST "http://localhost:$OLLAMA_PORT/api/generate" \
        -H "Content-Type: application/json" \
        -d "{
            \"model\": \"$BIELIK_MODEL\",
            \"prompt\": \"$test_prompt\",
            \"stream\": false,
            \"options\": {
                \"temperature\": 0.7,
                \"max_tokens\": 200
            }
        }")
    
    if echo "$response" | jq -e '.response' > /dev/null 2>&1; then
        print_success "Test Bielik zakończony sukcesem!"
        echo ""
        echo -e "${GREEN}📝 Przykładowa odpowiedź:${NC}"
        echo "$response" | jq -r '.response' | head -5
        echo "..."
        return 0
    else
        print_error "Test Bielik nie powiódł się"
        echo "Odpowiedź API: $response"
        return 1
    fi
}

# Function to update backend configuration
update_backend_config() {
    print_status "Aktualizowanie konfiguracji backend..."
    
    # Update .env file
    local env_file="../backend/.env"
    
    if [ -f "$env_file" ]; then
        # Backup original
        cp "$env_file" "${env_file}.backup.$(date +%Y%m%d_%H%M%S)"
        
        # Update Bielik configuration
        sed -i 's|BIELIK_URL=.*|BIELIK_URL=http://localhost:11434|g' "$env_file"
        sed -i 's|BIELIK_ENABLED=.*|BIELIK_ENABLED=true|g' "$env_file"
        
        print_success "Konfiguracja backend zaktualizowana"
    else
        print_warning "Plik .env nie znaleziony w $env_file"
    fi
}

# Function to create monitoring script
create_monitoring_script() {
    print_status "Tworzenie skryptu monitoringu..."
    
    cat > monitor_docker_bielik.sh << 'EOF'
#!/bin/bash

echo "🐳 DOCKER BIELIK V3 4.5B MONITORING"
echo "===================================="

# Check Docker container
echo "📦 Status kontenera:"
docker ps --filter name=ollama-bielik --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "💾 Użycie zasobów:"
docker stats ollama-bielik --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

echo ""
echo "📊 Zainstalowane modele:"
docker exec ollama-bielik ollama list

echo ""
echo "🔍 Test API:"
curl -s http://localhost:11434/api/tags | jq '.models[] | select(.name | contains("bielik")) | {name, size, modified_at}'

echo ""
echo "✅ Monitoring zakończony: $(date)"
EOF
    
    chmod +x monitor_docker_bielik.sh
    print_success "Skrypt monitoringu utworzony: monitor_docker_bielik.sh"
}

# Function to create Docker Compose file
create_docker_compose() {
    print_status "Tworzenie Docker Compose..."
    
    cat > docker-compose.bielik.yml << 'EOF'
version: '3.8'

services:
  ollama:
    image: ollama/ollama
    container_name: ollama-bielik
    ports:
      - "11434:11434"
    volumes:
      - ollama-data:/root/.ollama
    restart: unless-stopped
    environment:
      - OLLAMA_HOST=0.0.0.0
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  ollama-data:
    driver: local
EOF
    
    print_success "Docker Compose utworzony: docker-compose.bielik.yml"
}

# Main function
main() {
    echo -e "${BLUE}🚀 Rozpoczynanie lokalnej instalacji Bielik V3 4.5B...${NC}"
    echo ""
    
    # Check prerequisites
    if ! check_docker; then
        print_error "Docker nie jest gotowy"
        exit 1
    fi
    
    # Setup Ollama container
    if ! setup_ollama_container; then
        print_error "Konfiguracja kontenera nie powiodła się"
        exit 1
    fi
    
    # Install Bielik model
    echo ""
    read -p "📥 Zainstalować model Bielik V3 4.5B (~4.5GB)? (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if ! install_bielik_model; then
            print_error "Instalacja modelu nie powiodła się"
            exit 1
        fi
    else
        print_warning "Pominięto instalację modelu"
    fi
    
    # Test model
    echo ""
    if test_bielik_model; then
        print_success "Model Bielik działa poprawnie!"
    else
        print_warning "Test modelu nie powiódł się, ale kontynuujemy..."
    fi
    
    # Update configuration
    echo ""
    update_backend_config
    create_monitoring_script
    create_docker_compose
    
    # Success summary
    echo ""
    echo -e "${GREEN}🎆 INSTALACJA DOCKER BIELIK ZAKOŃCZONA SUKCESEM!${NC}"
    echo "=================================================="
    echo ""
    echo -e "${GREEN}✅ Ollama działa w kontenerze Docker${NC}"
    echo -e "${GREEN}✅ Model Bielik V3 4.5B gotowy do użycia${NC}"
    echo -e "${GREEN}✅ Konfiguracja backend zaktualizowana${NC}"
    echo ""
    echo "🔧 Dostępne narzędzia:"
    echo "   - ./monitor_docker_bielik.sh - monitoring systemu"
    echo "   - docker-compose -f docker-compose.bielik.yml up -d - zarządzanie kontenerem"
    echo "   - http://localhost:11434/api/tags - API Ollama"
    echo ""
    echo "🎯 Następne kroki:"
    echo "   1. Uruchom backend: cd ../backend && npm start"
    echo "   2. Test API: curl http://localhost:5000/api/ai/bielik/health"
    echo "   3. Dashboard: http://localhost:3000/bielik-dashboard"
    echo ""
    echo -e "${BLUE}🇵🇱 Bielik V3 4.5B gotowy do polskiej rewolucji HVAC!${NC}"
}

# Handle script interruption
trap 'print_error "Instalacja przerwana"; exit 1' INT TERM

# Run main function
main "$@"
