#!/bin/bash

# 🚀 BIELIK V3 4.5B INSTALLATION SCRIPT
# Installs and configures Bielik V3 4.5B for HVAC CRM Polish language processing

set -e

echo "🇵🇱 BIELIK V3 4.5B INSTALLATION - HVAC CRM POLISH AI REVOLUTION"
echo "=================================================================="

# Configuration
OLLAMA_HOST="*************"
OLLAMA_PORT="11434"
BIELIK_MODEL="SpeakLeash/bielik-4.5b-v3.0-instruct:Q8_0"

echo "📋 Configuration:"
echo "   Host: $OLLAMA_HOST:$OLLAMA_PORT"
echo "   Model: $BIELIK_MODEL"
echo ""

# Function to check if Ollama is running
check_ollama() {
    echo "🔍 Checking Ollama service..."
    if curl -s "http://$OLLAMA_HOST:$OLLAMA_PORT/api/tags" > /dev/null 2>&1; then
        echo "✅ Ollama is running on $OLLAMA_HOST:$OLLAMA_PORT"
        return 0
    else
        echo "❌ Ollama is not accessible on $OLLAMA_HOST:$OLLAMA_PORT"
        return 1
    fi
}

# Function to install Bielik model
install_bielik() {
    echo "📥 Installing Bielik V3 4.5B model..."
    echo "   This may take 5-10 minutes depending on connection speed..."
    
    # Use SSH to connect to the remote server and install the model
    ssh -o ConnectTimeout=10 koldbringer@$OLLAMA_HOST << EOF
        echo "🔄 Pulling Bielik model on remote server..."
        ollama pull $BIELIK_MODEL
        echo "✅ Bielik model installed successfully!"
EOF
}

# Function to test Bielik model
test_bielik() {
    echo "🧪 Testing Bielik V3 4.5B with HVAC Polish prompt..."
    
    local test_prompt="Przeanalizuj następujące zgłoszenie serwisowe HVAC: 'Klimatyzacja w biurze nie chłodzi, robi dziwne dźwięki i kapie woda. Pilne.' Określ priorytet, typ problemu i sugerowane działania."
    
    local response=$(curl -s -X POST "http://$OLLAMA_HOST:$OLLAMA_PORT/api/generate" \
        -H "Content-Type: application/json" \
        -d "{
            \"model\": \"$BIELIK_MODEL\",
            \"prompt\": \"$test_prompt\",
            \"stream\": false,
            \"options\": {
                \"temperature\": 0.7,
                \"top_p\": 0.9,
                \"max_tokens\": 500
            }
        }")
    
    if echo "$response" | jq -e '.response' > /dev/null 2>&1; then
        echo "✅ Bielik test successful!"
        echo "📝 Sample response:"
        echo "$response" | jq -r '.response' | head -3
        echo "..."
        return 0
    else
        echo "❌ Bielik test failed!"
        echo "Response: $response"
        return 1
    fi
}

# Function to create Bielik service configuration
create_service_config() {
    echo "⚙️ Creating Bielik service configuration..."
    
    cat > ../backend/config/bielik.json << EOF
{
    "model": "$BIELIK_MODEL",
    "url": "http://$OLLAMA_HOST:$OLLAMA_PORT",
    "options": {
        "temperature": 0.7,
        "top_p": 0.9,
        "max_tokens": 1000,
        "stop": ["Human:", "Assistant:"]
    },
    "hvac_prompts": {
        "email_analysis": "Przeanalizuj następujący email HVAC i wyciągnij kluczowe informacje:",
        "transcription_analysis": "Przeanalizuj następującą transkrypcję rozmowy serwisowej HVAC:",
        "issue_classification": "Sklasyfikuj następujący problem HVAC według priorytetu i typu:",
        "response_generation": "Wygeneruj profesjonalną odpowiedź na następujące zgłoszenie HVAC:"
    },
    "fallback": {
        "enabled": true,
        "primary": "openai",
        "secondary": "lm_studio"
    }
}
EOF
    
    echo "✅ Bielik configuration created at backend/config/bielik.json"
}

# Function to update backend AI service
update_ai_service() {
    echo "🔧 Creating Bielik AI service integration..."
    
    cat > ../backend/services/bielikService.js << 'EOF'
const axios = require('axios');
const fs = require('fs');
const path = require('path');

class BielikService {
    constructor() {
        this.config = this.loadConfig();
        this.isEnabled = process.env.BIELIK_ENABLED === 'true';
        this.baseURL = process.env.BIELIK_URL || this.config.url;
        this.model = process.env.BIELIK_MODEL || this.config.model;
    }

    loadConfig() {
        try {
            const configPath = path.join(__dirname, '../config/bielik.json');
            return JSON.parse(fs.readFileSync(configPath, 'utf8'));
        } catch (error) {
            console.warn('Bielik config not found, using defaults');
            return {
                model: 'SpeakLeash/bielik-4.5b-v3.0-instruct:Q8_0',
                url: 'http://*************:11434',
                options: { temperature: 0.7, top_p: 0.9, max_tokens: 1000 }
            };
        }
    }

    async analyzeText(text, type = 'general', options = {}) {
        if (!this.isEnabled) {
            throw new Error('Bielik service is disabled');
        }

        try {
            const prompt = this.buildPrompt(text, type);
            const response = await this.callBielik(prompt, options);
            return this.parseResponse(response, type);
        } catch (error) {
            console.error('Bielik analysis failed:', error.message);
            throw error;
        }
    }

    buildPrompt(text, type) {
        const prompts = this.config.hvac_prompts || {};
        const basePrompt = prompts[type] || 'Przeanalizuj następujący tekst:';
        
        return `${basePrompt}

${text}

Odpowiedz w formacie JSON z następującymi polami:
- analiza: szczegółowa analiza tekstu
- kategoria: typ problemu/zapytania
- priorytet: niski/średni/wysoki/krytyczny
- akcje: sugerowane działania
- sentiment: pozytywny/neutralny/negatywny`;
    }

    async callBielik(prompt, options = {}) {
        const requestOptions = {
            ...this.config.options,
            ...options
        };

        const response = await axios.post(`${this.baseURL}/api/generate`, {
            model: this.model,
            prompt: prompt,
            stream: false,
            options: requestOptions
        }, {
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json'
            }
        });

        return response.data;
    }

    parseResponse(response, type) {
        try {
            const text = response.response || '';
            
            // Try to extract JSON from response
            const jsonMatch = text.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
            
            // Fallback to structured text parsing
            return {
                analiza: text,
                kategoria: this.extractCategory(text),
                priorytet: this.extractPriority(text),
                akcje: this.extractActions(text),
                sentiment: this.extractSentiment(text),
                raw_response: text
            };
        } catch (error) {
            console.warn('Failed to parse Bielik response as JSON:', error.message);
            return {
                analiza: response.response || '',
                raw_response: response.response || '',
                error: 'Failed to parse structured response'
            };
        }
    }

    extractCategory(text) {
        const categories = ['klimatyzacja', 'wentylacja', 'pompa_ciepla', 'serwis', 'instalacja'];
        for (const cat of categories) {
            if (text.toLowerCase().includes(cat)) return cat;
        }
        return 'ogólne';
    }

    extractPriority(text) {
        const priorities = ['krytyczny', 'wysoki', 'średni', 'niski'];
        for (const priority of priorities) {
            if (text.toLowerCase().includes(priority)) return priority;
        }
        return 'średni';
    }

    extractActions(text) {
        const actionKeywords = ['sprawdź', 'wymień', 'napraw', 'oczyść', 'skontaktuj'];
        const actions = [];
        for (const keyword of actionKeywords) {
            if (text.toLowerCase().includes(keyword)) {
                actions.push(keyword);
            }
        }
        return actions.length > 0 ? actions : ['analiza_wymagana'];
    }

    extractSentiment(text) {
        const positive = ['zadowolony', 'dziękuję', 'świetnie', 'dobrze'];
        const negative = ['problem', 'awaria', 'nie działa', 'zepsuty', 'pilne'];
        
        const positiveCount = positive.filter(word => text.toLowerCase().includes(word)).length;
        const negativeCount = negative.filter(word => text.toLowerCase().includes(word)).length;
        
        if (negativeCount > positiveCount) return 'negatywny';
        if (positiveCount > negativeCount) return 'pozytywny';
        return 'neutralny';
    }

    async healthCheck() {
        try {
            const response = await axios.get(`${this.baseURL}/api/tags`, { timeout: 5000 });
            const models = response.data.models || [];
            const bielikInstalled = models.some(m => m.name.includes('bielik'));
            
            return {
                status: 'healthy',
                url: this.baseURL,
                model: this.model,
                installed: bielikInstalled,
                enabled: this.isEnabled
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
                url: this.baseURL,
                enabled: this.isEnabled
            };
        }
    }
}

module.exports = new BielikService();
EOF
    
    echo "✅ Bielik service created at backend/services/bielikService.js"
}

# Main execution
main() {
    echo "🚀 Starting Bielik V3 4.5B installation process..."
    echo ""
    
    # Create directories if they don't exist
    mkdir -p ../backend/config
    mkdir -p ../backend/services
    
    # Check if Ollama is accessible
    if ! check_ollama; then
        echo "❌ Cannot proceed without Ollama access"
        echo "💡 Please ensure:"
        echo "   1. Ollama is installed on $OLLAMA_HOST"
        echo "   2. Ollama service is running"
        echo "   3. Port $OLLAMA_PORT is accessible"
        echo "   4. SSH access to $OLLAMA_HOST is configured"
        exit 1
    fi
    
    # Install Bielik model
    echo ""
    read -p "📥 Install Bielik V3 4.5B model? This will download ~4.5GB (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        install_bielik
    else
        echo "⏭️ Skipping model installation"
    fi
    
    # Test Bielik
    echo ""
    if test_bielik; then
        echo "🎉 Bielik is working correctly!"
    else
        echo "⚠️ Bielik test failed, but continuing with configuration..."
    fi
    
    # Create configuration
    echo ""
    create_service_config
    update_ai_service
    
    echo ""
    echo "🎆 BIELIK V3 4.5B INSTALLATION COMPLETED!"
    echo "========================================"
    echo ""
    echo "✅ Next steps:"
    echo "   1. Restart your backend server"
    echo "   2. Test AI features with Polish content"
    echo "   3. Monitor performance and quality"
    echo ""
    echo "🔧 Configuration files created:"
    echo "   - backend/config/bielik.json"
    echo "   - backend/services/bielikService.js"
    echo "   - backend/.env (updated)"
    echo ""
    echo "🇵🇱 Bielik V3 4.5B is ready for Polish HVAC excellence!"
}

# Run main function
main "$@"
