#!/bin/bash

# 🧪 AI MODELS COMPARISON TEST
# Tests Bielik V3 4.5B vs Gemma3:4b vs OpenAI for Polish HVAC analysis

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${PURPLE}🧪 AI MODELS COMPARISON TEST - POLISH HVAC ANALYSIS${NC}"
echo "=================================================================="
echo ""

# Test prompts in Polish
HVAC_PROMPTS=(
    "Klimatyzacja w biurze nie chłodzi, robi dziwne dźwięki i kapie woda. Pilne."
    "Pompa ciepła LG przestała grzać, wyświetla błąd E03. K<PERSON> w Warszawie."
    "Serwis klimatyzacji Daikin - wymiana filtrów i czyszczenie parownika."
    "Instalacja nowej klimatyzacji w mieszkaniu 60m2, 3 pokoje, Mokotów."
    "Awaria wentylacji mechanicznej w biurowcu - brak przepływu powietrza."
)

OLLAMA_URL="http://localhost:11434"
BACKEND_URL="http://localhost:5000"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to test Ollama model
test_ollama_model() {
    local model=$1
    local prompt=$2
    local start_time=$(date +%s%N)
    
    local response=$(curl -s -X POST "$OLLAMA_URL/api/generate" \
        -H "Content-Type: application/json" \
        -d "{
            \"model\": \"$model\",
            \"prompt\": \"Przeanalizuj następujące zgłoszenie HVAC i określ: typ problemu, priorytet (niski/średni/wysoki/krytyczny), sugerowane działania. Zgłoszenie: $prompt\",
            \"stream\": false,
            \"options\": {
                \"temperature\": 0.7,
                \"max_tokens\": 300
            }
        }")
    
    local end_time=$(date +%s%N)
    local duration=$(( (end_time - start_time) / 1000000 )) # Convert to milliseconds
    
    if echo "$response" | jq -e '.response' > /dev/null 2>&1; then
        local analysis=$(echo "$response" | jq -r '.response')
        echo "✅ Model: $model"
        echo "⏱️  Czas: ${duration}ms"
        echo "📝 Analiza:"
        echo "$analysis" | head -5
        echo "..."
        echo ""
        return 0
    else
        echo "❌ Model: $model - BŁĄD"
        echo "Odpowiedź: $response"
        echo ""
        return 1
    fi
}

# Function to test backend AI endpoint
test_backend_ai() {
    local prompt=$1
    local start_time=$(date +%s%N)
    
    local response=$(curl -s -X POST "$BACKEND_URL/api/ai/bielik/analyze" \
        -H "Content-Type: application/json" \
        -d "{
            \"text\": \"$prompt\",
            \"type\": \"issue_classification\"
        }")
    
    local end_time=$(date +%s%N)
    local duration=$(( (end_time - start_time) / 1000000 ))
    
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        local model_used=$(echo "$response" | jq -r '.data.model_used // "unknown"')
        local analysis=$(echo "$response" | jq -r '.data.analiza // "brak analizy"')
        local category=$(echo "$response" | jq -r '.data.kategoria // "brak kategorii"')
        local priority=$(echo "$response" | jq -r '.data.priorytet // "brak priorytetu"')
        
        echo "✅ Backend AI (Model: $model_used)"
        echo "⏱️  Czas: ${duration}ms"
        echo "📂 Kategoria: $category"
        echo "🚨 Priorytet: $priority"
        echo "📝 Analiza: $analysis"
        echo ""
        return 0
    else
        echo "❌ Backend AI - BŁĄD"
        echo "Odpowiedź: $response"
        echo ""
        return 1
    fi
}

# Function to check if models are available
check_models() {
    print_status "Sprawdzanie dostępnych modeli..."
    
    local models_response=$(curl -s "$OLLAMA_URL/api/tags")
    if echo "$models_response" | jq -e '.models' > /dev/null 2>&1; then
        echo "📋 Dostępne modele Ollama:"
        echo "$models_response" | jq -r '.models[] | "  - \(.name)"'
        echo ""
        
        # Check for Bielik
        if echo "$models_response" | jq -r '.models[].name' | grep -q "bielik"; then
            print_success "Bielik V3 4.5B jest dostępny"
        else
            print_warning "Bielik V3 4.5B nie jest dostępny"
        fi
        
        # Check for Gemma
        if echo "$models_response" | jq -r '.models[].name' | grep -q "gemma"; then
            print_success "Gemma3:4b jest dostępny"
        else
            print_warning "Gemma3:4b nie jest dostępny"
        fi
        
        return 0
    else
        print_error "Nie można pobrać listy modeli z Ollama"
        return 1
    fi
}

# Function to run comprehensive test
run_comprehensive_test() {
    local prompt_index=$1
    local prompt="${HVAC_PROMPTS[$prompt_index]}"
    
    echo -e "${YELLOW}🔍 TEST ${prompt_index}: ${prompt}${NC}"
    echo "=================================================================="
    
    # Test Bielik if available
    if curl -s "$OLLAMA_URL/api/tags" | jq -r '.models[].name' | grep -q "bielik"; then
        echo -e "${BLUE}🇵🇱 BIELIK V3 4.5B:${NC}"
        test_ollama_model "SpeakLeash/bielik-4.5b-v3.0-instruct:Q8_0" "$prompt"
    fi
    
    # Test Gemma3
    if curl -s "$OLLAMA_URL/api/tags" | jq -r '.models[].name' | grep -q "gemma"; then
        echo -e "${BLUE}🤖 GEMMA3:4B:${NC}"
        test_ollama_model "gemma3:4b" "$prompt"
    fi
    
    # Test Backend AI (hybrid system)
    echo -e "${BLUE}🔧 BACKEND AI (Hybrid):${NC}"
    test_backend_ai "$prompt"
    
    echo ""
}

# Function to run performance benchmark
run_performance_benchmark() {
    print_status "Uruchamianie benchmarku wydajności..."
    
    local test_prompt="Klimatyzacja nie działa poprawnie"
    local iterations=3
    
    echo "📊 Benchmark (${iterations} iteracji):"
    echo ""
    
    # Benchmark Bielik
    if curl -s "$OLLAMA_URL/api/tags" | jq -r '.models[].name' | grep -q "bielik"; then
        echo "🇵🇱 Bielik V3 4.5B:"
        local total_time=0
        for i in $(seq 1 $iterations); do
            local start_time=$(date +%s%N)
            curl -s -X POST "$OLLAMA_URL/api/generate" \
                -H "Content-Type: application/json" \
                -d "{\"model\": \"SpeakLeash/bielik-4.5b-v3.0-instruct:Q8_0\", \"prompt\": \"$test_prompt\", \"stream\": false}" > /dev/null
            local end_time=$(date +%s%N)
            local duration=$(( (end_time - start_time) / 1000000 ))
            total_time=$((total_time + duration))
            echo "  Iteracja $i: ${duration}ms"
        done
        local avg_time=$((total_time / iterations))
        echo "  Średnia: ${avg_time}ms"
        echo ""
    fi
    
    # Benchmark Gemma3
    if curl -s "$OLLAMA_URL/api/tags" | jq -r '.models[].name' | grep -q "gemma"; then
        echo "🤖 Gemma3:4b:"
        local total_time=0
        for i in $(seq 1 $iterations); do
            local start_time=$(date +%s%N)
            curl -s -X POST "$OLLAMA_URL/api/generate" \
                -H "Content-Type: application/json" \
                -d "{\"model\": \"gemma3:4b\", \"prompt\": \"$test_prompt\", \"stream\": false}" > /dev/null
            local end_time=$(date +%s%N)
            local duration=$(( (end_time - start_time) / 1000000 ))
            total_time=$((total_time + duration))
            echo "  Iteracja $i: ${duration}ms"
        done
        local avg_time=$((total_time / iterations))
        echo "  Średnia: ${avg_time}ms"
        echo ""
    fi
}

# Main function
main() {
    echo -e "${BLUE}🚀 Rozpoczynanie testów porównawczych AI...${NC}"
    echo ""
    
    # Check prerequisites
    if ! command -v jq &> /dev/null; then
        print_error "jq nie jest zainstalowane"
        exit 1
    fi
    
    if ! curl -s "$OLLAMA_URL/api/tags" > /dev/null 2>&1; then
        print_error "Ollama nie jest dostępny na $OLLAMA_URL"
        exit 1
    fi
    
    # Check available models
    check_models
    
    # Run tests for each prompt
    for i in "${!HVAC_PROMPTS[@]}"; do
        run_comprehensive_test $i
        if [ $i -lt $((${#HVAC_PROMPTS[@]} - 1)) ]; then
            echo -e "${YELLOW}Oczekiwanie 3 sekundy przed następnym testem...${NC}"
            sleep 3
        fi
    done
    
    # Performance benchmark
    echo -e "${PURPLE}📊 BENCHMARK WYDAJNOŚCI${NC}"
    echo "=================================================================="
    run_performance_benchmark
    
    # Summary
    echo -e "${GREEN}🎆 TESTY ZAKOŃCZONE!${NC}"
    echo "=================================================================="
    echo ""
    echo "📋 Podsumowanie:"
    echo "   - Przetestowano ${#HVAC_PROMPTS[@]} promptów HVAC"
    echo "   - Porównano dostępne modele AI"
    echo "   - Wykonano benchmark wydajności"
    echo ""
    echo "🔍 Sprawdź wyniki powyżej, aby ocenić:"
    echo "   - Jakość analizy polskiego tekstu"
    echo "   - Dokładność klasyfikacji HVAC"
    echo "   - Wydajność modeli"
    echo ""
    echo -e "${BLUE}🇵🇱 Bielik V3 4.5B - Polski AI dla HVAC Excellence!${NC}"
}

# Run main function
main "$@"
