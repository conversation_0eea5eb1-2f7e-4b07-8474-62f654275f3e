#!/usr/bin/env python3
"""
🚀 FULMARK HVAC CRM - RUNNER SKRYPTU WLEWU DANYCH
Pomocniczy skrypt do uruchamiania wlewu danych z różnymi opcjami

Wielki inżynierze! Użyj tego skryptu do:
- Testowania wlewu (dry run)
- Pełnego wlewu z AI
- Wlewu bez AI (szybszy)
- Analizy danych przed wlewem
"""

import sys
import os
import asyncio
import argparse
from pathlib import Path

# Dodaj ścieżkę do głównego skryptu
sys.path.append(str(Path(__file__).parent))

from data_ingestion_master import DataIngestionMaster, IngestionConfig

def create_parser():
    """Tworzy parser argumentów wiersza poleceń"""
    parser = argparse.ArgumentParser(
        description='🚀 FULMARK HVAC CRM - Master Data Ingestion System',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Przykłady użycia:

🧪 Test run (bez zapisywania do bazy):
    python run_data_ingestion.py --dry-run

⚡ Szybki import (bez AI):
    python run_data_ingestion.py --no-ai

🤖 Pełny import z AI:
    python run_data_ingestion.py --full

🔍 Tylko analiza plików CSV:
    python run_data_ingestion.py --analyze-only

📊 Custom batch size:
    python run_data_ingestion.py --batch-size 50

🎯 Wynik na skali 2137 punktów!
        """
    )
    
    # Główne opcje
    parser.add_argument('--dry-run', action='store_true',
                       help='🧪 Tryb testowy - nie zapisuje danych do bazy')
    
    parser.add_argument('--no-ai', action='store_true',
                       help='⚡ Wyłącz AI enhancement (szybszy import)')
    
    parser.add_argument('--full', action='store_true',
                       help='🤖 Pełny import z AI enhancement')
    
    parser.add_argument('--analyze-only', action='store_true',
                       help='🔍 Tylko analiza plików CSV bez importu')
    
    # Konfiguracja
    parser.add_argument('--batch-size', type=int, default=100,
                       help='📦 Rozmiar batch dla importu (domyślnie: 100)')
    
    parser.add_argument('--data-folder', type=str,
                       default='/home/<USER>/HVAC/unifikacja/Data_to_ingest',
                       help='📁 Ścieżka do folderu z plikami CSV')
    
    parser.add_argument('--mongodb-url', type=str,
                       help='🗄️ URL do MongoDB (domyślnie z .env)')
    
    parser.add_argument('--lm-studio-url', type=str,
                       default='http://192.168.0.179:1234',
                       help='🤖 URL do LM Studio dla AI')
    
    parser.add_argument('--no-backup', action='store_true',
                       help='💾 Pomiń tworzenie backup')
    
    # Debugowanie
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='🔍 Szczegółowe logi')
    
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='🔇 Tylko błędy w logach')
    
    return parser

def setup_logging(verbose=False, quiet=False):
    """Konfiguruje poziom logowania"""
    import logging
    
    if quiet:
        level = logging.ERROR
    elif verbose:
        level = logging.DEBUG
    else:
        level = logging.INFO
    
    logging.getLogger().setLevel(level)

def validate_environment():
    """Sprawdza środowisko przed uruchomieniem"""
    print("🔍 Sprawdzanie środowiska...")
    
    # Sprawdź Python
    if sys.version_info < (3, 8):
        print("❌ Wymagany Python 3.8+")
        return False
    
    # Sprawdź wymagane moduły
    required_modules = ['pandas', 'pymongo', 'requests']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ Brakujące moduły: {', '.join(missing_modules)}")
        print("💡 Zainstaluj: pip install pandas pymongo requests")
        return False
    
    print("✅ Środowisko gotowe!")
    return True

def check_data_files(data_folder):
    """Sprawdza dostępność plików CSV"""
    print(f"📁 Sprawdzanie plików w: {data_folder}")
    
    required_files = [
        'Kartoteka kontrahentów_extracted.csv',
        'clients_export.csv',
        'calendar_archive.csv'
    ]
    
    missing_files = []
    found_files = []
    
    for filename in required_files:
        filepath = os.path.join(data_folder, filename)
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            found_files.append(f"✅ {filename} ({size:,} bytes)")
        else:
            missing_files.append(f"❌ {filename}")
    
    for file_info in found_files:
        print(f"   {file_info}")
    
    for file_info in missing_files:
        print(f"   {file_info}")
    
    if missing_files:
        print("⚠️ Niektóre pliki nie zostały znalezione")
        return False
    
    print("✅ Wszystkie pliki CSV dostępne!")
    return True

async def run_analysis_only(config):
    """Uruchamia tylko analizę plików CSV"""
    print("🔍 TRYB ANALIZY - sprawdzanie plików CSV...")
    
    master = DataIngestionMaster(config)
    await master.initialize()
    await master.analyze_csv_files()
    
    print("📊 Analiza zakończona!")

async def main():
    """Główna funkcja"""
    parser = create_parser()
    args = parser.parse_args()
    
    # Konfiguracja logowania
    setup_logging(args.verbose, args.quiet)
    
    print("🚀 FULMARK HVAC CRM - MASTER DATA INGESTION SYSTEM")
    print("=" * 60)
    
    # Sprawdź środowisko
    if not validate_environment():
        sys.exit(1)
    
    # Sprawdź pliki
    if not check_data_files(args.data_folder):
        if not args.analyze_only:
            print("💡 Użyj --analyze-only aby sprawdzić dostępne pliki")
            sys.exit(1)
    
    # Przygotuj konfigurację
    config = IngestionConfig()
    config.data_folder = args.data_folder
    config.batch_size = args.batch_size
    config.dry_run = args.dry_run
    config.enable_ai_enhancement = not args.no_ai and not args.analyze_only
    config.create_backup = not args.no_backup
    config.lm_studio_url = args.lm_studio_url
    
    if args.mongodb_url:
        config.mongodb_url = args.mongodb_url
    
    # Wyświetl konfigurację
    print(f"📁 Folder danych: {config.data_folder}")
    print(f"📦 Batch size: {config.batch_size}")
    print(f"🧪 Dry run: {'TAK' if config.dry_run else 'NIE'}")
    print(f"🤖 AI Enhancement: {'TAK' if config.enable_ai_enhancement else 'NIE'}")
    print(f"💾 Backup: {'TAK' if config.create_backup else 'NIE'}")
    print("=" * 60)
    
    # Uruchom odpowiedni tryb
    try:
        if args.analyze_only:
            await run_analysis_only(config)
        else:
            master = DataIngestionMaster(config)
            score = await master.run_ingestion()
            
            print("=" * 60)
            print(f"🎯 WYNIK KOŃCOWY: {score}/2137 punktów")
            
            if score > 1800:
                print("🏆 DOSKONAŁY WYNIK! Dane wlane z kosmiczną jakością!")
            elif score > 1500:
                print("🥇 BARDZO DOBRY WYNIK! Misja zakończona sukcesem!")
            elif score > 1000:
                print("🥈 DOBRY WYNIK! Dane zostały pomyślnie wlane!")
            else:
                print("🥉 WYNIK DO POPRAWY. Sprawdź logi błędów.")
    
    except KeyboardInterrupt:
        print("\n⏹️ Proces przerwany przez użytkownika")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Krytyczny błąd: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
