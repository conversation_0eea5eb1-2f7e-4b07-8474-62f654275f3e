import React, { useState, useEffect } from 'react';
import { 
  Card, Row, Col, Statistic, Progress, Alert, Badge, Table, 
  Typography, Tabs, Space, Button, Tooltip, Timeline 
} from 'antd';
import { 
  DashboardOutlined, 
  ThunderboltOutlined, 
  DatabaseOutlined,
  CloudServerOutlined,
  BugOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { Line, Gauge, Liquid } from '@ant-design/plots';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const SystemMonitoring = () => {
  const [loading, setLoading] = useState(false);
  const [systemMetrics, setSystemMetrics] = useState({});
  const [realTimeData, setRealTimeData] = useState([]);
  const [alerts, setAlerts] = useState([]);

  useEffect(() => {
    fetchSystemMetrics();
    
    // Set up real-time updates every 5 seconds
    const interval = setInterval(() => {
      fetchRealTimeMetrics();
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const fetchSystemMetrics = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/analytics/real-time');
      const data = await response.json();
      
      if (data.success) {
        setSystemMetrics(data.result);
        setAlerts(data.result.alerts || []);
      }
    } catch (error) {
      console.error('Error fetching system metrics:', error);
      // Mock data for demo
      setSystemMetrics(getMockSystemMetrics());
      setAlerts(getMockAlerts());
    } finally {
      setLoading(false);
    }
  };

  const fetchRealTimeMetrics = async () => {
    try {
      const response = await fetch('/api/analytics/real-time');
      const data = await response.json();
      
      if (data.success) {
        const newDataPoint = {
          time: new Date().toLocaleTimeString(),
          cpu: data.result.currentMetrics?.cpuLoad || Math.random() * 100,
          memory: data.result.currentMetrics?.memoryUsage || Math.random() * 100,
          responseTime: data.result.currentMetrics?.responseTime || Math.random() * 500
        };
        
        setRealTimeData(prev => [...prev.slice(-19), newDataPoint]);
      }
    } catch (error) {
      // Mock real-time data
      const newDataPoint = {
        time: new Date().toLocaleTimeString(),
        cpu: Math.random() * 100,
        memory: Math.random() * 100,
        responseTime: Math.random() * 500
      };
      setRealTimeData(prev => [...prev.slice(-19), newDataPoint]);
    }
  };

  const getMockSystemMetrics = () => ({
    currentMetrics: {
      activeUsers: 12,
      emailsProcessingQueue: 3,
      transcriptionsInProgress: 1,
      systemLoad: 23.5,
      responseTime: 145,
      cpuLoad: 34.2,
      memoryUsage: 67.8,
      diskUsage: 45.3,
      networkLatency: 12.5
    },
    serviceStatus: {
      emailProcessing: 'healthy',
      transcriptionService: 'healthy',
      database: 'healthy',
      aiServices: 'warning',
      fileStorage: 'healthy'
    },
    performance: {
      avgResponseTime: 145,
      throughput: 1247,
      errorRate: 0.02,
      uptime: 99.97
    }
  });

  const getMockAlerts = () => ([
    {
      id: 1,
      type: 'warning',
      message: 'Wysoka liczba emaili w kolejce przetwarzania',
      timestamp: new Date(Date.now() - 300000), // 5 min ago
      severity: 'medium'
    },
    {
      id: 2,
      type: 'info',
      message: 'Automatyczne backup bazy danych zakończone pomyślnie',
      timestamp: new Date(Date.now() - 1800000), // 30 min ago
      severity: 'low'
    },
    {
      id: 3,
      type: 'error',
      message: 'Błąd połączenia z serwisem AI - przełączono na backup',
      timestamp: new Date(Date.now() - 3600000), // 1h ago
      severity: 'high'
    }
  ]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return 'success';
      case 'warning': return 'warning';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'healthy': return <CheckCircleOutlined />;
      case 'warning': return <ExclamationCircleOutlined />;
      case 'error': return <BugOutlined />;
      default: return <ClockCircleOutlined />;
    }
  };

  const cpuGaugeConfig = {
    percent: (systemMetrics.currentMetrics?.cpuLoad || 34.2) / 100,
    range: {
      color: ['#30BF78', '#FAAD14', '#F4664A'],
    },
    indicator: {
      pointer: {
        style: {
          stroke: '#D0D0D0',
        },
      },
      pin: {
        style: {
          stroke: '#D0D0D0',
        },
      },
    },
    statistic: {
      content: {
        style: {
          fontSize: '36px',
          lineHeight: '36px',
        },
      },
    },
  };

  const memoryLiquidConfig = {
    percent: (systemMetrics.currentMetrics?.memoryUsage || 67.8) / 100,
    outline: {
      border: 4,
      distance: 8,
    },
    wave: {
      length: 128,
    },
  };

  const responseTimeConfig = {
    data: realTimeData,
    xField: 'time',
    yField: 'responseTime',
    smooth: true,
    color: '#1890ff',
    point: {
      size: 3,
      shape: 'circle',
    },
  };

  const serviceStatusColumns = [
    {
      title: 'Serwis',
      dataIndex: 'service',
      key: 'service',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Badge 
          status={getStatusColor(status)} 
          text={status} 
          icon={getStatusIcon(status)}
        />
      ),
    },
    {
      title: 'Ostatnia aktualizacja',
      dataIndex: 'lastUpdate',
      key: 'lastUpdate',
    },
    {
      title: 'Uptime',
      dataIndex: 'uptime',
      key: 'uptime',
    }
  ];

  const serviceStatusData = [
    {
      key: '1',
      service: 'Email Processing',
      status: systemMetrics.serviceStatus?.emailProcessing || 'healthy',
      lastUpdate: '2 min temu',
      uptime: '99.97%'
    },
    {
      key: '2',
      service: 'Transcription Service',
      status: systemMetrics.serviceStatus?.transcriptionService || 'healthy',
      lastUpdate: '1 min temu',
      uptime: '99.95%'
    },
    {
      key: '3',
      service: 'Database',
      status: systemMetrics.serviceStatus?.database || 'healthy',
      lastUpdate: '30 sek temu',
      uptime: '99.99%'
    },
    {
      key: '4',
      service: 'AI Services',
      status: systemMetrics.serviceStatus?.aiServices || 'warning',
      lastUpdate: '5 min temu',
      uptime: '98.45%'
    },
    {
      key: '5',
      service: 'File Storage',
      status: systemMetrics.serviceStatus?.fileStorage || 'healthy',
      lastUpdate: '1 min temu',
      uptime: '99.98%'
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>🖥️ Monitoring Systemu w Czasie Rzeczywistym</Title>
        <Button 
          type="primary" 
          icon={<ReloadOutlined />} 
          onClick={fetchSystemMetrics}
          loading={loading}
        >
          Odśwież
        </Button>
      </div>

      {/* Alerts Section */}
      {alerts.length > 0 && (
        <Alert
          message={`${alerts.length} aktywnych alertów systemowych`}
          description={alerts[0]?.message}
          type={alerts[0]?.type || 'info'}
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      <Tabs defaultActiveKey="overview" size="large">
        <TabPane tab="📊 Przegląd" key="overview">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="Aktywni Użytkownicy"
                  value={systemMetrics.currentMetrics?.activeUsers || 12}
                  prefix={<DashboardOutlined />}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="Czas Odpowiedzi"
                  value={systemMetrics.currentMetrics?.responseTime || 145}
                  suffix="ms"
                  prefix={<ThunderboltOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="Obciążenie Systemu"
                  value={systemMetrics.currentMetrics?.systemLoad || 23.5}
                  suffix="%"
                  prefix={<CloudServerOutlined />}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="Uptime"
                  value={systemMetrics.performance?.uptime || 99.97}
                  suffix="%"
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
            <Col xs={24} lg={8}>
              <Card title="💻 Obciążenie CPU" style={{ height: 300 }}>
                <Gauge {...cpuGaugeConfig} height={200} />
              </Card>
            </Col>
            <Col xs={24} lg={8}>
              <Card title="🧠 Użycie Pamięci" style={{ height: 300 }}>
                <Liquid {...memoryLiquidConfig} height={200} />
              </Card>
            </Col>
            <Col xs={24} lg={8}>
              <Card title="⚡ Czas Odpowiedzi" style={{ height: 300 }}>
                <Line {...responseTimeConfig} height={200} />
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="🔧 Status Serwisów" key="services">
          <Card title="Status Serwisów Systemowych">
            <Table
              dataSource={serviceStatusData}
              columns={serviceStatusColumns}
              pagination={false}
              size="middle"
            />
          </Card>

          <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
            <Col xs={24} lg={12}>
              <Card title="📈 Metryki Wydajności">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text>Średni czas odpowiedzi</Text>
                    <Progress 
                      percent={Math.min((systemMetrics.performance?.avgResponseTime || 145) / 5, 100)} 
                      format={() => `${systemMetrics.performance?.avgResponseTime || 145}ms`}
                    />
                  </div>
                  <div>
                    <Text>Przepustowość</Text>
                    <Progress 
                      percent={Math.min((systemMetrics.performance?.throughput || 1247) / 20, 100)} 
                      format={() => `${systemMetrics.performance?.throughput || 1247} req/min`}
                      strokeColor="#52c41a"
                    />
                  </div>
                  <div>
                    <Text>Wskaźnik błędów</Text>
                    <Progress 
                      percent={(systemMetrics.performance?.errorRate || 0.02) * 100} 
                      format={() => `${((systemMetrics.performance?.errorRate || 0.02) * 100).toFixed(2)}%`}
                      strokeColor="#f5222d"
                    />
                  </div>
                </Space>
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="💾 Zasoby Systemowe">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text>Użycie dysku</Text>
                    <Progress 
                      percent={systemMetrics.currentMetrics?.diskUsage || 45.3} 
                      strokeColor="#1890ff"
                    />
                  </div>
                  <div>
                    <Text>Opóźnienie sieci</Text>
                    <Progress 
                      percent={Math.min((systemMetrics.currentMetrics?.networkLatency || 12.5) * 2, 100)} 
                      format={() => `${systemMetrics.currentMetrics?.networkLatency || 12.5}ms`}
                      strokeColor="#faad14"
                    />
                  </div>
                  <div>
                    <Text>Kolejka przetwarzania</Text>
                    <Progress 
                      percent={Math.min((systemMetrics.currentMetrics?.emailsProcessingQueue || 3) * 10, 100)} 
                      format={() => `${systemMetrics.currentMetrics?.emailsProcessingQueue || 3} emaili`}
                      strokeColor="#722ed1"
                    />
                  </div>
                </Space>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="🚨 Alerty" key="alerts">
          <Card title="Historia Alertów Systemowych">
            <Timeline>
              {alerts.map((alert, index) => (
                <Timeline.Item
                  key={alert.id || index}
                  color={alert.type === 'error' ? 'red' : alert.type === 'warning' ? 'orange' : 'blue'}
                  dot={getStatusIcon(alert.type)}
                >
                  <div>
                    <Text strong>{alert.message}</Text>
                    <br />
                    <Text type="secondary">
                      {alert.timestamp ? new Date(alert.timestamp).toLocaleString() : 'Nieznany czas'}
                    </Text>
                    <br />
                    <Badge 
                      status={alert.severity === 'high' ? 'error' : alert.severity === 'medium' ? 'warning' : 'default'} 
                      text={`Priorytet: ${alert.severity || 'niski'}`} 
                    />
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default SystemMonitoring;
