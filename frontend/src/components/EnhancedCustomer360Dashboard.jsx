/**
 * 📱 ENHANCED CUSTOMER 360° DASHBOARD 2025 - COSMIC LEVEL UX
 * 
 * Kompletny widok klienta z wszystkimi interakcjami i AI insights:
 * - Real-time profile updates
 * - Email & call interaction timeline
 * - Predictive analytics & recommendations
 * - Business owner actionable insights
 * - Time-saving automation alerts
 * 
 * Features:
 * - Interactive customer profile visualization
 * - Real-time sentiment tracking
 * - Predictive maintenance alerts
 * - Upsell opportunity detection
 * - Communication style adaptation
 * - Mobile-responsive design
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Avatar,
  Tag,
  Progress,
  Timeline,
  Statistic,
  Alert,
  Button,
  Tabs,
  Space,
  Divider,
  Badge,
  Tooltip,
  Rate,
  List,
  Typography,
  Spin,
  notification
} from 'antd';
import {
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  HomeOutlined,
  ToolOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  HeartOutlined,
  AlertOutlined,
  TrendingUpOutlined,
  BulbOutlined,
  CalendarOutlined,
  MessageOutlined,
  SettingOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const EnhancedCustomer360Dashboard = ({ customerId, onClose }) => {
  const [loading, setLoading] = useState(true);
  const [customer360, setCustomer360] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [realTimeUpdates, setRealTimeUpdates] = useState(true);

  // Load customer 360° data
  useEffect(() => {
    if (customerId) {
      loadCustomer360Data();
      
      // Setup real-time updates
      if (realTimeUpdates) {
        const interval = setInterval(loadCustomer360Data, 30000); // Update every 30 seconds
        return () => clearInterval(interval);
      }
    }
  }, [customerId, realTimeUpdates]);

  /**
   * 📊 Load Customer 360° data
   */
  const loadCustomer360Data = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/intelligence/customer/${customerId}/360`);
      setCustomer360(response.data.data);
      
      // Show notifications for urgent alerts
      if (response.data.data.predictiveInsights?.alerts?.length > 0) {
        response.data.data.predictiveInsights.alerts.forEach(alert => {
          if (alert.priority === 'high') {
            notification.warning({
              message: 'Uwaga!',
              description: alert.message,
              duration: 10
            });
          }
        });
      }
      
    } catch (error) {
      console.error('Failed to load customer 360° data:', error);
      notification.error({
        message: 'Błąd',
        description: 'Nie udało się załadować danych klienta'
      });
    } finally {
      setLoading(false);
    }
  };

  /**
   * 🎨 Get sentiment color
   */
  const getSentimentColor = (sentiment) => {
    const colors = {
      very_positive: '#52c41a',
      positive: '#73d13d',
      neutral: '#faad14',
      negative: '#ff7875',
      very_negative: '#ff4d4f'
    };
    return colors[sentiment] || '#d9d9d9';
  };

  /**
   * 🏷️ Get risk level tag
   */
  const getRiskTag = (riskScore) => {
    if (riskScore > 0.7) return <Tag color="red">Wysokie Ryzyko</Tag>;
    if (riskScore > 0.4) return <Tag color="orange">Średnie Ryzyko</Tag>;
    return <Tag color="green">Niskie Ryzyko</Tag>;
  };

  /**
   * 💰 Format currency
   */
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('pl-PL', {
      style: 'currency',
      currency: 'PLN'
    }).format(amount);
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>Ładowanie profilu klienta 360°...</p>
      </div>
    );
  }

  if (!customer360) {
    return (
      <Alert
        message="Brak danych"
        description="Nie udało się załadować profilu klienta 360°"
        type="error"
        showIcon
      />
    );
  }

  const { basicInfo, communicationProfile, technicalProfile, businessProfile, 
          behavioralPatterns, predictiveInsights, interactionHistory } = customer360;

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <Avatar size={64} icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />
          <div>
            <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
              {basicInfo?.name || 'Nieznany Klient'}
            </Title>
            <Text type="secondary">{basicInfo?.buildingType} • {basicInfo?.address}</Text>
            <div style={{ marginTop: 8 }}>
              <Space>
                <Badge status="processing" text="Live Updates" />
                {getRiskTag(predictiveInsights?.churnRisk?.score || 0)}
                <Tag color="blue">{businessProfile?.budgetIndicator}</Tag>
              </Space>
            </div>
          </div>
        </div>
        <div>
          <Space>
            <Button 
              type="primary" 
              icon={<MessageOutlined />}
              onClick={() => window.open(`mailto:${basicInfo?.email}`)}
            >
              Wyślij Email
            </Button>
            <Button 
              icon={<PhoneOutlined />}
              onClick={() => window.open(`tel:${basicInfo?.phone}`)}
            >
              Zadzwoń
            </Button>
            <Button onClick={onClose}>Zamknij</Button>
          </Space>
        </div>
      </div>

      {/* Quick Stats */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Satysfakcja"
              value={behavioralPatterns?.loyaltyIndicators?.satisfaction || 4.2}
              suffix="/ 5.0"
              prefix={<HeartOutlined style={{ color: '#52c41a' }} />}
              precision={1}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Wartość LTV"
              value={businessProfile?.lifetimeValue || 15000}
              prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
              formatter={(value) => formatCurrency(value)}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Interakcje"
              value={interactionHistory?.totalInteractions || 0}
              prefix={<MessageOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Ostatni Kontakt"
              value={interactionHistory?.lastInteraction ? 
                new Date(interactionHistory.lastInteraction).toLocaleDateString('pl-PL') : 'Brak'}
              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Urgent Alerts */}
      {predictiveInsights?.nextBestActions?.filter(action => action.priority === 'high').length > 0 && (
        <Alert
          message="Pilne Działania Wymagane!"
          description={
            <List
              size="small"
              dataSource={predictiveInsights.nextBestActions.filter(action => action.priority === 'high')}
              renderItem={action => (
                <List.Item>
                  <AlertOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                  {action.action} - {action.timing}
                </List.Item>
              )}
            />
          }
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      <Tabs activeKey={activeTab} onChange={setActiveTab} size="large">
        {/* Overview Tab */}
        <TabPane 
          tab={
            <span>
              <UserOutlined />
              Przegląd
            </span>
          } 
          key="overview"
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="Profil Komunikacji" extra={<Badge status="processing" text="Live" />}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text strong>Preferowany Kanał:</Text>
                    <Tag color="blue" style={{ marginLeft: 8 }}>
                      {communicationProfile?.preferredChannel || 'email'}
                    </Tag>
                  </div>
                  <div>
                    <Text strong>Styl Komunikacji:</Text>
                    <Tag color="green" style={{ marginLeft: 8 }}>
                      {communicationProfile?.communicationStyle || 'professional'}
                    </Tag>
                  </div>
                  <div>
                    <Text strong>Trend Sentymentu:</Text>
                    <div style={{ marginTop: 8 }}>
                      {communicationProfile?.sentimentTrend?.slice(-5).map((sentiment, index) => (
                        <Tag 
                          key={index}
                          color={getSentimentColor(sentiment.sentiment)}
                          style={{ marginBottom: 4 }}
                        >
                          {sentiment.sentiment} ({Math.round(sentiment.confidence * 100)}%)
                        </Tag>
                      ))}
                    </div>
                  </div>
                </Space>
              </Card>
            </Col>
            
            <Col xs={24} lg={12}>
              <Card title="Profil Techniczny" extra={<ToolOutlined />}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text strong>Wiedza Techniczna:</Text>
                    <Progress 
                      percent={this.getTechnicalKnowledgePercent(technicalProfile?.technicalKnowledge)}
                      strokeColor="#1890ff"
                      style={{ marginLeft: 16, width: '60%' }}
                    />
                  </div>
                  <div>
                    <Text strong>Preferowane Marki:</Text>
                    <div style={{ marginTop: 8 }}>
                      {technicalProfile?.preferredBrands?.map(brand => (
                        <Tag key={brand} color="blue">{brand}</Tag>
                      ))}
                    </div>
                  </div>
                  <div>
                    <Text strong>Świadomość Serwisu:</Text>
                    <Tag 
                      color={technicalProfile?.maintenanceAwareness === 'proactive' ? 'green' : 'orange'}
                      style={{ marginLeft: 8 }}
                    >
                      {technicalProfile?.maintenanceAwareness || 'reactive'}
                    </Tag>
                  </div>
                </Space>
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col xs={24}>
              <Card title="Możliwości Biznesowe" extra={<TrendingUpOutlined />}>
                <Row gutter={[16, 16]}>
                  {predictiveInsights?.upsellOpportunities?.map((opportunity, index) => (
                    <Col xs={24} sm={12} lg={8} key={index}>
                      <Card size="small" style={{ borderLeft: '4px solid #52c41a' }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <div>
                            <Text strong>{opportunity.product}</Text>
                            <div>
                              <Text type="secondary">
                                Prawdopodobieństwo: {Math.round(opportunity.probability * 100)}%
                              </Text>
                            </div>
                            <div>
                              <Text strong style={{ color: '#52c41a' }}>
                                {formatCurrency(opportunity.value)}
                              </Text>
                            </div>
                          </div>
                          <Progress
                            type="circle"
                            percent={Math.round(opportunity.probability * 100)}
                            width={50}
                            strokeColor="#52c41a"
                          />
                        </div>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* Interaction History Tab */}
        <TabPane 
          tab={
            <span>
              <MessageOutlined />
              Historia Interakcji
            </span>
          } 
          key="interactions"
        >
          <Card title="Timeline Interakcji" extra={<Badge count={interactionHistory?.totalInteractions || 0} />}>
            <Timeline>
              {interactionHistory?.recentInteractions?.map((interaction, index) => (
                <Timeline.Item 
                  key={index}
                  color={getSentimentColor(interaction.sentiment)}
                  dot={this.getInteractionIcon(interaction.type)}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <div style={{ flex: 1 }}>
                      <Text strong>{interaction.type.toUpperCase()}</Text>
                      <div style={{ color: '#666', fontSize: '12px', marginBottom: 8 }}>
                        {new Date(interaction.date).toLocaleString('pl-PL')}
                      </div>
                      <Paragraph ellipsis={{ rows: 2, expandable: true }}>
                        {interaction.summary}
                      </Paragraph>
                      <Space>
                        <Tag color={getSentimentColor(interaction.sentiment)}>
                          {interaction.sentiment}
                        </Tag>
                        <Tag>{interaction.outcome}</Tag>
                      </Space>
                    </div>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </Card>
        </TabPane>

        {/* Predictive Analytics Tab */}
        <TabPane 
          tab={
            <span>
              <BulbOutlined />
              Analityka Predykcyjna
            </span>
          } 
          key="analytics"
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="Ryzyko Utraty Klienta" extra={getRiskTag(predictiveInsights?.churnRisk?.score || 0)}>
                <Progress
                  percent={Math.round((predictiveInsights?.churnRisk?.score || 0) * 100)}
                  strokeColor={predictiveInsights?.churnRisk?.score > 0.5 ? '#ff4d4f' : '#52c41a'}
                  style={{ marginBottom: 16 }}
                />
                <Text strong>Czynniki Ryzyka:</Text>
                <List
                  size="small"
                  dataSource={predictiveInsights?.churnRisk?.factors || []}
                  renderItem={factor => <List.Item>{factor}</List.Item>}
                />
                <Divider />
                <Text strong>Rekomendacja:</Text>
                <Paragraph>{predictiveInsights?.churnRisk?.recommendation}</Paragraph>
              </Card>
            </Col>
            
            <Col xs={24} lg={12}>
              <Card title="Potrzeby Serwisowe" extra={<SettingOutlined />}>
                <List
                  dataSource={predictiveInsights?.maintenanceNeeds || []}
                  renderItem={need => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={
                          <Badge 
                            status={need.urgency === 'immediate' ? 'error' : 'processing'} 
                          />
                        }
                        title={need.equipment}
                        description={
                          <div>
                            <div>{need.description}</div>
                            <div style={{ marginTop: 4 }}>
                              <Text strong>{formatCurrency(need.estimatedCost)}</Text>
                              <Text type="secondary" style={{ marginLeft: 8 }}>
                                • {need.urgency}
                              </Text>
                            </div>
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* Next Actions Tab */}
        <TabPane 
          tab={
            <span>
              <CalendarOutlined />
              Następne Kroki
            </span>
          } 
          key="actions"
        >
          <Card title="Rekomendowane Działania" extra={<BulbOutlined />}>
            <List
              dataSource={predictiveInsights?.nextBestActions || []}
              renderItem={action => (
                <List.Item
                  actions={[
                    <Button 
                      type={action.priority === 'high' ? 'primary' : 'default'}
                      size="small"
                    >
                      Wykonaj
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    avatar={
                      <Badge 
                        status={
                          action.priority === 'high' ? 'error' : 
                          action.priority === 'medium' ? 'warning' : 'default'
                        } 
                      />
                    }
                    title={
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <Text strong>{action.action}</Text>
                        <Tag color={
                          action.priority === 'high' ? 'red' : 
                          action.priority === 'medium' ? 'orange' : 'blue'
                        }>
                          {action.priority}
                        </Tag>
                      </div>
                    }
                    description={
                      <div>
                        <div><Text strong>Timing:</Text> {action.timing}</div>
                        <div><Text strong>Oczekiwany Rezultat:</Text> {action.expectedOutcome}</div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );

  // Helper methods
  getTechnicalKnowledgePercent(level) {
    const levels = { basic: 25, intermediate: 50, advanced: 75, expert: 100 };
    return levels[level] || 25;
  }

  getInteractionIcon(type) {
    const icons = {
      email: <MailOutlined />,
      call: <PhoneOutlined />,
      service: <ToolOutlined />,
      visit: <HomeOutlined />
    };
    return icons[type] || <MessageOutlined />;
  }
};

export default EnhancedCustomer360Dashboard;
