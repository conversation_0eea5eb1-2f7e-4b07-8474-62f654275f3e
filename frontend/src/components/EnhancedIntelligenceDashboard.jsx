/**
 * 🚀 ENHANCED INTELLIGENCE DASHBOARD 2025 - COSMIC LEVEL UX
 * 
 * Advanced React component for Email Intelligence & AI insights
 * Wykorzyst<PERSON><PERSON> najnowsze Ant Design 5.14.1 z cosmic-level design
 * 
 * Features:
 * - Real-time email intelligence metrics
 * - Semantic search interface
 * - Customer 360-degree view
 * - AI-powered insights
 * - Interactive charts and analytics
 * - Mobile-responsive design
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Input,
  Button,
  Tag,
  Space,
  Spin,
  Alert,
  Tabs,
  Timeline,
  Avatar,
  Tooltip,
  Badge,
  Divider
} from 'antd';
import {
  MailOutlined,
  BrainOutlined,
  SearchOutlined,
  UserOutlined,
  TrendingUpOutlined,
  AlertOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  HeartOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { Search } = Input;
const { TabPane } = Tabs;

const EnhancedIntelligenceDashboard = () => {
  const [loading, setLoading] = useState(false);
  const [dashboardData, setDashboardData] = useState(null);
  const [searchResults, setSearchResults] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Load dashboard data on component mount
  useEffect(() => {
    loadDashboardData();
  }, []);

  /**
   * 📊 Load dashboard insights
   */
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/intelligence/dashboard');
      setDashboardData(response.data.data);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 🔍 Perform semantic search
   */
  const handleSemanticSearch = async (query) => {
    if (!query.trim()) return;
    
    try {
      setSearchLoading(true);
      const response = await axios.get('/api/intelligence/email/search', {
        params: { query, limit: 10 }
      });
      setSearchResults(response.data.data);
    } catch (error) {
      console.error('Semantic search failed:', error);
    } finally {
      setSearchLoading(false);
    }
  };

  /**
   * 🎨 Get sentiment color
   */
  const getSentimentColor = (sentiment) => {
    const colors = {
      very_positive: '#52c41a',
      positive: '#73d13d',
      neutral: '#faad14',
      negative: '#ff7875',
      very_negative: '#ff4d4f'
    };
    return colors[sentiment] || '#d9d9d9';
  };

  /**
   * 🏷️ Get urgency tag
   */
  const getUrgencyTag = (urgency) => {
    const configs = {
      critical: { color: 'red', icon: <AlertOutlined /> },
      high: { color: 'orange', icon: <ThunderboltOutlined /> },
      medium: { color: 'blue', icon: <ClockCircleOutlined /> },
      low: { color: 'green', icon: <CheckCircleOutlined /> }
    };
    const config = configs[urgency] || configs.medium;
    
    return (
      <Tag color={config.color} icon={config.icon}>
        {urgency?.toUpperCase()}
      </Tag>
    );
  };

  if (loading && !dashboardData) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>Ładowanie Intelligence Dashboard...</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{ marginBottom: 24 }}>
        <h1 style={{ 
          fontSize: '28px', 
          fontWeight: 'bold', 
          color: '#1890ff',
          margin: 0,
          display: 'flex',
          alignItems: 'center',
          gap: '12px'
        }}>
          <BrainOutlined style={{ fontSize: '32px' }} />
          Enhanced Intelligence Dashboard 2025
        </h1>
        <p style={{ color: '#666', margin: '8px 0 0 44px' }}>
          AI-powered email intelligence & customer insights
        </p>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab} size="large">
        {/* Overview Tab */}
        <TabPane 
          tab={
            <span>
              <TrendingUpOutlined />
              Przegląd
            </span>
          } 
          key="overview"
        >
          {dashboardData && (
            <>
              {/* KPI Cards */}
              <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
                <Col xs={24} sm={12} lg={6}>
                  <Card>
                    <Statistic
                      title="Przetworzone Emaile"
                      value={dashboardData.totalEmails}
                      prefix={<MailOutlined style={{ color: '#1890ff' }} />}
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} lg={6}>
                  <Card>
                    <Statistic
                      title="Satysfakcja Klientów"
                      value={dashboardData.customerSatisfaction}
                      suffix="/ 5.0"
                      prefix={<HeartOutlined style={{ color: '#52c41a' }} />}
                      valueStyle={{ color: '#52c41a' }}
                      precision={1}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} lg={6}>
                  <Card>
                    <Statistic
                      title="Czas Odpowiedzi"
                      value={dashboardData.responseTime}
                      prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
                      valueStyle={{ color: '#faad14' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} lg={6}>
                  <Card>
                    <Statistic
                      title="Automatyzacja"
                      value={dashboardData.automationRate}
                      suffix="%"
                      prefix={<ThunderboltOutlined style={{ color: '#722ed1' }} />}
                      valueStyle={{ color: '#722ed1' }}
                    />
                  </Card>
                </Col>
              </Row>

              {/* Charts Row */}
              <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
                <Col xs={24} lg={12}>
                  <Card title="Rozkład Sentymentu" extra={<Badge status="processing" text="Live" />}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>Pozytywny</span>
                        <Progress 
                          percent={dashboardData.sentimentDistribution.positive} 
                          strokeColor="#52c41a"
                          showInfo={false}
                          style={{ flex: 1, margin: '0 16px' }}
                        />
                        <span style={{ fontWeight: 'bold' }}>{dashboardData.sentimentDistribution.positive}%</span>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>Neutralny</span>
                        <Progress 
                          percent={dashboardData.sentimentDistribution.neutral} 
                          strokeColor="#faad14"
                          showInfo={false}
                          style={{ flex: 1, margin: '0 16px' }}
                        />
                        <span style={{ fontWeight: 'bold' }}>{dashboardData.sentimentDistribution.neutral}%</span>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>Negatywny</span>
                        <Progress 
                          percent={dashboardData.sentimentDistribution.negative} 
                          strokeColor="#ff4d4f"
                          showInfo={false}
                          style={{ flex: 1, margin: '0 16px' }}
                        />
                        <span style={{ fontWeight: 'bold' }}>{dashboardData.sentimentDistribution.negative}%</span>
                      </div>
                    </div>
                  </Card>
                </Col>
                <Col xs={24} lg={12}>
                  <Card title="Kategorie Intencji" extra={<Badge status="processing" text="AI-Powered" />}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>Zlecenia Serwisowe</span>
                        <Progress 
                          percent={dashboardData.intentDistribution.service_request} 
                          strokeColor="#1890ff"
                          showInfo={false}
                          style={{ flex: 1, margin: '0 16px' }}
                        />
                        <span style={{ fontWeight: 'bold' }}>{dashboardData.intentDistribution.service_request}%</span>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>Zapytania</span>
                        <Progress 
                          percent={dashboardData.intentDistribution.inquiry} 
                          strokeColor="#52c41a"
                          showInfo={false}
                          style={{ flex: 1, margin: '0 16px' }}
                        />
                        <span style={{ fontWeight: 'bold' }}>{dashboardData.intentDistribution.inquiry}%</span>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>Wyceny</span>
                        <Progress 
                          percent={dashboardData.intentDistribution.quote_request} 
                          strokeColor="#722ed1"
                          showInfo={false}
                          style={{ flex: 1, margin: '0 16px' }}
                        />
                        <span style={{ fontWeight: 'bold' }}>{dashboardData.intentDistribution.quote_request}%</span>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>Reklamacje</span>
                        <Progress 
                          percent={dashboardData.intentDistribution.complaint} 
                          strokeColor="#ff4d4f"
                          showInfo={false}
                          style={{ flex: 1, margin: '0 16px' }}
                        />
                        <span style={{ fontWeight: 'bold' }}>{dashboardData.intentDistribution.complaint}%</span>
                      </div>
                    </div>
                  </Card>
                </Col>
              </Row>

              {/* Top Issues */}
              <Card title="Najczęstsze Problemy HVAC" extra={<Badge count="AI" style={{ backgroundColor: '#52c41a' }} />}>
                <Timeline>
                  {dashboardData.topIssues.map((issue, index) => (
                    <Timeline.Item 
                      key={index}
                      color={index === 0 ? 'red' : index === 1 ? 'orange' : 'blue'}
                    >
                      <strong>{issue}</strong>
                      <div style={{ color: '#666', fontSize: '12px' }}>
                        Priorytet: {index === 0 ? 'Wysoki' : index === 1 ? 'Średni' : 'Normalny'}
                      </div>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </Card>
            </>
          )}
        </TabPane>

        {/* Semantic Search Tab */}
        <TabPane 
          tab={
            <span>
              <SearchOutlined />
              Wyszukiwanie Semantyczne
            </span>
          } 
          key="search"
        >
          <Card title="Inteligentne Wyszukiwanie Emaili" extra={<Badge status="processing" text="Weaviate v3.5.3" />}>
            <Search
              placeholder="Wyszukaj podobne emaile (np. 'problemy z klimatyzacją', 'awaria pompy ciepła')"
              allowClear
              enterButton={
                <Button type="primary" icon={<SearchOutlined />}>
                  Szukaj
                </Button>
              }
              size="large"
              onSearch={handleSemanticSearch}
              loading={searchLoading}
              style={{ marginBottom: 24 }}
            />

            {searchResults.length > 0 && (
              <div>
                <Divider orientation="left">Wyniki Wyszukiwania ({searchResults.length})</Divider>
                <Space direction="vertical" style={{ width: '100%' }} size="middle">
                  {searchResults.map((result, index) => (
                    <Card 
                      key={index}
                      size="small"
                      style={{ 
                        borderLeft: `4px solid ${getSentimentColor(result.sentiment)}`,
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                      }}
                    >
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                        <div style={{ flex: 1 }}>
                          <h4 style={{ margin: '0 0 8px 0', color: '#1890ff' }}>
                            {result.subject}
                          </h4>
                          <p style={{ 
                            margin: '0 0 8px 0', 
                            color: '#666',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical'
                          }}>
                            {result.content}
                          </p>
                          <Space>
                            <Tag color={getSentimentColor(result.sentiment)}>
                              {result.sentiment}
                            </Tag>
                            <span style={{ fontSize: '12px', color: '#999' }}>
                              {new Date(result.timestamp).toLocaleDateString('pl-PL')}
                            </span>
                          </Space>
                        </div>
                        <div style={{ marginLeft: 16 }}>
                          <Tooltip title="Podobieństwo">
                            <Progress
                              type="circle"
                              percent={Math.round(result.similarity * 100)}
                              width={50}
                              strokeColor="#1890ff"
                            />
                          </Tooltip>
                        </div>
                      </div>
                    </Card>
                  ))}
                </Space>
              </div>
            )}
          </Card>
        </TabPane>

        {/* Customer 360 Tab */}
        <TabPane 
          tab={
            <span>
              <UserOutlined />
              Widok 360°
            </span>
          } 
          key="customer360"
        >
          <Alert
            message="Customer 360° View"
            description="Kompleksowy widok klienta z wszystkimi danymi, urządzeniami, emailami, rozmowami, fakturami i insights AI."
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />
          
          <Card title="Wybierz Klienta" loading={loading}>
            <p>Funkcjonalność Customer 360° View będzie dostępna po wybraniu konkretnego klienta z listy.</p>
            <Button type="primary" size="large">
              Wybierz Klienta
            </Button>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default EnhancedIntelligenceDashboard;
