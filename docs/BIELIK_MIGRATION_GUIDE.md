# 🇵🇱 BIELIK V3 4.5B MIGRATION GUIDE

## Przewodnik Migracji na Polski AI dla HVAC CRM

### 📋 Spis Treści
1. [Wprowadzenie](#wprowadzenie)
2. [Wymagania](#wymagania)
3. [Instalacja](#instalacja)
4. [Konfiguracja](#konfiguracja)
5. [Testowanie](#testowanie)
6. [Monitoring](#monitoring)
7. [Rozwiązywanie Problemów](#rozwiązywanie-problemów)

---

## 🎯 Wprowadzenie

Bielik V3 4.5B to przełomowy model AI trenowany specjalnie na polskich danych, oferujący:

- **🇵🇱 Natywne wsparcie polskiego** - Doskonałe zrozumienie polskiej terminologii HVAC
- **⚡ Wysoką wydajność** - 4.5B parametrów z wydajnością modeli 10x większych
- **💰 Oszczędności** - 90%+ redukcja kosztów vs OpenAI API
- **🔒 Prywatność** - Pełna kontrola nad danymi, GDPR compliance
- **🏢 HVAC Specjalizacja** - Optymalizacja dla branży klimatyzacyjnej

---

## 📋 Wymagania

### Sprzęt
- **RAM**: Minimum 6GB (8GB zalecane dla Q8_0)
- **Dysk**: 5GB wolnego miejsca
- **CPU**: 4+ rdzenie (8+ zalecane)
- **Sieć**: Dostęp do *************:11434

### Oprogramowanie
- **Ollama**: Zainstalowany na serwerze *************
- **Node.js**: 18+ (aktualnie używane)
- **SSH**: Dostęp do serwera Ollama
- **curl, jq**: Narzędzia systemowe

---

## 🚀 Instalacja

### Automatyczna Instalacja (Zalecana)

```bash
# 1. Przejdź do katalogu scripts
cd scripts

# 2. Nadaj uprawnienia
chmod +x migrate_to_bielik.sh

# 3. Uruchom migrację
./migrate_to_bielik.sh
```

### Ręczna Instalacja

```bash
# 1. Zainstaluj Bielik na serwerze Ollama
ssh koldbringer@*************
ollama pull SpeakLeash/bielik-4.5b-v3.0-instruct:Q8_0

# 2. Test modelu
ollama run SpeakLeash/bielik-4.5b-v3.0-instruct:Q8_0

# 3. Sprawdź API
curl http://*************:11434/api/tags
```

---

## ⚙️ Konfiguracja

### Zmienne Środowiskowe (.env)

```env
# Bielik V3 4.5B Configuration
BIELIK_URL=http://*************:11434
BIELIK_MODEL=SpeakLeash/bielik-4.5b-v3.0-instruct:Q8_0
BIELIK_ENABLED=true

# AI Model Priority
AI_PRIMARY_MODEL=bielik
AI_FALLBACK_MODEL=openai
AI_TERTIARY_MODEL=lm_studio
```

### Konfiguracja Bielik (backend/config/bielik.json)

```json
{
    "model": "SpeakLeash/bielik-4.5b-v3.0-instruct:Q8_0",
    "url": "http://*************:11434",
    "options": {
        "temperature": 0.7,
        "top_p": 0.9,
        "max_tokens": 1000
    },
    "hvac_prompts": {
        "email_analysis": "Przeanalizuj następujący email HVAC...",
        "transcription_analysis": "Przeanalizuj następującą transkrypcję...",
        "issue_classification": "Sklasyfikuj następujący problem HVAC...",
        "response_generation": "Wygeneruj profesjonalną odpowiedź..."
    }
}
```

---

## 🧪 Testowanie

### Test Podstawowy

```bash
# Test połączenia
curl -s http://*************:11434/api/tags

# Test modelu
curl -X POST http://*************:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "SpeakLeash/bielik-4.5b-v3.0-instruct:Q8_0",
    "prompt": "Przeanalizuj: klimatyzacja nie chłodzi",
    "stream": false
  }'
```

### Test Backend API

```bash
# Health check
curl http://localhost:5000/api/ai/bielik/health

# Test analizy
curl -X POST http://localhost:5000/api/ai/bielik/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Klimatyzacja hałasuje i nie chłodzi",
    "type": "issue_classification"
  }'
```

### Test HVAC Specyficzny

```bash
# Test email analysis
curl -X POST http://localhost:5000/api/ai/bielik/analyze/email \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Dzień dobry, klimatyzacja w biurze przestała chłodzić i robi dziwne dźwięki. Proszę o pilną naprawę."
  }'
```

---

## 📊 Monitoring

### Dashboard React
- **URL**: http://localhost:3000/bielik-dashboard
- **Funkcje**: Status modeli, statystyki, testy

### API Endpoints
- `GET /api/ai/bielik/health` - Status systemu
- `GET /api/ai/bielik/stats` - Statystyki użycia
- `GET /api/ai/bielik/models` - Lista modeli

### Skrypt Monitoringu

```bash
# Uruchom monitoring
./monitor_bielik.sh

# Wynik:
# 🇵🇱 BIELIK V3 4.5B MONITORING
# ==============================
# 📡 Ollama Status: healthy
# 🧠 AI Health: healthy
# 📊 AI Statistics: 45 requests, 42 successes
```

---

## 🔧 Rozwiązywanie Problemów

### Problem: Bielik nie odpowiada

```bash
# Sprawdź status Ollama
ssh koldbringer@*************
systemctl status ollama

# Restart Ollama
sudo systemctl restart ollama

# Sprawdź logi
journalctl -u ollama -f
```

### Problem: Model nie załadowany

```bash
# Lista modeli
ollama list

# Ponowne pobranie
ollama pull SpeakLeash/bielik-4.5b-v3.0-instruct:Q8_0

# Test modelu
ollama run SpeakLeash/bielik-4.5b-v3.0-instruct:Q8_0
```

### Problem: Błędy API

```bash
# Sprawdź logi backend
tail -f backend/logs/info-$(date +%Y-%m-%d).log

# Test health check
curl -v http://localhost:5000/api/ai/bielik/health

# Restart backend
cd backend && npm restart
```

### Rollback do Poprzedniej Konfiguracji

```bash
# Przywróć backup
cp backup_*/env.backup backend/.env
cp -r backup_*/services_backup/* backend/services/

# Restart backend
cd backend && npm restart
```

---

## 📈 Oczekiwane Korzyści

### Jakość Analizy
- **40-60%** lepsza analiza polskich emaili
- **50-70%** dokładniejszy sentiment analysis
- **90%+** rozpoznawanie polskiej terminologii HVAC

### Wydajność
- **30-40%** szybsze przetwarzanie vs OpenAI API
- **<2s** średni czas odpowiedzi
- **99.9%** dostępność (local deployment)

### Koszty
- **90%+** redukcja kosztów AI
- **Brak limitów** API calls
- **Pełna kontrola** nad infrastrukturą

---

## 🎯 Następne Kroki

1. **Fine-tuning**: Dostosowanie modelu do danych Fulmark
2. **Optymalizacja**: Tuning parametrów dla HVAC
3. **Integracja**: Połączenie z wszystkimi modułami CRM
4. **Monitoring**: Rozszerzenie systemu monitoringu

---

## 📞 Wsparcie

W przypadku problemów:
1. Sprawdź logi: `tail -f backend/logs/*.log`
2. Uruchom monitoring: `./monitor_bielik.sh`
3. Test health check: `curl localhost:5000/api/ai/bielik/health`

**🇵🇱 Bielik V3 4.5B - Polski AI dla HVAC Excellence!**
