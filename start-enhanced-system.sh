#!/bin/bash

# 🚀 ENHANCED FULMARK HVAC CRM SYSTEM STARTUP 2025
# 
# Uruchamia kompletny system z najnowszymi technologiami:
# - Backend Node.js z Enhanced AI Services
# - Frontend React z Cosmic-level UX
# - Weaviate v3.5.5 Vector Database
# - Enhanced Email Intelligence
# - Real-time monitoring

echo "🚀 Starting Enhanced FULMARK HVAC CRM System 2025..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${PURPLE}🔥 $1${NC}"
}

# Check if required tools are installed
check_requirements() {
    print_header "Checking System Requirements..."
    
    # Check Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_status "Node.js found: $NODE_VERSION"
    else
        print_error "Node.js not found! Please install Node.js 18+"
        exit 1
    fi
    
    # Check npm
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        print_status "npm found: $NPM_VERSION"
    else
        print_error "npm not found!"
        exit 1
    fi
    
    # Check Docker (optional for Weaviate)
    if command -v docker &> /dev/null; then
        print_status "Docker found - Weaviate can be started"
    else
        print_warning "Docker not found - Weaviate needs to be running separately"
    fi
}

# Start Weaviate if Docker is available
start_weaviate() {
    print_header "Starting Weaviate Vector Database..."
    
    if command -v docker &> /dev/null; then
        # Check if Weaviate is already running
        if docker ps | grep -q weaviate; then
            print_status "Weaviate is already running"
        else
            print_info "Starting Weaviate container..."
            docker run -d \
                --name weaviate \
                -p 8080:8080 \
                -p 50051:50051 \
                -e QUERY_DEFAULTS_LIMIT=25 \
                -e AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED=true \
                -e PERSISTENCE_DATA_PATH='/var/lib/weaviate' \
                -e DEFAULT_VECTORIZER_MODULE='text2vec-openai' \
                -e ENABLE_MODULES='text2vec-openai,generative-openai' \
                -e CLUSTER_HOSTNAME='node1' \
                semitechnologies/weaviate:1.30.0
            
            print_status "Weaviate started on port 8080"
        fi
    else
        print_warning "Please ensure Weaviate is running on localhost:8080"
    fi
}

# Install backend dependencies
setup_backend() {
    print_header "Setting up Backend..."
    
    cd backend
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_info "Installing backend dependencies..."
        npm install
    else
        print_status "Backend dependencies already installed"
    fi
    
    # Check for .env file
    if [ ! -f ".env" ]; then
        print_warning "Creating .env file from template..."
        cat > .env << EOF
# Enhanced FULMARK HVAC CRM Environment Variables 2025
NODE_ENV=development
PORT=5000

# Database
DATABASE_URL=mongodb://localhost:27017/fulmark-hvac-crm

# OpenAI API (Required for AI features)
OPENAI_API_KEY=your_openai_api_key_here

# Weaviate Configuration
WEAVIATE_HOST=localhost
WEAVIATE_PORT=8080
WEAVIATE_GRPC_HOST=localhost
WEAVIATE_GRPC_PORT=50051

# Email Configuration
DOLORES_EMAIL=<EMAIL>
DOLORES_PASSWORD=Blaeritipol1
GRZEGORZ_EMAIL=<EMAIL>
GRZEGORZ_PASSWORD=Blaeritipol1

# Redis (Optional)
REDIS_URL=redis://localhost:6379

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key-here

# File Upload
MAX_FILE_SIZE=10485760
EOF
        print_warning "Please update .env file with your API keys!"
    else
        print_status "Environment file exists"
    fi
    
    cd ..
}

# Install frontend dependencies
setup_frontend() {
    print_header "Setting up Frontend..."
    
    cd frontend
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_info "Installing frontend dependencies..."
        npm install
    else
        print_status "Frontend dependencies already installed"
    fi
    
    cd ..
}

# Start backend server
start_backend() {
    print_header "Starting Backend Server..."
    
    cd backend
    
    # Start backend in background
    print_info "Starting Node.js backend on port 5000..."
    npm run dev &
    BACKEND_PID=$!
    
    # Wait a moment for server to start
    sleep 3
    
    # Check if backend is running
    if curl -s http://localhost:5000/api/health &> /dev/null; then
        print_status "Backend server is running on http://localhost:5000"
    else
        print_warning "Backend server may still be starting..."
    fi
    
    cd ..
}

# Start frontend server
start_frontend() {
    print_header "Starting Frontend Server..."
    
    cd frontend
    
    # Start frontend in background
    print_info "Starting React frontend on port 3002..."
    npm run dev &
    FRONTEND_PID=$!
    
    # Wait a moment for server to start
    sleep 5
    
    # Check if frontend is running
    if curl -s http://localhost:3002 &> /dev/null; then
        print_status "Frontend server is running on http://localhost:3002"
    else
        print_warning "Frontend server may still be starting..."
    fi
    
    cd ..
}

# Display system status
show_status() {
    print_header "System Status"
    echo ""
    echo -e "${CYAN}🌐 Frontend:${NC} http://localhost:3002"
    echo -e "${CYAN}🔧 Backend API:${NC} http://localhost:5000"
    echo -e "${CYAN}🧠 Weaviate:${NC} http://localhost:8080"
    echo -e "${CYAN}📊 Intelligence API:${NC} http://localhost:5000/api/intelligence"
    echo ""
    echo -e "${GREEN}🎉 Enhanced FULMARK HVAC CRM System is ready!${NC}"
    echo ""
    echo -e "${YELLOW}📝 Next Steps:${NC}"
    echo "1. Update .env file with your OpenAI API key"
    echo "2. Open http://localhost:3002 in your browser"
    echo "3. Navigate to Enhanced Intelligence Dashboard"
    echo "4. Start processing emails and enjoy AI-powered insights!"
    echo ""
    echo -e "${PURPLE}🚀 Welcome to the future of HVAC CRM!${NC}"
}

# Cleanup function
cleanup() {
    print_header "Shutting down system..."
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
        print_status "Backend server stopped"
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
        print_status "Frontend server stopped"
    fi
    
    print_status "System shutdown complete"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Main execution
main() {
    print_header "Enhanced FULMARK HVAC CRM System 2025"
    echo ""
    
    check_requirements
    echo ""
    
    start_weaviate
    echo ""
    
    setup_backend
    echo ""
    
    setup_frontend
    echo ""
    
    start_backend
    echo ""
    
    start_frontend
    echo ""
    
    show_status
    
    # Keep script running
    print_info "Press Ctrl+C to stop all services"
    wait
}

# Run main function
main
