#!/usr/bin/env python3
"""
🚀 SIMPLIFIED EMAIL INTELLIGENCE 2025 - COSMIC LEVEL AUTOMATION
Uproszczona wersja dla szybkiego uruchomienia systemu
"""

import os
import sys
from datetime import datetime
from typing import Dict, Any, List
import asyncio
import json

from fastapi import FastAPI, HTTPException, BackgroundTasks, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
from dotenv import load_dotenv
from transcription_integration import TranscriptionIntegration

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="HVAC Email Intelligence API",
    description="Simplified Email Intelligence for HVAC CRM System",
    version="2025.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class EmailData(BaseModel):
    subject: str
    content: str
    sender: str = ""
    recipient: str = ""
    timestamp: str = ""

class EmailAnalysisResponse(BaseModel):
    success: bool
    analysis: Dict[str, Any]
    message: str
    timestamp: str

class HealthResponse(BaseModel):
    status: str
    version: str
    timestamp: str
    services: Dict[str, str]

class TranscriptionRequest(BaseModel):
    call_metadata: Dict[str, Any] = {}

class TranscriptionAnalysisResponse(BaseModel):
    success: bool
    analysis: Dict[str, Any]
    transcription: str = ""
    message: str
    timestamp: str

# Simple in-memory storage for demo
email_storage = []
analysis_cache = {}

def analyze_email_simple(email_data: EmailData) -> Dict[str, Any]:
    """
    Simplified email analysis without external AI dependencies
    """
    content = email_data.content.lower()
    subject = email_data.subject.lower()
    
    # Simple keyword-based analysis
    hvac_keywords = [
        'klimatyzacja', 'klimatyzator', 'pompa ciepła', 'wentylacja',
        'serwis', 'naprawa', 'awaria', 'montaż', 'instalacja',
        'daikin', 'lg', 'mitsubishi', 'carrier', 'toshiba'
    ]
    
    urgency_keywords = ['pilne', 'awaria', 'nie działa', 'emergency', 'natychmiast']
    positive_keywords = ['dziękuję', 'świetnie', 'doskonale', 'polecam', 'zadowolony']
    negative_keywords = ['problem', 'awaria', 'nie działa', 'źle', 'niezadowolony']
    
    # Calculate scores
    hvac_score = sum(1 for keyword in hvac_keywords if keyword in content or keyword in subject)
    urgency_score = sum(1 for keyword in urgency_keywords if keyword in content or keyword in subject)
    positive_score = sum(1 for keyword in positive_keywords if keyword in content)
    negative_score = sum(1 for keyword in negative_keywords if keyword in content)
    
    # Determine sentiment
    if positive_score > negative_score:
        sentiment = "positive"
        sentiment_score = 0.7 + (positive_score * 0.1)
    elif negative_score > positive_score:
        sentiment = "negative"
        sentiment_score = 0.3 - (negative_score * 0.1)
    else:
        sentiment = "neutral"
        sentiment_score = 0.5
    
    # Determine urgency
    if urgency_score > 0:
        urgency = "high"
    elif hvac_score > 2:
        urgency = "medium"
    else:
        urgency = "low"
    
    # Determine category
    if any(word in content for word in ['awaria', 'nie działa', 'problem']):
        category = "service_request"
    elif any(word in content for word in ['montaż', 'instalacja', 'nowy']):
        category = "installation_inquiry"
    elif any(word in content for word in ['serwis', 'przegląd', 'konserwacja']):
        category = "maintenance"
    elif any(word in content for word in ['oferta', 'wycena', 'cena']):
        category = "quote_request"
    else:
        category = "general_inquiry"
    
    return {
        "sentiment": sentiment,
        "sentiment_score": min(max(sentiment_score, 0.0), 1.0),
        "urgency": urgency,
        "category": category,
        "hvac_relevance": min(hvac_score / 5.0, 1.0),
        "key_topics": [kw for kw in hvac_keywords if kw in content or kw in subject][:5],
        "requires_immediate_attention": urgency_score > 0,
        "analysis_timestamp": datetime.now().isoformat(),
        "confidence": 0.8  # Static confidence for simplified version
    }

@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint"""
    return {
        "message": "HVAC Email Intelligence API - Simplified Version",
        "version": "2025.1.0",
        "status": "operational"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        version="2025.1.0",
        timestamp=datetime.now().isoformat(),
        services={
            "email_analysis": "operational",
            "storage": "operational",
            "api": "operational"
        }
    )

@app.post("/api/email/analyze", response_model=EmailAnalysisResponse)
async def analyze_email(email_data: EmailData):
    """
    Analyze single email
    """
    try:
        # Validate input
        if not email_data.subject and not email_data.content:
            raise HTTPException(
                status_code=400,
                detail="Email must have either subject or content"
            )
        
        # Perform analysis
        analysis = analyze_email_simple(email_data)
        
        # Store for future reference
        email_record = {
            "id": len(email_storage) + 1,
            "email_data": email_data.dict(),
            "analysis": analysis,
            "processed_at": datetime.now().isoformat()
        }
        email_storage.append(email_record)
        
        return EmailAnalysisResponse(
            success=True,
            analysis=analysis,
            message="Email analyzed successfully",
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Email analysis failed: {str(e)}"
        )

@app.post("/api/email/batch")
async def analyze_email_batch(emails: List[EmailData]):
    """
    Batch email analysis
    """
    try:
        if len(emails) > 50:
            raise HTTPException(
                status_code=400,
                detail="Maximum 50 emails per batch"
            )
        
        results = []
        success_count = 0
        
        for email in emails:
            try:
                analysis = analyze_email_simple(email)
                results.append({
                    "email": email.dict(),
                    "analysis": analysis,
                    "success": True
                })
                success_count += 1
            except Exception as e:
                results.append({
                    "email": email.dict(),
                    "error": str(e),
                    "success": False
                })
        
        success_rate = (success_count / len(emails)) * 100
        
        return {
            "success": True,
            "results": results,
            "summary": {
                "total": len(emails),
                "successful": success_count,
                "failed": len(emails) - success_count,
                "success_rate": success_rate
            },
            "message": f"Batch processing completed: {success_rate:.1f}% success rate",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Batch processing failed: {str(e)}"
        )

@app.get("/api/email/history")
async def get_email_history(limit: int = 10):
    """
    Get email analysis history
    """
    try:
        # Return last N emails
        recent_emails = email_storage[-limit:] if len(email_storage) > limit else email_storage
        
        return {
            "success": True,
            "data": recent_emails,
            "total": len(email_storage),
            "returned": len(recent_emails),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve history: {str(e)}"
        )

@app.get("/api/dashboard/insights")
async def get_dashboard_insights():
    """
    Get dashboard insights
    """
    try:
        if not email_storage:
            return {
                "success": True,
                "data": {
                    "total_emails": 0,
                    "categories": {},
                    "sentiment_distribution": {},
                    "urgency_distribution": {},
                    "message": "No emails processed yet"
                },
                "timestamp": datetime.now().isoformat()
            }
        
        # Calculate insights
        total_emails = len(email_storage)
        categories = {}
        sentiments = {}
        urgencies = {}
        
        for record in email_storage:
            analysis = record["analysis"]
            
            # Count categories
            category = analysis.get("category", "unknown")
            categories[category] = categories.get(category, 0) + 1
            
            # Count sentiments
            sentiment = analysis.get("sentiment", "unknown")
            sentiments[sentiment] = sentiments.get(sentiment, 0) + 1
            
            # Count urgencies
            urgency = analysis.get("urgency", "unknown")
            urgencies[urgency] = urgencies.get(urgency, 0) + 1
        
        return {
            "success": True,
            "data": {
                "total_emails": total_emails,
                "categories": categories,
                "sentiment_distribution": sentiments,
                "urgency_distribution": urgencies,
                "high_priority_count": urgencies.get("high", 0),
                "negative_sentiment_count": sentiments.get("negative", 0)
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate insights: {str(e)}"
        )

@app.post("/api/transcription/analyze", response_model=TranscriptionAnalysisResponse)
async def analyze_transcription_file(
    audio: UploadFile = File(...),
    call_metadata: str = "{}"
):
    """
    🎙️ Analyze M4A transcription file
    """
    try:
        # Validate file type
        if not audio.filename.lower().endswith(('.m4a', '.mp3', '.wav', '.mp4')):
            raise HTTPException(
                status_code=400,
                detail="Unsupported file type. Please upload M4A, MP3, WAV, or MP4 file."
            )
        
        # Parse metadata
        try:
            metadata = json.loads(call_metadata) if call_metadata != "{}" else {}
        except json.JSONDecodeError:
            metadata = {}
        
        # Read audio file
        audio_data = await audio.read()
        
        if len(audio_data) == 0:
            raise HTTPException(
                status_code=400,
                detail="Empty audio file"
            )
        
        # Process with transcription integration
        async with TranscriptionIntegration() as integration:
            # Check transcription service health
            health = await integration.check_transcription_health()
            if not health.get("transcription_available", False):
                raise HTTPException(
                    status_code=503,
                    detail="Transcription service unavailable"
                )
            
            # Transcribe audio
            transcription_result = await integration.transcribe_audio(
                audio_data, 
                audio.filename
            )
            
            if not transcription_result.get("success", False):
                raise HTTPException(
                    status_code=500,
                    detail=f"Transcription failed: {transcription_result.get('error', 'Unknown error')}"
                )
            
            transcription_text = transcription_result.get("transcription", "")
            
            if not transcription_text.strip():
                return TranscriptionAnalysisResponse(
                    success=False,
                    analysis={},
                    transcription="",
                    message="No speech detected in audio file",
                    timestamp=datetime.now().isoformat()
                )
            
            # Analyze transcribed call
            analysis_result = await integration.analyze_transcribed_call(
                transcription_text,
                {
                    **metadata,
                    "filename": audio.filename,
                    "file_size": len(audio_data),
                    "transcription_confidence": transcription_result.get("confidence", 0.0),
                    "transcription_duration": transcription_result.get("duration", 0.0)
                }
            )
            
            if not analysis_result.get("success", False):
                raise HTTPException(
                    status_code=500,
                    detail=f"Analysis failed: {analysis_result.get('error', 'Unknown error')}"
                )
            
            # Store for future reference
            transcription_record = {
                "id": len(email_storage) + 1000,  # Different ID range for transcriptions
                "type": "transcription",
                "filename": audio.filename,
                "transcription": transcription_text,
                "analysis": analysis_result["analysis"],
                "metadata": analysis_result["metadata"],
                "processed_at": datetime.now().isoformat()
            }
            email_storage.append(transcription_record)
            
            return TranscriptionAnalysisResponse(
                success=True,
                analysis=analysis_result["analysis"],
                transcription=transcription_text,
                message="Transcription and analysis completed successfully",
                timestamp=datetime.now().isoformat()
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Transcription processing failed: {str(e)}"
        )

@app.get("/api/transcription/health")
async def check_transcription_health():
    """
    🔍 Check transcription service health
    """
    try:
        async with TranscriptionIntegration() as integration:
            health = await integration.check_transcription_health()
            return {
                "success": True,
                "transcription_service": health,
                "timestamp": datetime.now().isoformat()
            }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

if __name__ == "__main__":
    print("🚀 Starting HVAC Email Intelligence API - Simplified Version")
    print("📧 Email analysis ready")
    print("🔗 API Documentation: http://localhost:8001/docs")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8001,
        log_level="info"
    )