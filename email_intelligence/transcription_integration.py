#!/usr/bin/env python3
"""
🎙️ TRANSCRIPTION INTEGRATION 2025 - COSMIC LEVEL AUTOMATION
Integracja Email Intelligence z Transcription System
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class TranscriptionIntegration:
    """
    🎙️ Integracja z systemem transkrypcji
    """
    
    def __init__(self, transcription_url: str = "http://localhost:8889"):
        self.transcription_url = transcription_url
        self.session = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def check_transcription_health(self) -> Dict[str, Any]:
        """
        🔍 Sprawdź status systemu transkrypcji
        """
        try:
            async with self.session.get(f"{self.transcription_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "status": "healthy",
                        "transcription_available": True,
                        "models_loaded": data.get("models_loaded", []),
                        "gpu_available": data.get("gpu_available", False),
                        "mode": data.get("mode", "unknown")
                    }
                else:
                    return {
                        "status": "unhealthy",
                        "transcription_available": False,
                        "error": f"HTTP {response.status}"
                    }
        except Exception as e:
            logger.error(f"Transcription health check failed: {e}")
            return {
                "status": "error",
                "transcription_available": False,
                "error": str(e)
            }
    
    async def transcribe_audio(self, audio_data: bytes, filename: str = "audio.m4a") -> Dict[str, Any]:
        """
        🎙️ Transkrypcja pliku audio
        """
        try:
            # Prepare multipart form data
            data = aiohttp.FormData()
            data.add_field('audio', audio_data, filename=filename, content_type='audio/m4a')
            data.add_field('language', 'pl')  # Polish language
            data.add_field('model', 'whisper')  # Use Whisper model
            
            async with self.session.post(
                f"{self.transcription_url}/transcribe",
                data=data,
                timeout=aiohttp.ClientTimeout(total=300)  # 5 minutes timeout
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    return {
                        "success": True,
                        "transcription": result.get("transcription", ""),
                        "confidence": result.get("confidence", 0.0),
                        "language": result.get("language", "pl"),
                        "duration": result.get("duration", 0.0),
                        "processing_time": result.get("processing_time", 0.0),
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"Transcription failed: HTTP {response.status}",
                        "details": error_text,
                        "timestamp": datetime.now().isoformat()
                    }
                    
        except asyncio.TimeoutError:
            return {
                "success": False,
                "error": "Transcription timeout (5 minutes)",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Transcription error: {e}")
            return {
                "success": False,
                "error": f"Transcription error: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    async def analyze_transcribed_call(self, transcription: str, call_metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        📞 Analiza transkrypcji rozmowy telefonicznej
        """
        if not transcription.strip():
            return {
                "success": False,
                "error": "Empty transcription",
                "timestamp": datetime.now().isoformat()
            }
        
        # Simple analysis based on HVAC keywords
        content = transcription.lower()
        
        # HVAC-specific analysis
        hvac_keywords = [
            'klimatyzacja', 'klimatyzator', 'pompa ciepła', 'wentylacja',
            'serwis', 'naprawa', 'awaria', 'montaż', 'instalacja',
            'daikin', 'lg', 'mitsubishi', 'carrier', 'toshiba'
        ]
        
        urgency_keywords = ['pilne', 'awaria', 'nie działa', 'emergency', 'natychmiast', 'zimno', 'gorąco']
        positive_keywords = ['dziękuję', 'świetnie', 'doskonale', 'polecam', 'zadowolony']
        negative_keywords = ['problem', 'awaria', 'nie działa', 'źle', 'niezadowolony', 'reklamacja']
        
        # Calculate scores
        hvac_score = sum(1 for keyword in hvac_keywords if keyword in content)
        urgency_score = sum(1 for keyword in urgency_keywords if keyword in content)
        positive_score = sum(1 for keyword in positive_keywords if keyword in content)
        negative_score = sum(1 for keyword in negative_keywords if keyword in content)
        
        # Determine call type
        if any(word in content for word in ['awaria', 'nie działa', 'problem']):
            call_type = "service_request"
            priority = "high" if urgency_score > 0 else "medium"
        elif any(word in content for word in ['montaż', 'instalacja', 'nowy']):
            call_type = "installation_inquiry"
            priority = "medium"
        elif any(word in content for word in ['serwis', 'przegląd', 'konserwacja']):
            call_type = "maintenance"
            priority = "low"
        elif any(word in content for word in ['oferta', 'wycena', 'cena']):
            call_type = "quote_request"
            priority = "medium"
        else:
            call_type = "general_inquiry"
            priority = "low"
        
        # Sentiment analysis
        if positive_score > negative_score:
            sentiment = "positive"
            sentiment_score = 0.7 + (positive_score * 0.1)
        elif negative_score > positive_score:
            sentiment = "negative"
            sentiment_score = 0.3 - (negative_score * 0.1)
        else:
            sentiment = "neutral"
            sentiment_score = 0.5
        
        # Extract potential customer info
        customer_info = self._extract_customer_info(transcription)
        
        return {
            "success": True,
            "analysis": {
                "call_type": call_type,
                "priority": priority,
                "sentiment": sentiment,
                "sentiment_score": min(max(sentiment_score, 0.0), 1.0),
                "hvac_relevance": min(hvac_score / 5.0, 1.0),
                "urgency_level": urgency_score,
                "key_topics": [kw for kw in hvac_keywords if kw in content][:5],
                "customer_info": customer_info,
                "requires_followup": urgency_score > 0 or call_type in ["service_request", "quote_request"],
                "estimated_value": self._estimate_call_value(call_type, content),
                "next_actions": self._suggest_next_actions(call_type, priority, sentiment)
            },
            "metadata": {
                "transcription_length": len(transcription),
                "analysis_timestamp": datetime.now().isoformat(),
                "confidence": 0.85,
                **(call_metadata or {})
            }
        }
    
    def _extract_customer_info(self, transcription: str) -> Dict[str, Any]:
        """
        👤 Wyciągnij informacje o kliencie z transkrypcji
        """
        import re
        
        # Simple regex patterns for Polish data
        phone_pattern = r'(\+48\s?)?(\d{3}[\s-]?\d{3}[\s-]?\d{3})'
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        
        phones = re.findall(phone_pattern, transcription)
        emails = re.findall(email_pattern, transcription)
        
        # Extract potential names (very basic)
        name_indicators = ['nazywam się', 'jestem', 'mówi', 'to jest']
        potential_names = []
        
        for indicator in name_indicators:
            if indicator in transcription.lower():
                # Simple name extraction after indicator
                parts = transcription.lower().split(indicator)
                if len(parts) > 1:
                    words = parts[1].strip().split()[:2]  # Take first 2 words
                    if words:
                        potential_names.append(' '.join(words))
        
        return {
            "phones": [f"{p[0]}{p[1]}" for p in phones] if phones else [],
            "emails": emails,
            "potential_names": potential_names[:3],  # Limit to 3 potential names
            "has_contact_info": bool(phones or emails)
        }
    
    def _estimate_call_value(self, call_type: str, content: str) -> Dict[str, Any]:
        """
        💰 Oszacuj wartość rozmowy
        """
        value_indicators = {
            "service_request": {"min": 200, "max": 1000, "avg": 400},
            "installation_inquiry": {"min": 3000, "max": 15000, "avg": 8000},
            "maintenance": {"min": 150, "max": 500, "avg": 250},
            "quote_request": {"min": 1000, "max": 10000, "avg": 4000},
            "general_inquiry": {"min": 0, "max": 500, "avg": 100}
        }
        
        base_value = value_indicators.get(call_type, value_indicators["general_inquiry"])
        
        # Adjust based on content
        multiplier = 1.0
        if any(word in content.lower() for word in ['duży', 'wielki', 'cały dom', 'biuro']):
            multiplier *= 1.5
        if any(word in content.lower() for word in ['pilne', 'natychmiast']):
            multiplier *= 1.2
        if any(word in content.lower() for word in ['budżet', 'tani', 'najtaniej']):
            multiplier *= 0.8
        
        return {
            "estimated_min": int(base_value["min"] * multiplier),
            "estimated_max": int(base_value["max"] * multiplier),
            "estimated_avg": int(base_value["avg"] * multiplier),
            "confidence": 0.6,
            "factors": {
                "call_type": call_type,
                "size_multiplier": multiplier,
                "urgency_bonus": "pilne" in content.lower() or "natychmiast" in content.lower()
            }
        }
    
    def _suggest_next_actions(self, call_type: str, priority: str, sentiment: str) -> list:
        """
        📋 Zasugeruj następne kroki
        """
        actions = []
        
        if call_type == "service_request":
            actions.append("Skontaktować się z klientem w ciągu 2 godzin")
            actions.append("Sprawdzić dostępność technika")
            if priority == "high":
                actions.append("Priorytetowe traktowanie - awaria")
        
        elif call_type == "installation_inquiry":
            actions.append("Umówić wizytę techniczną")
            actions.append("Przygotować wstępną wycenę")
            actions.append("Wysłać materiały informacyjne")
        
        elif call_type == "quote_request":
            actions.append("Przygotować szczegółową ofertę")
            actions.append("Skontaktować się w ciągu 24 godzin")
        
        elif call_type == "maintenance":
            actions.append("Sprawdzić harmonogram serwisu")
            actions.append("Umówić termin przeglądu")
        
        if sentiment == "negative":
            actions.append("Priorytetowe traktowanie - niezadowolony klient")
            actions.append("Rozważyć eskalację do managera")
        
        return actions

# Test function
async def test_transcription_integration():
    """
    🧪 Test integracji z systemem transkrypcji
    """
    async with TranscriptionIntegration() as integration:
        # Test health check
        health = await integration.check_transcription_health()
        print("🔍 Transcription Health:", json.dumps(health, indent=2, ensure_ascii=False))
        
        # Test analysis with sample transcription
        sample_transcription = """
        Dzień dobry, nazywam się Jan Kowalski. Mam problem z klimatyzacją Daikin w biurze. 
        Urządzenie nie działa od wczoraj i jest bardzo gorąco. To pilne, proszę o szybki kontakt.
        Mój telefon to 123 456 789, email <EMAIL>.
        """
        
        analysis = await integration.analyze_transcribed_call(
            sample_transcription,
            {"call_duration": 120, "caller_number": "+48123456789"}
        )
        print("📞 Call Analysis:", json.dumps(analysis, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    asyncio.run(test_transcription_integration())