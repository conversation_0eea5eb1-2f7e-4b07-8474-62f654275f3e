#!/usr/bin/env python3
# Test podstawowych funkcji wlewu danych

import asyncio
from data_ingestion_master import DataIngestionMaster, IngestionConfig

async def test_basic_functionality():
    print("🧪 Test podstawowych funkcji...")
    
    config = IngestionConfig()
    config.dry_run = True  # Tylko test, bez zapisywania
    config.enable_ai_enhancement = False  # Szybszy test
    
    master = DataIngestionMaster(config)
    
    try:
        await master.initialize()
        print("✅ Inicjalizacja OK")
        
        await master.analyze_csv_files()
        print("✅ Analiza CSV OK")
        
        print("🎯 Test zakończony sukcesem!")
        return True
        
    except Exception as e:
        print(f"❌ Test nieudany: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_basic_functionality())
    exit(0 if success else 1)
